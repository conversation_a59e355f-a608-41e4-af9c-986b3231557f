# LinkMaster UI Pattern - Quick Reference

## 🚀 **Quick Start Template**

Copy this template for any new page:

```erb
<% content_for :title, "[Page Title] - LinkMaster" %>

<!-- Smooth scroll behavior -->
<style>
  html { scroll-behavior: smooth; }
  .animate-pulse-delay {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    animation-delay: 1s;
  }
</style>

<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-slate-900 via-[COLOR]-900 to-slate-900 overflow-hidden">
  <!-- Background pattern -->
  <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cpattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"%3E%3Cpath d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\"100%25\" height=\"100%25\" fill=\"url(%23grid)\"/%3E%3C/svg%3E')] opacity-20"></div>
  
  <!-- Floating orbs -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-[COLOR]-500 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-72 h-72 bg-[COLOR2]-500 rounded-full filter blur-3xl opacity-30 animate-pulse-delay"></div>
  </div>

  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to root_path, class: "text-white/60 hover:text-white transition-colors" do %>
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
          <% end %>
        </li>
        <!-- Add more breadcrumb items -->
      </ol>
    </nav>

    <!-- Header -->
    <div class="text-center">
      <div class="inline-flex items-center space-x-3 mb-6">
        <div class="w-16 h-16 bg-gradient-to-br from-[COLOR]-600 to-[COLOR2]-600 rounded-2xl flex items-center justify-center shadow-xl">
          <!-- Icon SVG -->
        </div>
      </div>
      <h1 class="text-5xl font-bold text-white mb-4">[Page Title]</h1>
      <p class="text-xl text-white/80 mb-2">[Subtitle]</p>
    </div>
  </div>
</div>

<!-- Content Section -->
<div class="bg-gray-50 py-16">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Your content here -->
  </div>
</div>

<!-- Footer -->
<div class="text-center mt-16 pt-8 border-t border-gray-200">
  <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
    <span>[Status message]</span>
    <div class="w-2 h-2 bg-[COLOR]-500 rounded-full animate-pulse"></div>
    <span>[Status text]</span>
  </div>
</div>

<!-- Floating Back to Top -->
<div class="fixed bottom-8 right-8 z-50">
  <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="w-12 h-12 bg-gradient-to-r from-[COLOR]-600 to-[COLOR2]-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
    </svg>
  </button>
</div>
```

---

## 🎨 **Color Themes by Page Type**

| Page Type | Primary | Secondary | Usage |
|-----------|---------|-----------|-------|
| **Legal** | `purple` | `pink` | Terms, Privacy |
| **Imports** | `indigo` | `blue` | Bulk imports list |
| **Upload** | `green` | `emerald` | New import, forms |
| **Analytics** | `blue` | `cyan` | Dashboard, stats |
| **Settings** | `orange` | `yellow` | Configuration |
| **Errors** | `red` | `pink` | Error pages |

---

## 🧩 **Common Components**

### **Stat Card**
```erb
<div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
  <div class="flex items-center">
    <div class="w-12 h-12 bg-gradient-to-br from-[COLOR]-500 to-[COLOR]-600 rounded-xl flex items-center justify-center mr-4">
      <!-- Icon -->
    </div>
    <div>
      <p class="text-sm text-gray-600">[Label]</p>
      <p class="text-2xl font-bold text-gray-900">[Value]</p>
    </div>
  </div>
</div>
```

### **Content Card**
```erb
<div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-[COLOR]-50 to-[COLOR2]-50">
    <h2 class="text-lg font-semibold text-gray-900">[Title]</h2>
  </div>
  <div class="p-6">
    <!-- Content -->
  </div>
</div>
```

### **Primary Button**
```erb
<%= link_to path, class: "inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-[COLOR]-600 to-[COLOR2]-600 text-white rounded-xl font-semibold hover:from-[COLOR]-700 hover:to-[COLOR2]-700 transition-all duration-300 transform hover:scale-105 shadow-lg" do %>
  <!-- Icon -->
  [Text]
<% end %>
```

### **Secondary Button**
```erb
<%= link_to path, class: "inline-flex items-center justify-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl font-semibold hover:bg-white/20 transition-all duration-300" do %>
  <!-- Icon -->
  [Text]
<% end %>
```

### **Status Badge**
```erb
<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[COLOR]-100 text-[COLOR]-800">
  <!-- Icon -->
  [Status]
</span>
```

---

## 📐 **Spacing Standards**

| Element | Class | Usage |
|---------|-------|-------|
| **Section** | `py-16` | Main content sections |
| **Card** | `p-6` or `p-8` | Card content padding |
| **Button** | `px-6 py-3` | Standard buttons |
| **Large Button** | `px-8 py-4` | Primary CTAs |
| **Grid Gap** | `gap-6` | Standard grid spacing |
| **Margin** | `mb-8` | Section spacing |

---

## 🎯 **Typography Quick Reference**

| Element | Classes | Usage |
|---------|---------|-------|
| **Page Title** | `text-5xl font-bold text-white` | Hero h1 |
| **Section Title** | `text-2xl font-bold text-gray-900` | Section h2 |
| **Card Title** | `text-lg font-semibold text-gray-900` | Card headers |
| **Body Text** | `text-gray-700 leading-relaxed` | Regular content |
| **Small Text** | `text-sm text-gray-600` | Descriptions |
| **Tiny Text** | `text-xs text-gray-500` | Captions |

---

## ⚡ **Animation Classes**

```css
/* Add to your CSS */
.animate-pulse-delay {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 1s;
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}
```

---

## 📱 **Responsive Patterns**

| Layout | Classes | Usage |
|--------|---------|-------|
| **2 Column** | `grid grid-cols-1 md:grid-cols-2 gap-6` | Cards, forms |
| **3 Column** | `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6` | Stats, features |
| **4 Column** | `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6` | Small cards |
| **Flex Stack** | `flex flex-col sm:flex-row gap-4` | Buttons, actions |

---

## ✅ **Implementation Checklist**

For each new page:

- [ ] Choose appropriate color theme
- [ ] Add hero section with gradient background
- [ ] Include breadcrumb navigation
- [ ] Add floating orbs with animations
- [ ] Use consistent card styling
- [ ] Add hover effects and transitions
- [ ] Include floating back-to-top button
- [ ] Add simple footer with status
- [ ] Test responsive design
- [ ] Verify accessibility

---

## 🔗 **Examples**

**Implemented Pages:**
- ✅ Terms of Service (`purple/pink` theme)
- ✅ Privacy Policy (`blue/cyan` theme)  
- ✅ Bulk Imports Index (`indigo/blue` theme)
- ✅ Bulk Imports New (`green/emerald` theme)

**Next to Implement:**
- [ ] Bulk Imports Show page
- [ ] Dashboard pages
- [ ] Settings pages
- [ ] Link management pages

---

**Remember**: Consistency is key! Always refer back to this guide when implementing new pages to maintain our premium, professional design standard.
