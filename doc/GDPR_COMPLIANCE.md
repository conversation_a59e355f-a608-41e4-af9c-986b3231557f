# GDPR Compliance and Privacy Implementation

This document outlines the privacy and GDPR compliance measures implemented in LinkMaster.

## Overview

LinkMaster implements comprehensive privacy protections to comply with GDPR, CCPA, and other privacy regulations. The system follows privacy-by-design principles and provides users with control over their data.

## Key Privacy Features

### 1. IP Address Anonymization

**Implementation**: All IP addresses are automatically anonymized before storage.

- **IPv4**: Last octet is zeroed out (e.g., `*************` → `***********`)
- **IPv6**: Only first 48 bits preserved (e.g., `2001:db8:85a3::1234` → `2001:db8:85a3::`)

**Benefits**:
- Maintains geographic accuracy for analytics
- Protects individual user privacy
- Complies with GDPR's pseudonymization requirements

### 2. Do Not Track (DNT) Compliance

**Implementation**: Automatically respects DNT headers from user browsers.

- When DNT is enabled, no tracking data is collected
- Only basic attribution data (UTM parameters) is stored
- `dnt_enabled` flag tracks compliance status

### 3. Data Retention and Automatic Cleanup

**Default Retention Period**: 13 months (configurable)

**Automatic Cleanup Process**:
- Daily cleanup job removes expired data
- Configurable retention periods per data type
- Batch processing to minimize database impact
- Comprehensive logging and monitoring

**Cleanup Scope**:
- Link click analytics data
- Old bulk import records
- Expired background job records

### 4. Geolocation Privacy

**Implementation**: Location data is collected at city/region level only.

- Original IP used for accurate geolocation lookup
- Only anonymized IP stored in database
- No precise location coordinates stored
- Can be completely disabled via configuration

## Configuration Options

### Environment Variables

```bash
# Data retention period in months (default: 13)
DATA_RETENTION_PERIOD=13

# Enable/disable geolocation lookup (default: true)
ENABLE_GEOLOCATION=true

# Require cookie consent banner (default: true)
REQUIRE_COOKIE_CONSENT=true

# Enable notifications for data cleanup (default: false)
NOTIFY_DATA_CLEANUP=false

# Minimal analytics mode - store only essential data (default: false)
MINIMAL_ANALYTICS=false
```

### Rails Configuration

```ruby
# config/initializers/privacy_settings.rb
Rails.application.configure do
  config.data_retention_period = 13.months
  config.anonymize_ips = true
  config.respect_dnt = true
  config.enable_geolocation = true
  config.require_cookie_consent = true
  config.minimal_analytics = false
end
```

## Database Schema

### Privacy Fields in `link_clicks` Table

```sql
-- Tracks if Do Not Track was enabled for this click
dnt_enabled BOOLEAN DEFAULT false NOT NULL

-- When this record should be automatically deleted
retention_expires_at TIMESTAMP

-- Indexes for efficient cleanup and privacy queries
INDEX index_link_clicks_on_dnt_enabled
INDEX index_link_clicks_on_retention_expires_at
INDEX index_link_clicks_on_clicked_at_for_cleanup
```

### JSONB Indexes for Analytics

```sql
-- Efficient queries on anonymized tracking data
INDEX index_link_clicks_on_country_code ON ((tracking_data->>'country_code'))
INDEX index_link_clicks_on_device_type ON ((tracking_data->>'device_type'))
INDEX index_link_clicks_on_bot_flag ON ((tracking_data->>'bot'))
INDEX index_link_clicks_on_anonymized_ip ON ((tracking_data->>'ip_address'))
```

## Data Processing Safeguards

### 1. Attribution Tracking Service

**Privacy Protections**:
```ruby
# Automatic IP anonymization
data[:ip_address] = anonymize_ip(original_ip)

# DNT compliance
return {} if respect_dnt?

# Retention expiry calculation
retention_expires_at: calculate_retention_expiry
```

### 2. Link Click Model

**Privacy Helper Methods**:
```ruby
# Check if record is safe for analytics use
def privacy_safe?
  !dnt_enabled && !retention_expired?
end

# Get anonymized IP address
def anonymized_ip
  tracking_data['ip_address']
end

# Calculate days until automatic deletion
def days_until_expiry
  ((retention_expires_at - Time.current) / 1.day).ceil
end
```

### 3. Privacy-Aware Scopes

```ruby
# Only include privacy-compliant data in analytics
scope :privacy_compliant, -> { where(dnt_enabled: false) }

# Find data ready for cleanup
scope :expired_for_retention, -> { where('retention_expires_at < ?', Time.current) }

# Exclude empty tracking data from queries
scope :with_tracking_data, -> { where.not(tracking_data: {}) }
```

## Automated Data Cleanup

### DataRetentionCleanupJob

**Purpose**: Automatically remove expired personal data to comply with retention policies.

**Schedule**: Daily at 2 AM UTC (configurable)

**Process**:
1. Identify data older than retention period
2. Process in batches to minimize database load
3. Delete expired link clicks and associated data
4. Clean up old bulk import records
5. Remove old background job records
6. Log cleanup statistics
7. Optional administrator notifications

**Monitoring**:
```ruby
# Cleanup statistics returned
{
  link_clicks_deleted: 1250,
  bulk_imports_deleted: 5,
  old_jobs_deleted: 150
}
```

## User Rights and Data Access

### Right to Access
- Users can view all their data through the dashboard
- API endpoints provide programmatic access
- Export functionality for data portability

### Right to Deletion
- Users can delete individual links and associated analytics
- Account deletion removes all associated data
- Bulk data export before deletion available

### Right to Rectification
- Users can update link metadata and settings
- Incorrect attribution data can be corrected
- Profile information is user-editable

### Right to Portability
- CSV export of all user data
- API access for programmatic data retrieval
- Standard data formats for easy migration

## Legal Basis for Processing

### Legitimate Interest
- Analytics data for service improvement
- Security monitoring and fraud prevention
- Performance optimization

### Consent
- Optional enhanced analytics features
- Marketing communications
- Third-party integrations

### Contract Performance
- Link shortening service provision
- User account management
- Billing and subscription management

## Data Security Measures

### Technical Safeguards
- IP address anonymization by default
- Encrypted data transmission (HTTPS)
- Secure database storage
- Regular automated backups
- Access logging and monitoring

### Organizational Safeguards
- Privacy by design principles
- Regular security audits
- Staff privacy training
- Incident response procedures
- Data processing agreements

## Compliance Monitoring

### Automated Checks
- Daily data retention compliance
- Privacy setting validation
- DNT header respect verification
- IP anonymization testing

### Regular Audits
- Quarterly privacy compliance review
- Annual security assessment
- Third-party penetration testing
- Data processing impact assessments

## Incident Response

### Data Breach Procedures
1. Immediate containment and assessment
2. Risk evaluation and impact analysis
3. User notification within 72 hours
4. Regulatory authority notification
5. Corrective actions and prevention measures

### Privacy Violation Response
1. Investigation and root cause analysis
2. Immediate corrective action
3. User notification and remediation
4. Process improvements
5. Staff retraining if necessary

## Testing and Validation

### Automated Testing
- IP anonymization correctness
- DNT compliance verification
- Data retention policy enforcement
- Privacy-safe analytics queries

### Manual Testing
- Privacy settings functionality
- Data export accuracy
- Deletion request processing
- Consent management system

## Documentation and Records

### Privacy Records Maintained
- Data processing activities register
- Privacy impact assessments
- User consent records
- Data breach incident logs
- Third-party processor agreements

### Regular Updates
- Policy review and updates
- Regulatory change compliance
- User communication updates
- Staff training materials

## Contact Information

### Data Protection Officer
- Email: <EMAIL>
- Response time: 72 hours maximum
- Languages: English, Spanish, French

### User Rights Requests
- Email: <EMAIL>
- Online form: Available in user dashboard
- Processing time: 30 days maximum
- Free of charge for reasonable requests

---

**Last Updated**: January 2025  
**Next Review**: July 2025  
**Version**: 1.0