# Rack::Attack Configuration

This document describes the rate limiting and security policies implemented using Rack::Attack for LinkMaster.

## Overview

Rack::Attack provides middleware-level request filtering and rate limiting to protect against:
- Brute force attacks
- API abuse
- DDoS attempts
- Web scraping
- Malicious requests

## Rate Limiting Policies

### General Requests
- **Limit**: 300 requests per 5 minutes per IP
- **Applies to**: All requests except assets
- **Purpose**: Prevent general abuse and DoS attempts

### Authentication
- **Login attempts by IP**: 5 attempts per 20 seconds
- **Login attempts by email**: 5 attempts per 20 seconds per email
- **Registration**: 3 registrations per hour per IP
- **Password reset**: 5 requests per hour per IP

### Link Management
- **Authenticated users**: 100 link creations per hour per user
- **Unauthenticated users**: 10 link creations per hour per IP
- **Bulk imports**: 5 bulk imports per hour per user

### API Access
- **With API token**: 1,000 requests per hour per token
- **Without token**: 100 requests per hour per IP

### Short Link Redirects
- **Limit**: 1,000 redirects per 5 minutes per IP
- **Purpose**: Prevent scraping and abuse while allowing normal usage

## Security Policies

### Blocked Requests
- Malicious bot user agents (excluding legitimate crawlers)
- Requests with paths longer than 255 characters
- Requests with SQL injection patterns in parameters
- Requests with XSS patterns in parameters
- Requests with user agents longer than 500 characters

### Allowed Requests
- Requests from localhost (127.0.0.1, ::1)
- Health check endpoints (/up, /health)
- Legitimate search engine bots (Google, Bing, etc.)

## Response Format

### Rate Limited (429)
```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again later.",
  "retry_after": 300
}
```

Headers:
- `Retry-After`: Seconds until rate limit resets
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Requests remaining (0 when throttled)
- `X-RateLimit-Reset`: Unix timestamp when limit resets

### Blocked (403)
```json
{
  "error": "Forbidden",
  "message": "Your request has been blocked."
}
```

## Monitoring

### Logging
All throttled and blocked requests are logged to Rails logger with:
- Request IP address
- Request path
- Matching rule name

### Metrics (if StatsD available)
- `linkmaster.rack_attack.throttled`: Counter of throttled requests
- `linkmaster.rack_attack.blocked`: Counter of blocked requests

## Cache Storage

Uses Rails cache store (Solid Cache in production) to maintain rate limiting counters and blocklist data.

## Configuration Management

### Environment Variables
No environment variables required - configuration is embedded in the initializer.

### Customization
To modify limits, edit `/config/initializers/rack_attack.rb`:

1. Find the relevant throttle rule
2. Adjust `limit` and `period` parameters
3. Restart the application

### Adding IP to Blocklist
To permanently block specific IPs, add them to the `block bad ips` rule:

```ruby
blocklist('block bad ips') do |request|
  ['*******', '*******'].include?(request.ip)
end
```

## Testing

Run the Rack::Attack test suite:
```bash
bundle exec rspec spec/requests/rack_attack_spec.rb
```

## Performance Impact

- Minimal latency overhead (< 1ms per request)
- Memory usage scales with number of unique IPs/tokens
- Cache storage requirements: ~1KB per unique identifier per hour

## Troubleshooting

### Users Reporting Rate Limiting
1. Check logs for their IP address
2. Verify their usage patterns
3. Consider safelisting legitimate high-volume users
4. Adjust limits if necessary

### High False Positive Rate
1. Review blocked request logs
2. Adjust user agent patterns in blocklist
3. Consider more lenient limits for specific endpoints

### Performance Issues
1. Monitor cache hit rates
2. Consider using dedicated cache store
3. Implement cache cleanup for old entries