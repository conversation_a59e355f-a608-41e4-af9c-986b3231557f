module SeoHelper
  # Default SEO configuration for Linklysis
  DEFAULT_TITLE = "Linklysis - Professional Link Shortening & Analytics Platform"
  DEFAULT_DESCRIPTION = "Transform your links into powerful marketing tools with Linklysis. Get detailed analytics, custom domains, QR codes, and team collaboration features. Trusted by businesses worldwide."
  DEFAULT_KEYWORDS = %w[
    link shortener url shortener custom domains analytics
    click tracking QR codes team collaboration marketing tools
    bitly alternative rebrandly alternative link management
    linklysis link analytics business intelligence
  ].freeze
  DEFAULT_IMAGE = "/assets/linklysis-og-image.png"
  SITE_NAME = "Linklysis"
  TWITTER_HANDLE = "@linklysis"
  DOMAIN = "linklysis.com"

  # Generate complete SEO meta tags
  def seo_meta_tags(options = {})
    config = seo_config(options)

    tags = []

    # Basic meta tags
    tags << tag.meta(name: "description", content: config[:description])
    tags << tag.meta(name: "keywords", content: config[:keywords].join(", "))
    tags << tag.meta(name: "author", content: "Linklysis")
    tags << tag.meta(name: "robots", content: config[:robots])

    # Canonical URL
    tags << tag.link(rel: "canonical", href: config[:canonical_url]) if config[:canonical_url]

    # Open Graph tags
    tags << tag.meta(property: "og:title", content: config[:title])
    tags << tag.meta(property: "og:description", content: config[:description])
    tags << tag.meta(property: "og:image", content: config[:image])
    tags << tag.meta(property: "og:url", content: config[:canonical_url] || request.original_url)
    tags << tag.meta(property: "og:type", content: config[:type])
    tags << tag.meta(property: "og:site_name", content: SITE_NAME)
    tags << tag.meta(property: "og:locale", content: "en_US")

    # Twitter Card tags
    tags << tag.meta(name: "twitter:card", content: config[:twitter_card])
    tags << tag.meta(name: "twitter:site", content: TWITTER_HANDLE)
    tags << tag.meta(name: "twitter:creator", content: TWITTER_HANDLE)
    tags << tag.meta(name: "twitter:title", content: config[:title])
    tags << tag.meta(name: "twitter:description", content: config[:description])
    tags << tag.meta(name: "twitter:image", content: config[:image])

    # Additional meta tags
    tags << tag.meta(name: "theme-color", content: "#7c3aed") # Purple brand color
    tags << tag.meta(name: "msapplication-TileColor", content: "#7c3aed")

    tags.join("\n").html_safe
  end

  # Generate JSON-LD structured data
  def structured_data(type, data = {})
    base_data = {
      "@context" => "https://schema.org",
      "@type" => type
    }

    case type
    when "Organization"
      schema = organization_schema(data)
    when "WebSite"
      schema = website_schema(data)
    when "SoftwareApplication"
      schema = software_application_schema(data)
    when "Article"
      schema = article_schema(data)
    when "BreadcrumbList"
      schema = breadcrumb_schema(data)
    else
      schema = data
    end

    content_tag :script, type: "application/ld+json" do
      base_data.merge(schema).to_json.html_safe
    end
  end

  # Set page title with proper formatting
  def page_title(title = nil)
    if title.present?
      content_for :title, "#{title} | #{SITE_NAME}"
      title
    else
      content_for :title, DEFAULT_TITLE
      DEFAULT_TITLE
    end
  end

  # Page-specific SEO configurations
  def page_seo_config(page_name)
    case page_name.to_s
    when "home"
      {
        title: "Linklysis - Professional Link Shortening & Analytics Platform",
        description: "Transform your links into powerful marketing tools with Linklysis. Get detailed analytics, custom domains, QR codes, and team collaboration features. Start free today!",
        keywords: %w[link shortener url shortener custom domains analytics click tracking QR codes team collaboration marketing tools bitly alternative]
      }
    when "pricing"
      {
        title: "Pricing Plans - Linklysis",
        description: "Choose the perfect plan for your link management needs. Free plan available with 1,000 links/month. Professional and Business plans with advanced features.",
        keywords: %w[link shortener pricing plans free professional business custom domains analytics]
      }
    when "features"
      {
        title: "Features - Advanced Link Management | Linklysis",
        description: "Discover powerful features: custom domains, detailed analytics, QR codes, team collaboration, API access, and more. See why businesses choose Linklysis.",
        keywords: %w[link shortener features custom domains analytics QR codes team collaboration API webhooks]
      }
    when "about"
      {
        title: "About Us - Linklysis",
        description: "Learn about Linklysis, the modern link shortening platform built for businesses. Discover our mission to transform links into powerful marketing tools.",
        keywords: %w[about linklysis company mission link shortening platform business]
      }
    when "contact"
      {
        title: "Contact Us - Linklysis Support",
        description: "Get in touch with the Linklysis team. We're here to help with your link management needs. Contact support or sales for assistance.",
        keywords: %w[contact support help linklysis customer service sales]
      }
    when "blog"
      {
        title: "Blog - Link Marketing Tips & Insights | Linklysis",
        description: "Discover the latest tips, strategies, and insights for link marketing, analytics, and digital marketing. Expert advice from the Linklysis team.",
        keywords: %w[blog link marketing tips analytics insights digital marketing strategies]
      }
    else
      {
        title: DEFAULT_TITLE,
        description: DEFAULT_DESCRIPTION,
        keywords: DEFAULT_KEYWORDS
      }
    end
  end

  # Generate breadcrumb navigation
  def breadcrumbs(*crumbs)
    return if crumbs.empty?

    breadcrumb_items = crumbs.map.with_index do |crumb, index|
      {
        "@type" => "ListItem",
        "position" => index + 1,
        "name" => crumb[:name],
        "item" => crumb[:url] ? "https://#{DOMAIN}#{crumb[:url]}" : nil
      }.compact
    end

    structured_data("BreadcrumbList", { "itemListElement" => breadcrumb_items })
  end

  # Generate hreflang tags for internationalization
  def hreflang_tags(locales = {})
    tags = []
    locales.each do |locale, url|
      tags << tag.link(rel: "alternate", hreflang: locale, href: url)
    end
    tags.join("\n").html_safe
  end

  # Generate meta tags for social sharing
  def social_share_tags(url, title, description, image = nil)
    tags = []

    # Facebook/Open Graph
    tags << tag.meta(property: "og:url", content: url)
    tags << tag.meta(property: "og:title", content: title)
    tags << tag.meta(property: "og:description", content: description)
    tags << tag.meta(property: "og:image", content: image) if image

    # Twitter
    tags << tag.meta(name: "twitter:url", content: url)
    tags << tag.meta(name: "twitter:title", content: title)
    tags << tag.meta(name: "twitter:description", content: description)
    tags << tag.meta(name: "twitter:image", content: image) if image

    tags.join("\n").html_safe
  end

  private

  def seo_config(options)
    {
      title: options[:title] || content_for(:title) || DEFAULT_TITLE,
      description: options[:description] || DEFAULT_DESCRIPTION,
      keywords: options[:keywords] || DEFAULT_KEYWORDS,
      image: options[:image] || asset_url(DEFAULT_IMAGE),
      canonical_url: options[:canonical_url],
      type: options[:type] || "website",
      robots: options[:robots] || "index, follow",
      twitter_card: options[:twitter_card] || "summary_large_image"
    }
  end

  def organization_schema(data)
    {
      "name" => SITE_NAME,
      "url" => "https://#{DOMAIN}",
      "logo" => asset_url("/assets/linklysis-logo.png"),
      "description" => DEFAULT_DESCRIPTION,
      "foundingDate" => "2024",
      "sameAs" => [
        "https://twitter.com/linklysis",
        "https://linkedin.com/company/linklysis",
        "https://github.com/linklysis"
      ]
    }.merge(data)
  end

  def website_schema(data)
    {
      "name" => SITE_NAME,
      "url" => "https://#{DOMAIN}",
      "description" => DEFAULT_DESCRIPTION,
      "potentialAction" => {
        "@type" => "SearchAction",
        "target" => "https://#{DOMAIN}/search?q={search_term_string}",
        "query-input" => "required name=search_term_string"
      }
    }.merge(data)
  end

  def software_application_schema(data)
    {
      "name" => SITE_NAME,
      "applicationCategory" => "BusinessApplication",
      "operatingSystem" => "Web",
      "description" => DEFAULT_DESCRIPTION,
      "url" => "https://#{DOMAIN}",
      "screenshot" => asset_url("/assets/linklysis-screenshot.png"),
      "offers" => [
        {
          "@type" => "Offer",
          "name" => "Free Plan",
          "price" => "0",
          "priceCurrency" => "USD",
          "description" => "1,000 links per month with basic analytics"
        },
        {
          "@type" => "Offer",
          "name" => "Professional Plan",
          "price" => "29",
          "priceCurrency" => "USD",
          "description" => "10,000 links with API access and custom domains"
        },
        {
          "@type" => "Offer",
          "name" => "Business Plan",
          "price" => "99",
          "priceCurrency" => "USD",
          "description" => "50,000 links with teams and webhooks"
        }
      ]
    }.merge(data)
  end

  def article_schema(data)
    {
      "headline" => data[:title],
      "description" => data[:description],
      "image" => data[:image],
      "author" => {
        "@type" => "Organization",
        "name" => SITE_NAME
      },
      "publisher" => {
        "@type" => "Organization",
        "name" => SITE_NAME,
        "logo" => {
          "@type" => "ImageObject",
          "url" => asset_url("/assets/linklysis-logo.png")
        }
      },
      "datePublished" => data[:published_at]&.iso8601,
      "dateModified" => data[:updated_at]&.iso8601
    }.compact.merge(data.except(:title, :description, :image, :published_at, :updated_at))
  end

  def breadcrumb_schema(data)
    {
      "itemListElement" => data[:items] || []
    }
  end
end
