import { Controller } from "@hotwired/stimulus"
import { createConsumer } from "@rails/actioncable"

export default class extends Controller {
  static targets = [
    "messageContainer",
    "messageInput", 
    "sendButton",
    "typingIndicator",
    "suggestionsContainer",
    "chatWindow",
    "minimizeButton",
    "closeButton",
    "openButton"
  ]
  
  static classes = [
    "minimized",
    "hidden"
  ]
  
  static values = {
    userId: Number,
    sessionContext: Object,
    isMinimized: <PERSON>olean,
    isHidden: <PERSON>olean
  }

  connect() {
    this.setupChannel()
    this.restoreState()
    this.scrollToBottom()
  }

  disconnect() {
    if (this.channel) {
      this.channel.unsubscribe()
    }
  }

  setupChannel() {
    this.consumer = createConsumer(`/cable?user_id=${this.userIdValue}`)
    this.channel = this.consumer.subscriptions.create("BotChannel", {
      connected: () => {
        console.log("Connected to BotChannel")
        this.requestSuggestions()
      },

      disconnected: () => {
        console.log("Disconnected from BotChannel")
      },

      received: (data) => {
        this.handleReceivedData(data)
      }
    })
  }

  handleReceivedData(data) {
    switch(data.type) {
      case 'welcome':
        this.addSystemMessage(data.message)
        break
      case 'message':
        this.hideTypingIndicator()
        this.addBotMessage(data.bot_response, data.intent)
        this.updateSessionContext(data.context)
        if (data.suggestions) {
          this.displaySuggestions(data.suggestions)
        }
        break
      case 'typing':
        data.is_typing ? this.showTypingIndicator() : this.hideTypingIndicator()
        break
      case 'suggestions':
        this.displaySuggestions(data.suggestions)
        break
      case 'error':
        this.hideTypingIndicator()
        this.addErrorMessage(data.error)
        break
    }
    this.scrollToBottom()
  }

  sendMessage(event) {
    event?.preventDefault()
    
    const message = this.messageInputTarget.value.trim()
    if (!message) return
    
    // Display user message immediately
    this.addUserMessage(message)
    
    this.channel.perform("send_message", {
      message: message,
      context: this.sessionContextValue
    })
    
    this.messageInputTarget.value = ""
    this.messageInputTarget.focus()
    this.showTypingIndicator()
  }

  handleKeydown(event) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      this.sendMessage()
    } else {
      // Auto-resize textarea
      this.autoResizeTextarea(event.target)
    }
  }

  handleInput(event) {
    this.autoResizeTextarea(event.target)
  }

  autoResizeTextarea(textarea) {
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px'
  }

  handleSuggestionClick(event) {
    event.preventDefault()
    const suggestion = event.currentTarget.dataset.suggestion
    this.messageInputTarget.value = suggestion
    this.sendMessage()
    // Hide suggestions after clicking
    this.suggestionsContainerTarget.classList.add('hidden')
  }

  addUserMessage(message) {
    const messageHtml = `
      <div class="flex justify-end mb-4 animate-slide-in-right">
        <div class="max-w-xs md:max-w-md">
          <div class="bg-blue-500 text-white rounded-lg px-4 py-2">
            <p class="text-sm">${this.escapeHtml(message)}</p>
          </div>
          <p class="text-xs text-gray-500 mt-1 text-right">
            ${new Date().toLocaleTimeString()}
          </p>
        </div>
      </div>
    `
    this.messageContainerTarget.insertAdjacentHTML('beforeend', messageHtml)
  }

  addBotMessage(message, intent) {
    const messageHtml = `
      <div class="flex justify-start mb-4 animate-slide-in-left">
        <div class="max-w-xs md:max-w-md">
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2">
            <p class="text-sm text-gray-800 dark:text-gray-200">${this.formatBotMessage(message)}</p>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            Linklysis Assistant • ${new Date().toLocaleTimeString()}
            ${intent ? ` • ${this.formatIntent(intent)}` : ''}
          </p>
        </div>
      </div>
    `
    this.messageContainerTarget.insertAdjacentHTML('beforeend', messageHtml)
  }

  addSystemMessage(message) {
    const messageHtml = `
      <div class="text-center text-xs text-gray-500 my-2">
        ${this.escapeHtml(message)}
      </div>
    `
    this.messageContainerTarget.insertAdjacentHTML('beforeend', messageHtml)
  }

  addErrorMessage(error) {
    const messageHtml = `
      <div class="flex justify-center mb-4">
        <div class="bg-red-50 border border-red-200 rounded-lg px-4 py-2">
          <p class="text-sm text-red-600">
            <span class="font-semibold">Error:</span> ${this.escapeHtml(error)}
          </p>
        </div>
      </div>
    `
    this.messageContainerTarget.insertAdjacentHTML('beforeend', messageHtml)
  }

  showTypingIndicator() {
    this.typingIndicatorTarget.classList.remove('hidden')
  }

  hideTypingIndicator() {
    this.typingIndicatorTarget.classList.add('hidden')
  }

  displaySuggestions(suggestions) {
    if (!suggestions || suggestions.length === 0) {
      this.suggestionsContainerTarget.classList.add('hidden')
      return
    }
    
    const suggestionsHtml = suggestions.map(suggestion => `
      <button
        data-action="click->bot#handleSuggestionClick"
        data-suggestion="${this.escapeHtml(suggestion)}"
        class="inline-block bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 
               rounded-full px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 
               transition-colors duration-200"
      >
        ${this.escapeHtml(suggestion)}
      </button>
    `).join('')
    
    this.suggestionsContainerTarget.innerHTML = suggestionsHtml
    this.suggestionsContainerTarget.classList.remove('hidden')
  }

  requestSuggestions() {
    this.channel.perform("request_suggestions", {
      intent: this.detectIntent()
    })
  }

  detectIntent() {
    const pathname = window.location.pathname
    if (pathname === '/') return 'home'
    if (pathname.includes('/links')) return 'links'
    if (pathname.includes('/analytics')) return 'analytics'
    if (pathname.includes('/settings')) return 'settings'
    return 'general'
  }

  updateSessionContext(context) {
    this.sessionContextValue = { ...this.sessionContextValue, ...context }
  }

  minimize() {
    this.isMinimizedValue = true
    this.chatWindowTarget.classList.add(this.minimizedClass)
    this.saveState()
  }

  maximize() {
    this.isMinimizedValue = false
    this.chatWindowTarget.classList.remove(this.minimizedClass)
    this.saveState()
    this.scrollToBottom()
  }

  close() {
    this.isHiddenValue = true
    this.chatWindowTarget.classList.add(this.hiddenClass)
    this.openButtonTarget.classList.remove(this.hiddenClass)
    this.saveState()
  }

  open() {
    this.isHiddenValue = false
    this.chatWindowTarget.classList.remove(this.hiddenClass)
    this.openButtonTarget.classList.add(this.hiddenClass)
    this.saveState()
    this.scrollToBottom()
  }

  toggleMinimize() {
    this.isMinimizedValue ? this.maximize() : this.minimize()
  }

  saveState() {
    localStorage.setItem('bot_minimized', this.isMinimizedValue)
    localStorage.setItem('bot_hidden', this.isHiddenValue)
  }

  restoreState() {
    const minimized = localStorage.getItem('bot_minimized') === 'true'
    const hidden = localStorage.getItem('bot_hidden') === 'true'
    
    if (minimized) {
      this.minimize()
    }
    
    if (hidden) {
      this.close()
    }
  }

  scrollToBottom() {
    this.messageContainerTarget.scrollTop = this.messageContainerTarget.scrollHeight
  }

  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  formatBotMessage(message) {
    // Convert markdown-style formatting to HTML
    return message
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">$1</code>')
      .replace(/\n/g, '<br>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-500 hover:underline" target="_blank">$1</a>')
  }

  formatIntent(intent) {
    const intentMap = {
      'greeting': '👋',
      'help': '❓',
      'analytics': '📊',
      'links': '🔗',
      'settings': '⚙️',
      'subscription': '💳',
      'error': '⚠️'
    }
    return intentMap[intent] || intent
  }
}