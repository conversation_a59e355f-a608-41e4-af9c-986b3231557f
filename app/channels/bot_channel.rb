class BotChannel < ApplicationCable::Channel
  def subscribed
    # Stream from a unique channel for this user
    stream_from "bot_#{current_user.id}"

    # Send welcome message
    ActionCable.server.broadcast(
      "bot_#{current_user.id}",
      {
        type: "welcome",
        message: "Connected to Linklysis Assistant",
        timestamp: Time.current.to_i
      }
    )
  end

  def unsubscribed
    # Clean up when channel is terminated
    stop_all_streams
  end

  def send_message(data)
    message = data["message"]&.strip

    return if message.blank?

    # Process message through bot service
    bot_service = BotService.new(user: current_user, session_context: data["context"] || {})
    result = bot_service.process_message(message)

    if result.success?
      # Broadcast the response
      ActionCable.server.broadcast(
        "bot_#{current_user.id}",
        {
          type: "message",
          user_message: message,
          bot_response: result.data[:response],
          intent: result.data[:intent],
          suggestions: result.data[:suggestions],
          context: result.data[:context],
          timestamp: Time.current.to_i
        }
      )
    else
      # Handle error
      ActionCable.server.broadcast(
        "bot_#{current_user.id}",
        {
          type: "error",
          error: result.errors.first,
          timestamp: Time.current.to_i
        }
      )
    end
  end

  def typing_indicator(data)
    # Broadcast typing indicator to show bot is "thinking"
    ActionCable.server.broadcast(
      "bot_#{current_user.id}",
      {
        type: "typing",
        is_typing: data["is_typing"],
        timestamp: Time.current.to_i
      }
    )
  end

  def request_suggestions(data)
    # Generate contextual suggestions
    user_context = {
      subscription_plan: current_user.subscription_plan || "free",
      total_links: current_user.links.count,
      active_links: current_user.links.active.count,
      has_custom_domains: current_user.custom_domains.any?,
      is_team_member: current_user.teams.any?,
      recent_activity: current_user.links.recent.limit(3).exists?
    }

    suggestions = generate_suggestions(user_context, data["intent"])

    ActionCable.server.broadcast(
      "bot_#{current_user.id}",
      {
        type: "suggestions",
        suggestions: suggestions,
        context: user_context,
        timestamp: Time.current.to_i
      }
    )
  end

  private

  def generate_suggestions(context, intent = nil)
    base_suggestions = [ "How do I create a link?", "Show me my analytics", "Help me get started" ]

    suggestions = []

    if context[:total_links] == 0
      suggestions += [ "Create my first link", "What is link shortening?", "How do I get started?" ]
    else
      suggestions += [ "View my analytics", "How do I customize short codes?", "Export my data" ]
    end

    if context[:subscription_plan] == "free"
      suggestions += [ "What are the subscription benefits?", "How do I upgrade my plan?" ]
    end

    unless context[:has_custom_domains]
      suggestions += [ "How do custom domains work?", "Set up a custom domain" ]
    end

    unless context[:is_team_member]
      suggestions += [ "How do teams work?", "Invite team members" ]
    end

    # Return unique suggestions, limited to 6
    suggestions.uniq.first(6)
  end
end
