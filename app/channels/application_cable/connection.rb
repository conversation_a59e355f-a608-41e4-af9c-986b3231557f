module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      self.current_user = find_verified_user
    end

    private

    def find_verified_user
      # For development/testing, we'll use a simpler approach
      # Get user ID from URL parameters or headers
      if request.params[:user_id].present?
        if verified_user = User.find_by(id: request.params[:user_id])
          return verified_user
        end
      end

      # Try to extract from session cookie for Devise
      session_key = Rails.application.config.session_options[:key] || "_linkmaster_session"

      if cookies && (session_data = cookies.encrypted[session_key])
        # Try different Devise session structures
        if warden_data = session_data.dig("warden.user.user.key")
          user_id = warden_data&.first&.first
        elsif user_id = session_data["user_id"]
          # Direct user_id in session
        elsif devise_session = session_data["devise.user_id"]
          user_id = devise_session
        end

        if user_id && (verified_user = User.find_by(id: user_id))
          return verified_user
        end
      end

      # For now, let's be more permissive in development
      if Rails.env.development?
        logger.info "ActionCable: No authenticated user found, rejecting connection"
      end

      reject_unauthorized_connection
    end
  end
end
