<% content_for :title, "Linklysis - Professional Link Shortening & Analytics Platform" %>
<% content_for :head do %>
  <%= seo_meta_tags(
    title: "Linklysis - Professional Link Shortening & Analytics Platform",
    description: "Transform your links into powerful marketing tools with Linklysis. Get detailed analytics, custom domains, QR codes, and team collaboration features. Start free today!",
    keywords: %w[link shortener url shortener custom domains analytics click tracking QR codes team collaboration marketing tools bitly alternative],
    type: "website",
    canonical_url: root_url
  ) %>
  <%= structured_data("SoftwareApplication") %>
  <%= breadcrumbs(
    { name: "Home", url: "/" }
  ) %>
<% end %>

<!-- Hero Section with Glassmorphism -->
<section class="min-h-screen relative flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
  <!-- Animated gradient orbs -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full filter blur-3xl opacity-50 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500 rounded-full filter blur-3xl opacity-50 animate-pulse animation-delay-2000"></div>
    <div class="absolute top-1/2 left-1/2 w-96 h-96 bg-blue-500 rounded-full filter blur-3xl opacity-50 animate-pulse animation-delay-4000"></div>
  </div>

  <!-- Grid pattern overlay -->
  <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"%3E%3Cpath d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100%25" height="100%25" fill="url(%23grid)"/%3E%3C/svg%3E')] opacity-20"></div>

  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <!-- Floating badge -->
    <div class="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full text-sm text-white mb-8 animate-float">
      <span class="relative flex h-2 w-2">
        <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
        <span class="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
      </span>
      <span>Trusted by 50,000+ innovators worldwide</span>
    </div>

    <h1 class="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
      Links That
      <span class="relative inline-block">
        <span class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 blur-lg opacity-75"></span>
        <span class="relative bg-gradient-to-r from-purple-400 via-pink-400 to-orange-400 bg-clip-text text-transparent">
          Perform
        </span>
      </span>
    </h1>
    
    <p class="text-xl sm:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
      Supercharge your marketing with intelligent link management. Track, optimize, and scale your campaigns with real-time insights.
    </p>

    <!-- CTA buttons with glassmorphism -->
    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
      <%= link_to new_user_registration_path, class: "group relative px-8 py-4 bg-white text-gray-900 rounded-2xl font-semibold text-lg overflow-hidden transition-all duration-300 hover:scale-105" do %>
        <span class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
        <span class="relative group-hover:text-white transition-colors duration-300">Start Building Free</span>
      <% end %>

      <%= link_to "#demo", class: "px-8 py-4 bg-white/10 backdrop-blur-md border border-white/20 text-white rounded-2xl font-semibold text-lg hover:bg-white/20 transition-all duration-300 hover:scale-105", data: { action: "click->demo#show" } do %>
        Watch Demo
      <% end %>
    </div>

    <!-- Real stats ticker -->
    <div class="flex flex-wrap justify-center gap-8 text-white/80">
      <div class="flex items-center gap-2">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span class="font-mono text-sm">
          <span class="text-2xl font-bold text-white"><%= @stats.find { |s| s[:label] == "Links Created" }&.dig(:number) || "0" %></span> links created
        </span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
        <span class="font-mono text-sm">
          <span class="text-2xl font-bold text-white"><%= @stats.find { |s| s[:label] == "Clicks Tracked" }&.dig(:number) || "0" %></span> clicks tracked
        </span>
      </div>
    </div>
  </div>

  <!-- Scroll indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg class="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
    </svg>
  </div>
</section>

<!-- Interactive Features Grid -->
<section class="py-24 bg-gray-50 relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
        Built for Modern Marketers
      </h2>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Every feature designed to help you understand and optimize your audience engagement
      </p>
    </div>

    <!-- Bento grid layout -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Large feature card -->
      <div class="md:col-span-2 lg:col-span-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl p-8 text-white relative overflow-hidden group">
        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
        <div class="relative z-10">
          <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-6">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-3">Real-Time Analytics Dashboard</h3>
          <p class="text-white/90 mb-6">Track clicks, geography, devices, and referrers as they happen. Make data-driven decisions instantly.</p>
          <div class="flex items-center gap-4">
            <div class="flex -space-x-2">
              <% 5.times do |i| %>
                <div class="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm border-2 border-white/30"></div>
              <% end %>
            </div>
            <span class="text-sm text-white/70">Used by 10k+ marketers</span>
          </div>
        </div>
      </div>

      <!-- Vertical card -->
      <div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">Lightning Fast</h3>
        <p class="text-gray-600 mb-4">Sub-50ms redirects globally. Your links work at the speed of thought.</p>
        <div class="flex items-center gap-2 text-sm text-gray-500">
          <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <span>99.99% uptime SLA</span>
        </div>
      </div>

      <!-- Small feature cards -->
      <div class="bg-gradient-to-br from-green-500 to-emerald-500 rounded-3xl p-8 text-white">
        <div class="w-14 h-14 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-6">
          <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold mb-3">GDPR Compliant</h3>
        <p class="text-white/90">Privacy-first analytics that respect your users' data.</p>
      </div>

      <div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
        <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">Powerful API</h3>
        <p class="text-gray-600">Integrate with your stack in minutes. RESTful & GraphQL.</p>
      </div>

      <div class="bg-gradient-to-br from-indigo-500 to-purple-500 rounded-3xl p-8 text-white">
        <div class="w-14 h-14 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-6">
          <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold mb-3">Team Collaboration</h3>
        <p class="text-white/90">Work together seamlessly with roles and permissions.</p>
      </div>
    </div>
  </div>
</section>

<!-- Animated Stats -->
<section class="py-24 bg-gray-900 relative overflow-hidden">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-purple-900/20 to-pink-900/20"></div>
  </div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
      <% icons = ["🔗", "📊", "🌍", "⚡"] %>
      <% @stats.each_with_index do |stat, index| %>
        <div class="text-center group cursor-pointer" data-animate>
          <div class="text-4xl mb-3 transform group-hover:scale-125 transition-transform duration-300"><%= icons[index] %></div>
          <div class="text-4xl sm:text-5xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            <%= stat[:number] %>
          </div>
          <div class="text-gray-400"><%= stat[:label] %></div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Testimonials Carousel -->
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
        Loved by Teams Everywhere
      </h2>
      <p class="text-xl text-gray-600">
        See what our customers have to say
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @testimonials[0..2].each_with_index do |testimonial, index| %>
        <% gradients = ["from-purple-400 to-pink-400", "from-blue-400 to-cyan-400", "from-green-400 to-emerald-400"] %>
        <div class="relative group">
          <div class="absolute inset-0 bg-gradient-to-r <%= gradients[index] %> rounded-3xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
          <div class="relative bg-gray-50 rounded-3xl p-8 hover:bg-white transition-colors duration-300">
            <div class="flex items-center mb-6">
              <img src="<%= testimonial[:avatar] %>" alt="<%= testimonial[:name] %>" class="w-14 h-14 rounded-full mr-4">
              <div>
                <h4 class="font-bold text-gray-900"><%= testimonial[:name] %></h4>
                <p class="text-sm text-gray-600"><%= testimonial[:role] %> at <%= testimonial[:company] %></p>
              </div>
            </div>
            
            <div class="flex mb-4">
              <% 5.times do %>
                <svg class="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                  <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                </svg>
              <% end %>
            </div>
            
            <p class="text-gray-700 italic">"<%= testimonial[:content] %>"</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Pricing with Toggle -->
<section class="py-24 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
        Pricing That Scales With You
      </h2>
      <p class="text-xl text-gray-600 mb-8">
        Start free, upgrade when you need more power
      </p>
      
      <!-- Billing toggle -->
      <div class="inline-flex items-center bg-white rounded-full p-1 shadow-md">
        <button class="px-6 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium">Monthly</button>
        <button class="px-6 py-2 rounded-full text-gray-600 font-medium">Annual (Save 20%)</button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
      <% @pricing_plans[0..2].each_with_index do |plan, index| %>
        <% is_popular = index == 1 %>
        <div class="relative <%= 'scale-105' if is_popular %>">
          <% if is_popular %>
            <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10">
              <span class="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg">
                Most Popular
              </span>
            </div>
          <% end %>
          
          <div class="bg-white rounded-3xl p-8 <%= 'shadow-2xl' if is_popular %> shadow-lg hover:shadow-xl transition-all duration-300">
            <h3 class="text-2xl font-bold text-gray-900 mb-2"><%= plan[:name] %></h3>
            <div class="mb-6">
              <span class="text-5xl font-bold text-gray-900"><%= plan[:price] %></span>
              <span class="text-gray-600">/<%= plan[:period] %></span>
            </div>
            
            <ul class="space-y-4 mb-8">
              <% plan[:features].each do |feature| %>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700"><%= feature %></span>
                </li>
              <% end %>
            </ul>
            
            <% if plan[:name] == "Free" %>
              <%= link_to new_user_registration_path, class: "w-full py-3 px-6 rounded-2xl font-semibold transition-all duration-300 text-center block #{is_popular ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transform hover:scale-105' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}" do %>
                <%= plan[:cta] %>
              <% end %>
            <% elsif plan[:name] == "Enterprise" %>
              <%= link_to contact_path, class: "w-full py-3 px-6 rounded-2xl font-semibold transition-all duration-300 text-center block #{is_popular ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transform hover:scale-105' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}" do %>
                <%= plan[:cta] %>
              <% end %>
            <% else %>
              <%= link_to new_user_registration_path, class: "w-full py-3 px-6 rounded-2xl font-semibold transition-all duration-300 text-center block #{is_popular ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transform hover:scale-105' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}" do %>
                <%= plan[:cta] %>
              <% end %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Final CTA -->
<section class="py-24 bg-gradient-to-br from-purple-900 via-pink-900 to-orange-900 relative overflow-hidden">
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"%3E%3Cpath d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100%25" height="100%25" fill="url(%23grid)"/%3E%3C/svg%3E')] opacity-20"></div>
  </div>
  
  <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl sm:text-5xl font-bold text-white mb-6">
      Ready to Transform Your Links?
    </h2>
    <p class="text-xl text-white/80 mb-12">
      Join 50,000+ businesses already using LinkMaster to grow
    </p>
    
    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
      <%= link_to new_user_registration_path, class: "group relative px-8 py-4 bg-white text-gray-900 rounded-2xl font-semibold text-lg overflow-hidden transition-all duration-300 hover:scale-105" do %>
        <span class="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
        <span class="relative group-hover:text-white transition-colors duration-300">Get Started Free</span>
      <% end %>

      <%= link_to contact_path, class: "px-8 py-4 bg-white/10 backdrop-blur-md border border-white/20 text-white rounded-2xl font-semibold text-lg hover:bg-white/20 transition-all duration-300 hover:scale-105" do %>
        Talk to Sales
      <% end %>
    </div>
    
    <p class="mt-8 text-white/60 text-sm">
      No credit card required • Free forever plan • Cancel anytime
    </p>
  </div>
</section>
