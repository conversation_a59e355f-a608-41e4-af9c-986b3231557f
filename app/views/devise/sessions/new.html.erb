<!-- Authentication Layout -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <!-- Background pattern -->
  <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cpattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"%3E%3Cpath d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\"100%25\" height=\"100%25\" fill=\"url(%23grid)\"/%3E%3C/svg%3E')] opacity-20"></div>
  
  <!-- Floating gradient orbs -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-purple-500 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-72 h-72 bg-pink-500 rounded-full filter blur-3xl opacity-30 animate-pulse animation-delay-2000"></div>
  </div>

  <div class="relative z-10 max-w-md w-full space-y-8">
    <!-- Logo and Header -->
    <div class="text-center">
      <%= link_to root_path, class: "inline-flex items-center space-x-3 group mb-8" do %>
        <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-transform duration-200">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
        </div>
        <span class="text-2xl font-bold text-white">LinkMaster</span>
      <% end %>
      
      <div>
        <h2 class="text-3xl font-bold text-white mb-2">
          Welcome back
        </h2>
        <p class="text-gray-300">
          Sign in to your account to continue
        </p>
      </div>
    </div>

    <!-- Authentication Form -->
    <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl">
      <%= form_for(resource, as: resource_name, url: session_path(resource_name), local: true, html: { class: "space-y-6" }) do |f| %>
        <%= hidden_field_tag :authenticity_token, form_authenticity_token %>

        <div>
          <%= f.label :email, class: "block text-sm font-medium text-white mb-2" %>
          <%= f.email_field :email, 
                autofocus: true, 
                autocomplete: "email",
                class: "w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200",
                placeholder: "Enter your email" %>
        </div>

        <div>
          <%= f.label :password, class: "block text-sm font-medium text-white mb-2" %>
          <%= f.password_field :password, 
                autocomplete: "current-password",
                class: "w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200",
                placeholder: "Enter your password" %>
        </div>

        <% if devise_mapping.rememberable? %>
          <div class="flex items-center">
            <%= f.check_box :remember_me, class: "h-4 w-4 text-purple-600 focus:ring-purple-500 border-white/20 rounded bg-white/10" %>
            <%= f.label :remember_me, class: "ml-2 block text-sm text-gray-300" %>
          </div>
        <% end %>

        <div>
          <%= f.submit "Sign In", 
                class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:shadow-lg transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200" %>
        </div>
      <% end %>

      <!-- Divider -->
      <div class="relative my-6">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-white/20"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-transparent text-gray-300">or</span>
        </div>
      </div>

      <!-- Links -->
      <div class="space-y-4 text-center">
        <div>
          <%= link_to "Don't have an account? Sign up", new_registration_path(resource_name), 
                class: "text-purple-300 hover:text-purple-200 font-medium transition-colors" %>
        </div>
        
        <% if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
          <div>
            <%= link_to "Forgot your password?", new_password_path(resource_name), 
                  class: "text-gray-400 hover:text-gray-300 text-sm transition-colors" %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Back to Home -->
    <div class="text-center">
      <%= link_to "← Back to home", root_path, 
            class: "text-gray-400 hover:text-gray-300 transition-colors" %>
    </div>
  </div>
</div>