<% content_for :title, "Account Settings" %>
<% content_for :page_title, "Account Settings" %>

<% content_for :breadcrumbs do %>
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <%= link_to authenticated_root_path, class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
          </svg>
        <% end %>
      </li>
      <li class="flex items-center">
        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="ml-4 text-sm font-medium text-gray-900">Account Settings</span>
      </li>
    </ol>
  </nav>
<% end %>

<div class="max-w-4xl mx-auto p-6">
  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex items-center space-x-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
      </div>
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Account Settings</h1>
        <p class="text-gray-600">Manage your account information and security preferences</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
    <%= form_for(resource, as: resource_name, url: registration_path(resource_name),
                 html: { method: :put, class: "space-y-8", data: { controller: "form-validation" } }) do |f| %>

      <!-- Error Messages -->
      <% if resource.errors.any? %>
        <div class="p-6 bg-red-50 border-l-4 border-red-400">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                <%= pluralize(resource.errors.count, "error") %> prohibited this account from being saved:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc list-inside space-y-1">
                  <% resource.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Account Information Section -->
      <div class="p-6">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            Account Information
          </h2>
          <p class="text-sm text-gray-600 mt-1">Update your email address and basic account details</p>
        </div>

        <div class="space-y-6">
          <!-- Email Field -->
          <div>
            <%= f.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= f.email_field :email,
                              autofocus: true,
                              autocomplete: "email",
                              class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                              placeholder: "Enter your email address" %>

            <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
              <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm text-yellow-800">
                    Currently waiting confirmation for: <strong><%= resource.unconfirmed_email %></strong>
                  </span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Security Section -->
      <div class="border-t border-gray-200 p-6">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            Security Settings
          </h2>
          <p class="text-sm text-gray-600 mt-1">Update your password and security preferences</p>
        </div>

        <div class="space-y-6">
          <!-- Password Field -->
          <div>
            <%= f.label :password, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
              New Password
              <span class="text-xs font-normal text-gray-500">(leave blank if you don't want to change it)</span>
            <% end %>
            <%= f.password_field :password,
                                autocomplete: "new-password",
                                class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                placeholder: "Enter new password" %>
            <% if @minimum_password_length %>
              <p class="mt-2 text-xs text-gray-500">
                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Minimum <%= @minimum_password_length %> characters required
              </p>
            <% end %>
          </div>

          <!-- Password Confirmation Field -->
          <div>
            <%= f.label :password_confirmation, "Confirm New Password", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= f.password_field :password_confirmation,
                                autocomplete: "new-password",
                                class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                placeholder: "Confirm your new password" %>
          </div>

          <!-- Current Password Field -->
          <div>
            <%= f.label :current_password, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
              Current Password
              <span class="text-xs font-normal text-gray-500">(required to confirm changes)</span>
            <% end %>
            <%= f.password_field :current_password,
                                autocomplete: "current-password",
                                class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                placeholder: "Enter your current password" %>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="border-t border-gray-200 px-6 py-4 bg-gray-50">
        <div class="flex items-center justify-between">
          <%= link_to "Cancel", :back,
                      class: "px-6 py-2.5 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors" %>
          <%= f.submit "Update Account",
                      class: "px-6 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg shadow-purple-500/25 cursor-pointer",
                      data: { disable_with: "Updating..." } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Danger Zone -->
  <div class="mt-8 bg-white rounded-2xl shadow-sm border border-red-200 overflow-hidden">
    <div class="p-6">
      <div class="mb-6">
        <h2 class="text-lg font-semibold text-red-900 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          Danger Zone
        </h2>
        <p class="text-sm text-red-600 mt-1">Irreversible and destructive actions</p>
      </div>

      <div class="p-4 bg-red-50 border border-red-200 rounded-xl">
        <div class="flex items-start gap-4">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <h3 class="text-sm font-semibold text-red-900 mb-2">Delete Account</h3>
            <p class="text-sm text-red-700 mb-4">
              Once you delete your account, there is no going back. This will permanently delete your account,
              all your links, analytics data, and remove all team associations.
            </p>
            <%= button_to "Delete My Account",
                          registration_path(resource_name),
                          method: :delete,
                          class: "px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors",
                          data: {
                            confirm: "Are you absolutely sure? This action cannot be undone. Type 'DELETE' to confirm.",
                            turbo_confirm: "Are you absolutely sure? This action cannot be undone."
                          } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
