<% content_for :title, @link.title || "Link Details" %>

<div class="p-6">
  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-1"><%= @link.title || "Link Details" %></h1>
        <p class="text-gray-600">Created <%= time_ago_in_words(@link.created_at) %> ago</p>
      </div>
      <div class="flex items-center gap-3">
        <%= link_to edit_link_path(@link), 
                    class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Link
        <% end %>
        <%= link_to links_path, 
                    class: "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Links
        <% end %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Link Details Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Link Information</h2>
            <p class="text-sm text-gray-600">Your shortened link details</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
          </div>
        </div>

        <!-- Short URL -->
        <div class="mb-6">
          <label class="block text-sm font-semibold text-gray-600 mb-2">Short URL</label>
          <div class="flex items-center gap-2" data-controller="clipboard">
            <div class="flex-1 relative">
              <input type="text"
                     value="<%= @link.short_url(request) %>"
                     readonly
                     class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl bg-gray-50 text-gray-900 font-mono text-sm"
                     data-clipboard-target="source">
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              </div>
            </div>
            <div class="flex gap-2">
              <button data-action="click->clipboard#copy"
                      data-clipboard-target="button"
                      class="px-4 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 font-medium"
                      title="Copy link to clipboard">
                <svg class="w-4 h-4 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                <span class="hidden sm:inline">Copy</span>
              </button>
              <button onclick="shareLink('<%= @link.short_url(request) %>', '<%= j(@link.title || "Check out this link") %>')"
                      class="px-4 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium"
                      title="Share link">
                <svg class="w-4 h-4 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                </svg>
                <span class="hidden sm:inline">Share</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Original URL -->
        <div class="mb-6">
          <label class="block text-sm font-semibold text-gray-600 mb-2">Original URL</label>
          <div class="p-4 bg-gray-50 rounded-xl">
            <%= link_to @link.original_url, @link.original_url, 
                        target: "_blank",
                        class: "text-blue-600 hover:text-blue-700 break-all text-sm flex items-center gap-2" %>
          </div>
        </div>

        <!-- Additional Info -->
        <div class="grid grid-cols-2 gap-4">
          <% if @link.custom_domain %>
            <div>
              <label class="block text-sm font-semibold text-gray-600 mb-1">Custom Domain</label>
              <p class="text-gray-900"><%= @link.custom_domain.domain %></p>
            </div>
          <% end %>
          <% if @link.team %>
            <div>
              <label class="block text-sm font-semibold text-gray-600 mb-1">Team</label>
              <p class="text-gray-900"><%= @link.team.name %></p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- QR Code Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-violet-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">QR Code</h2>
            <p class="text-sm text-gray-600">Share your link with a scannable code</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
            </svg>
          </div>
        </div>

        <div class="flex flex-col xl:flex-row gap-8">
          <!-- QR Code Preview -->
          <div class="flex-shrink-0">
            <div class="bg-white p-8 border-2 border-gray-200 rounded-xl shadow-sm qr-code-container">
              <div id="qr-code-preview">
                <%= safe_qr_code_svg(@link, module_size: 8) %>
              </div>
            </div>
          </div>

          <!-- Customization Controls -->
          <div class="flex-1 space-y-6">
            <div>
              <p class="text-sm text-gray-600 mb-4">
                Scan this QR code with any smartphone camera to go directly to your short URL. Perfect for print materials, presentations, or anywhere you can't use a clickable link.
              </p>
            </div>

            <!-- Customization Form -->
            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
              <h3 class="text-sm font-semibold text-gray-900 mb-3">Customize QR Code</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Size Control -->
                <div>
                  <label for="qr-size" class="block text-xs font-medium text-gray-700 mb-1">Size</label>
                  <select id="qr-size" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent">
                    <option value="4">Small (4)</option>
                    <option value="6">Medium (6)</option>
                    <option value="8" selected>Large (8)</option>
                    <option value="10">Extra Large (10)</option>
                  </select>
                </div>

                <!-- Error Correction -->
                <div>
                  <label for="qr-error-correction" class="block text-xs font-medium text-gray-700 mb-1">Error Correction</label>
                  <select id="qr-error-correction" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent">
                    <option value="l">Low (7%)</option>
                    <option value="m" selected>Medium (15%)</option>
                    <option value="q">Quartile (25%)</option>
                    <option value="h">High (30%)</option>
                  </select>
                </div>

                <!-- Foreground Color -->
                <div>
                  <label for="qr-color" class="block text-xs font-medium text-gray-700 mb-1">Foreground Color</label>
                  <div class="flex gap-2">
                    <input type="color" id="qr-color" value="#000000" class="w-12 h-8 border border-gray-300 rounded cursor-pointer">
                    <input type="text" id="qr-color-hex" value="000000" placeholder="000000" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent">
                  </div>
                </div>

                <!-- Background Color -->
                <div>
                  <label for="qr-bg-color" class="block text-xs font-medium text-gray-700 mb-1">Background Color</label>
                  <div class="flex gap-2">
                    <input type="color" id="qr-bg-color" value="#ffffff" class="w-12 h-8 border border-gray-300 rounded cursor-pointer">
                    <input type="text" id="qr-bg-color-hex" value="ffffff" placeholder="ffffff" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent">
                  </div>
                </div>
              </div>

              <!-- Preview Button -->
              <button id="update-qr-preview" class="w-full px-4 py-2 bg-violet-600 text-white text-sm font-medium rounded-lg hover:bg-violet-700 transition-colors">
                Update Preview
              </button>
            </div>

            <!-- Download Options -->
            <div class="space-y-3">
              <h3 class="text-sm font-semibold text-gray-900">Download Options</h3>
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-2">
                <button id="download-svg" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  SVG
                </button>
                <button id="download-png" class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  PNG
                </button>
                <button id="download-pdf" class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white text-sm font-medium rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  PDF
                </button>
              </div>
            </div>

            <!-- Sharing Options -->
            <div class="space-y-3">
              <h3 class="text-sm font-semibold text-gray-900">Share QR Code</h3>
              <div class="grid grid-cols-2 gap-2">
                <button id="share-qr-twitter" class="inline-flex items-center justify-center px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                  Twitter
                </button>
                <button id="share-qr-linkedin" class="inline-flex items-center justify-center px-3 py-2 bg-blue-700 text-white text-sm font-medium rounded-lg hover:bg-blue-800 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                  LinkedIn
                </button>
                <button id="copy-qr-link" class="inline-flex items-center justify-center px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                  Copy Link
                </button>
                <button id="email-qr" class="inline-flex items-center justify-center px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  Email
                </button>
              </div>
            </div>

            <!-- Embed Code -->
            <div class="space-y-3">
              <h3 class="text-sm font-semibold text-gray-900">Embed QR Code</h3>
              <div class="space-y-2">
                <label for="embed-code" class="block text-xs font-medium text-gray-700">HTML Embed Code</label>
                <textarea id="embed-code" readonly class="w-full px-3 py-2 text-xs border border-gray-300 rounded-md bg-gray-50 font-mono" rows="3"></textarea>
                <button id="copy-embed-code" class="w-full px-3 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors">
                  Copy Embed Code
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Stats Cards -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL CLICKS</h3>
            <div class="w-8 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@link.link_clicks_count) %></div>
        <p class="text-sm text-gray-500">Total link clicks</p>
      </div>

      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-sm font-semibold text-gray-600 mb-1">UNIQUE VISITORS</h3>
            <div class="w-8 h-1 bg-gradient-to-r from-green-500 to-green-400 rounded-full"></div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@link.unique_visitors) %></div>
        <p class="text-sm text-gray-500">Unique visitors</p>
      </div>

      <!-- QR Code Analytics -->
      <% qr_stats = @link.qr_code_generation_stats %>
      <% if qr_stats[:total_generations] > 0 %>
        <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h3 class="text-sm font-semibold text-gray-600 mb-1">QR CODE ANALYTICS</h3>
              <div class="w-8 h-1 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"></div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-2xl font-bold text-gray-900"><%= qr_stats[:total_generations] %></span>
              <span class="text-sm text-gray-500">Total Generated</span>
            </div>

            <% if qr_stats[:popular_format] %>
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600">Most Popular Format:</span>
                <span class="font-medium text-purple-600 uppercase"><%= qr_stats[:popular_format] %></span>
              </div>
            <% end %>

            <div class="grid grid-cols-3 gap-2 pt-2">
              <% qr_stats[:by_format].each do |format, count| %>
                <div class="text-center p-2 bg-gray-50 rounded-lg">
                  <div class="text-sm font-semibold text-gray-900"><%= count %></div>
                  <div class="text-xs text-gray-500 uppercase"><%= format %></div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Recent Clicks -->
      <% if @recent_clicks.any? %>
        <div class="bg-white rounded-2xl border border-gray-200 p-6">
          <h3 class="text-lg font-bold text-gray-900 mb-4">Recent Activity</h3>
          <div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-50 pr-2">
            <div class="space-y-3">
              <% @recent_clicks.each do |click| %>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div class="flex-1">
                    <div class="flex items-center gap-2 text-sm">
                      <span class="font-medium text-gray-900">
                        <%= click.clicked_at.strftime("%b %d, %I:%M %p") %>
                      </span>
                      <% if click.tracking_data&.dig('country_code').present? %>
                        <span class="text-gray-600">•</span>
                        <span class="text-gray-700">
                          <%= click.tracking_data['country_code'] %>
                        </span>
                      <% end %>
                    </div>
                    <div class="flex items-center gap-2 mt-1">
                      <% if click.tracking_data&.dig('browser').present? %>
                        <span class="text-xs text-gray-500">
                          <%= click.tracking_data['browser'] %>
                        </span>
                      <% end %>
                      <% if click.attribution_data&.dig('utm_source').present? %>
                        <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-0.5 rounded-full">
                          <%= click.attribution_data['utm_source'] %>
                        </span>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  function shareLink(url, title) {
    if (navigator.share) {
      // Use Web Share API if available (mobile browsers)
      navigator.share({
        title: title,
        url: url
      }).catch(err => {
        console.log('Error sharing:', err);
        fallbackShare(url);
      });
    } else {
      // Fallback for desktop browsers
      fallbackShare(url);
    }
  }

  function fallbackShare(url) {
    // Copy to clipboard as fallback
    navigator.clipboard.writeText(url).then(() => {
      // Show feedback
      const shareButtons = document.querySelectorAll('button[onclick*="shareLink"]');
      shareButtons.forEach(button => {
        const originalText = button.innerHTML;
        button.innerHTML = '<svg class="w-4 h-4 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span class="hidden sm:inline">Copied!</span>';
        button.classList.add('bg-green-50', 'border-green-300', 'text-green-700');
        button.classList.remove('border-gray-300', 'text-gray-700', 'hover:bg-gray-50');

        setTimeout(() => {
          button.innerHTML = originalText;
          button.classList.remove('bg-green-50', 'border-green-300', 'text-green-700');
          button.classList.add('border-gray-300', 'text-gray-700', 'hover:bg-gray-50');
        }, 2000);
      });
    }).catch(err => {
      console.error('Failed to copy: ', err);
      alert('Link copied to clipboard: ' + url);
    });
  }

  // QR Code Customization
  document.addEventListener('DOMContentLoaded', function() {
    const linkId = <%= @link.id %>;
    const updateButton = document.getElementById('update-qr-preview');
    const qrPreview = document.getElementById('qr-code-preview');

    // Color input synchronization
    const colorInput = document.getElementById('qr-color');
    const colorHexInput = document.getElementById('qr-color-hex');
    const bgColorInput = document.getElementById('qr-bg-color');
    const bgColorHexInput = document.getElementById('qr-bg-color-hex');

    // Sync color picker with hex input
    colorInput.addEventListener('change', function() {
      colorHexInput.value = this.value.substring(1);
    });

    colorHexInput.addEventListener('input', function() {
      if (this.value.match(/^[0-9A-Fa-f]{6}$/)) {
        colorInput.value = '#' + this.value;
      }
    });

    bgColorInput.addEventListener('change', function() {
      bgColorHexInput.value = this.value.substring(1);
    });

    bgColorHexInput.addEventListener('input', function() {
      if (this.value.match(/^[0-9A-Fa-f]{6}$/)) {
        bgColorInput.value = '#' + this.value;
      }
    });

    // Update QR code preview function
    function updateQRPreview() {
      // Show loading state
      qrPreview.style.opacity = '0.5';

      const params = new URLSearchParams({
        module_size: document.getElementById('qr-size').value,
        error_correction: document.getElementById('qr-error-correction').value,
        color: colorHexInput.value,
        background_color: bgColorHexInput.value
      });

      fetch(`/links/${linkId}/qr_code.svg?${params}`)
        .then(response => response.text())
        .then(svg => {
          qrPreview.innerHTML = svg;
          qrPreview.style.opacity = '1';
        })
        .catch(error => {
          console.error('Error updating QR code:', error);
          qrPreview.style.opacity = '1';
        });
    }

    // Update QR code preview on button click
    updateButton.addEventListener('click', updateQRPreview);

    // Real-time preview updates
    document.getElementById('qr-size').addEventListener('change', updateQRPreview);
    document.getElementById('qr-error-correction').addEventListener('change', updateQRPreview);

    // Debounced color updates
    let colorUpdateTimeout;
    function debouncedColorUpdate() {
      clearTimeout(colorUpdateTimeout);
      colorUpdateTimeout = setTimeout(updateQRPreview, 500);
    }

    colorHexInput.addEventListener('input', debouncedColorUpdate);
    bgColorHexInput.addEventListener('input', debouncedColorUpdate);

    // Download handlers
    document.getElementById('download-svg').addEventListener('click', function() {
      downloadQRCode('svg');
    });

    document.getElementById('download-png').addEventListener('click', function() {
      downloadQRCode('png');
    });

    document.getElementById('download-pdf').addEventListener('click', function() {
      downloadQRCode('pdf');
    });

    function downloadQRCode(format) {
      const params = new URLSearchParams({
        module_size: document.getElementById('qr-size').value,
        error_correction: document.getElementById('qr-error-correction').value,
        color: colorHexInput.value,
        background_color: bgColorHexInput.value
      });

      const url = `/links/${linkId}/qr_code.${format}?${params}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = `<%= @link.short_code %>_qr.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // Sharing functionality
    document.getElementById('share-qr-twitter').addEventListener('click', function() {
      const text = `Check out this QR code for <%= @link.short_url(request) %>`;
      const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent('<%= @link.short_url(request) %>')}`;
      window.open(url, '_blank', 'width=600,height=400');
    });

    document.getElementById('share-qr-linkedin').addEventListener('click', function() {
      const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent('<%= @link.short_url(request) %>')}`;
      window.open(url, '_blank', 'width=600,height=400');
    });

    document.getElementById('copy-qr-link').addEventListener('click', function() {
      navigator.clipboard.writeText('<%= @link.short_url(request) %>').then(function() {
        // Show success feedback
        const button = document.getElementById('copy-qr-link');
        const originalText = button.innerHTML;
        button.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
        setTimeout(() => {
          button.innerHTML = originalText;
        }, 2000);
      });
    });

    document.getElementById('email-qr').addEventListener('click', function() {
      const subject = 'QR Code for <%= @link.short_url(request) %>';
      const body = `Hi,\n\nI wanted to share this QR code with you:\n\n<%= @link.short_url(request) %>\n\nScan the QR code to visit: <%= @link.original_url %>\n\nBest regards`;
      const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.location.href = mailtoUrl;
    });

    // Embed code functionality
    function updateEmbedCode() {
      const params = new URLSearchParams({
        module_size: document.getElementById('qr-size').value,
        error_correction: document.getElementById('qr-error-correction').value,
        color: colorHexInput.value,
        background_color: bgColorHexInput.value
      });

      const qrUrl = `${window.location.origin}/links/${linkId}/qr_code.svg?${params}`;
      const embedCode = `<div style="text-align: center;">
  <img src="${qrUrl}" alt="QR Code for <%= @link.short_url(request) %>" style="max-width: 300px; height: auto;" />
  <p style="margin-top: 10px; font-size: 14px; color: #666;">
    <a href="<%= @link.short_url(request) %>" target="_blank"><%= @link.short_url(request) %></a>
  </p>
</div>`;

      document.getElementById('embed-code').value = embedCode;
    }

    // Update embed code when QR options change
    updateEmbedCode();
    updateButton.addEventListener('click', updateEmbedCode);

    document.getElementById('copy-embed-code').addEventListener('click', function() {
      const embedCodeTextarea = document.getElementById('embed-code');
      embedCodeTextarea.select();
      navigator.clipboard.writeText(embedCodeTextarea.value).then(function() {
        const button = document.getElementById('copy-embed-code');
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = originalText;
        }, 2000);
      });
    });
  });
</script>

<style>
  .qr-code-container {
    width: 280px;
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .qr-code-container svg {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
  }

  #qr-code-preview {
    transition: opacity 0.3s ease-in-out;
  }
</style>