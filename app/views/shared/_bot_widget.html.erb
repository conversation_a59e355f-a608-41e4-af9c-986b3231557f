<% if user_signed_in? %>
  <div data-controller="bot"
       data-bot-user-id-value="<%= current_user.id %>"
       data-bot-session-context-value='<%= {}.to_json %>'
       data-bot-minimized-class="translate-y-[calc(100%-3.5rem)]"
       data-bot-hidden-class="hidden"
       class="fixed bottom-0 right-0 z-50">
    
    <!-- Open Button (shown when widget is closed) -->
    <button data-bot-target="openButton"
            data-action="click->bot#open"
            class="hidden fixed bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-4 shadow-lg transition-all duration-200 hover:scale-110">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
      </svg>
      <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">!</span>
    </button>
    
    <!-- Chat Window -->
    <div data-bot-target="chatWindow"
         class="flex flex-col bg-white dark:bg-gray-800 rounded-t-lg shadow-2xl transition-transform duration-300 w-96 h-[32rem] mr-4 mb-0">
      
      <!-- Header -->
      <div class="flex items-center justify-between bg-blue-500 text-white px-4 py-3 rounded-t-lg">
        <div class="flex items-center space-x-3">
          <div class="relative">
            <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
          </div>
          <div>
            <h3 class="font-semibold">Linklysis Assistant</h3>
            <p class="text-xs opacity-90">Always here to help</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <button data-bot-target="minimizeButton"
                  data-action="click->bot#toggleMinimize"
                  class="hover:bg-blue-600 rounded p-1 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
          <button data-bot-target="closeButton"
                  data-action="click->bot#close"
                  class="hover:bg-blue-600 rounded p-1 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Messages Container -->
      <div data-bot-target="messageContainer"
           class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
        <!-- Messages will be inserted here -->
      </div>
      
      <!-- Typing Indicator -->
      <div data-bot-target="typingIndicator"
           class="hidden px-4 py-2 bg-gray-50 dark:bg-gray-900">
        <div class="flex items-center space-x-2">
          <div class="flex space-x-1">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
          </div>
          <span class="text-xs text-gray-500">Linklysis is typing...</span>
        </div>
      </div>
      
      <!-- Suggestions Container -->
      <div data-bot-target="suggestionsContainer"
           class="hidden px-4 py-2 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <p class="text-xs text-gray-500 mb-2">Suggested questions:</p>
        <div class="flex flex-wrap gap-2">
          <!-- Suggestions will be inserted here -->
        </div>
      </div>
      
      <!-- Input Area -->
      <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-3 bg-white dark:bg-gray-800">
        <form data-action="submit->bot#sendMessage" class="flex items-end space-x-2">
          <textarea data-bot-target="messageInput"
                    data-action="keydown->bot#handleKeydown input->bot#handleInput"
                    placeholder="Type your message..."
                    rows="1"
                    class="flex-1 resize-none rounded-lg border border-gray-300 dark:border-gray-600 
                           px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 
                           dark:bg-gray-700 dark:text-white"
                    style="max-height: 100px;"></textarea>
          <button data-bot-target="sendButton"
                  type="submit"
                  class="bg-blue-500 hover:bg-blue-600 text-white rounded-lg px-4 py-2 
                         transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
            </svg>
          </button>
        </form>
      </div>
    </div>
  </div>

  <style>
    @keyframes slide-in-right {
      from {
        opacity: 0;
        transform: translateX(1rem);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
    
    @keyframes slide-in-left {
      from {
        opacity: 0;
        transform: translateX(-1rem);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
    
    .animate-slide-in-right {
      animation: slide-in-right 0.3s ease-out;
    }
    
    .animate-slide-in-left {
      animation: slide-in-left 0.3s ease-out;
    }
  </style>
<% end %>