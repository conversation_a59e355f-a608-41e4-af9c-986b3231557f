<div class="bot-chat-interface">
  <h1 class="text-2xl font-bold mb-4">Chat with Linklysis Assistant</h1>
  
  <div id="chat-messages" class="bg-gray-50 p-4 rounded-lg min-h-96 mb-4">
    <% if @recent_messages.any? %>
      <% @recent_messages.each do |message| %>
        <div class="message mb-2">
          <strong><%= message[:role] == 'user' ? 'You' : 'Linklysis' %>:</strong>
          <%= message[:content] %>
        </div>
      <% end %>
    <% else %>
      <p class="text-gray-500">Start a conversation with the Linklysis assistant...</p>
    <% end %>
  </div>
  
  <div class="chat-input">
    <%= form_with url: chat_path, method: :post, local: false, id: "chat-form" do |f| %>
      <div class="flex gap-2">
        <%= f.text_field :message, placeholder: "Type your message...", class: "flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500" %>
        <%= f.submit "Send", class: "px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700" %>
      </div>
    <% end %>
  </div>
</div>

<script>
// Basic chat functionality for the test template
document.getElementById('chat-form').addEventListener('submit', function(e) {
  e.preventDefault();
  // This would normally handle the chat submission
});
</script>