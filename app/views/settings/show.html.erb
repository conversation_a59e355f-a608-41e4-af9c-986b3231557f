<% content_for :title, "Settings - LinkMaster" %>
<% content_for :page_title, "Settings" %>
<% content_for :page_subtitle, "Manage your account preferences and configurations" %>

<% content_for :breadcrumbs do %>
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <%= link_to authenticated_root_path, class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
          </svg>
        <% end %>
      </li>
      <li class="flex items-center">
        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="ml-4 text-sm font-medium text-gray-900">Settings</span>
      </li>
      <% if @current_section && @current_section != 'account' %>
        <li class="flex items-center">
          <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span class="ml-4 text-sm font-medium text-purple-600 capitalize"><%= @current_section.humanize %></span>
        </li>
      <% end %>
    </ol>
  </nav>
<% end %>

<% content_for :header_actions do %>
  <div class="flex items-center space-x-3">
    <button disabled class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-400 rounded-xl cursor-not-allowed transition-all duration-200 text-sm font-medium shadow-sm">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      Export Settings (Coming Soon)
    </button>
    <button disabled class="inline-flex items-center px-4 py-2 bg-gray-400 text-white rounded-xl cursor-not-allowed transition-all duration-200 text-sm font-semibold shadow-lg">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
      </svg>
      Save All (Coming Soon)
    </button>
  </div>
<% end %>

<div class="p-6" data-controller="settings">

  <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
    <!-- Enhanced Sidebar Navigation -->
    <div class="lg:col-span-1">
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-4">
        <div class="mb-4">
          <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wider">Settings</h3>
          <p class="text-xs text-gray-500 mt-1">Configure your account</p>
        </div>

        <nav class="space-y-2" data-settings-target="navigation">
          <!-- Account -->
          <%= link_to settings_path(section: 'account'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 border border-purple-200 shadow-sm' if @current_section == 'account'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'account'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'account' ? 'text-purple-700' : 'text-gray-900 group-hover:text-gray-900' %>">Account</span>
              <p class="text-xs <%= @current_section == 'account' ? 'text-purple-600' : 'text-gray-500' %>">Personal information</p>
            </div>
            <% if @current_section == 'account' %>
              <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- Team -->
          <%= link_to settings_path(section: 'team'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 border border-blue-200 shadow-sm' if @current_section == 'team'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'team'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'team' ? 'text-blue-700' : 'text-gray-900 group-hover:text-gray-900' %>">Team</span>
              <p class="text-xs <%= @current_section == 'team' ? 'text-blue-600' : 'text-gray-500' %>">Collaboration settings</p>
            </div>
            <% if @current_section == 'team' %>
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- API Tokens -->
          <%= link_to settings_path(section: 'api_tokens'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-orange-50 to-orange-100 text-orange-700 border border-orange-200 shadow-sm' if @current_section == 'api_tokens'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'api_tokens'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'api_tokens' ? 'text-orange-700' : 'text-gray-900 group-hover:text-gray-900' %>">API Tokens</span>
              <p class="text-xs <%= @current_section == 'api_tokens' ? 'text-orange-600' : 'text-gray-500' %>">API access keys</p>
            </div>
            <% if @current_section == 'api_tokens' %>
              <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- Billing -->
          <%= link_to settings_path(section: 'billing'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-yellow-50 to-yellow-100 text-yellow-700 border border-yellow-200 shadow-sm' if @current_section == 'billing'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'billing'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'billing' ? 'text-yellow-700' : 'text-gray-900 group-hover:text-gray-900' %>">Billing</span>
              <p class="text-xs <%= @current_section == 'billing' ? 'text-yellow-600' : 'text-gray-500' %>">Plans & payments</p>
            </div>
            <% if @current_section == 'billing' %>
              <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- Notifications -->
          <%= link_to settings_path(section: 'notifications'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-violet-50 to-violet-100 text-violet-700 border border-violet-200 shadow-sm' if @current_section == 'notifications'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'notifications'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-violet-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'notifications' ? 'text-violet-700' : 'text-gray-900 group-hover:text-gray-900' %>">Notifications</span>
              <p class="text-xs <%= @current_section == 'notifications' ? 'text-violet-600' : 'text-gray-500' %>">Alerts & emails</p>
            </div>
            <% if @current_section == 'notifications' %>
              <div class="w-2 h-2 bg-violet-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- Security -->
          <%= link_to settings_path(section: 'security'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-red-50 to-red-100 text-red-700 border border-red-200 shadow-sm' if @current_section == 'security'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'security'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'security' ? 'text-red-700' : 'text-gray-900 group-hover:text-gray-900' %>">Security</span>
              <p class="text-xs <%= @current_section == 'security' ? 'text-red-600' : 'text-gray-500' %>">Password & 2FA</p>
            </div>
            <% if @current_section == 'security' %>
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- Domains -->
          <%= link_to settings_path(section: 'domains'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-teal-50 to-teal-100 text-teal-700 border border-teal-200 shadow-sm' if @current_section == 'domains'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'domains'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'domains' ? 'text-teal-700' : 'text-gray-900 group-hover:text-gray-900' %>">Domains</span>
              <p class="text-xs <%= @current_section == 'domains' ? 'text-teal-600' : 'text-gray-500' %>">Custom domains</p>
            </div>
            <% if @current_section == 'domains' %>
              <div class="w-2 h-2 bg-teal-500 rounded-full"></div>
            <% end %>
          <% end %>

          <!-- Integrations -->
          <%= link_to settings_path(section: 'integrations'),
                      class: "group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 #{'bg-gradient-to-r from-pink-50 to-pink-100 text-pink-700 border border-pink-200 shadow-sm' if @current_section == 'integrations'} #{'hover:bg-gray-50 hover:shadow-sm text-gray-700' unless @current_section == 'integrations'}" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
              </svg>
            </div>
            <div class="flex-1">
              <span class="font-semibold <%= @current_section == 'integrations' ? 'text-pink-700' : 'text-gray-900 group-hover:text-gray-900' %>">Integrations</span>
              <p class="text-xs <%= @current_section == 'integrations' ? 'text-pink-600' : 'text-gray-500' %>">Third-party apps</p>
            </div>
            <% if @current_section == 'integrations' %>
              <div class="w-2 h-2 bg-pink-500 rounded-full"></div>
            <% end %>
          <% end %>
        </nav>
      </div>
    </div>

    <!-- Enhanced Main Content -->
    <div class="lg:col-span-3">
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200 min-h-[600px] hover:shadow-lg transition-all duration-300">
        <!-- Content Header -->
        <div class="border-b border-gray-200 px-8 py-6">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-bold text-gray-900 capitalize">
                <%= @current_section&.humanize || 'Account' %> Settings
              </h2>
              <p class="text-sm text-gray-600 mt-1">
                <% case @current_section %>
                <% when 'account' %>
                  Manage your personal information and account preferences
                <% when 'team' %>
                  Configure team settings and member permissions
                <% when 'api_tokens' %>
                  Create and manage API access tokens
                <% when 'billing' %>
                  View and manage your subscription and billing information
                <% when 'notifications' %>
                  Control how and when you receive notifications
                <% when 'security' %>
                  Secure your account with password and two-factor authentication
                <% when 'domains' %>
                  Add and manage custom domains for your links
                <% when 'integrations' %>
                  Connect third-party services and applications
                <% else %>
                  Manage your personal information and account preferences
                <% end %>
              </p>
            </div>

            <!-- Section-specific actions -->
            <div class="flex items-center space-x-3">
              <% case @current_section %>
              <% when 'api_tokens' %>
                <button disabled class="inline-flex items-center px-3 py-2 border border-gray-300 text-gray-400 rounded-lg cursor-not-allowed transition-colors text-sm font-medium">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  New Token (Coming Soon)
                </button>
              <% when 'domains' %>
                <button disabled class="inline-flex items-center px-3 py-2 border border-gray-300 text-gray-400 rounded-lg cursor-not-allowed transition-colors text-sm font-medium">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add Domain (Coming Soon)
                </button>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Content Body -->
        <div class="p-8">
          <% case @current_section %>
          <% when 'account' %>
            <%= render 'settings/sections/account', user: @user %>
          <% when 'team' %>
            <%= render 'settings/sections/team', user: @user %>
          <% when 'api_tokens' %>
            <%= render 'settings/sections/api_tokens', user: @user %>
          <% when 'billing' %>
            <%= render 'settings/sections/billing', user: @user %>
          <% when 'notifications' %>
            <%= render 'settings/sections/notifications', user: @user %>
          <% when 'security' %>
            <%= render 'settings/sections/security', user: @user %>
          <% when 'domains' %>
            <%= render 'settings/sections/domains', user: @user %>
          <% when 'integrations' %>
            <%= render 'settings/sections/integrations', user: @user %>
          <% else %>
            <%= render 'settings/sections/account', user: @user %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>