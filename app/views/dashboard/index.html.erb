<% content_for :title, "Dashboard" %>

<div class="p-6">
  <!-- Welcome Section -->
  <div class="mb-8">
    <h1 class="text-2xl font-bold text-gray-900">Welcome back, <%= current_user.display_name %>!</h1>
    <p class="text-gray-600 mt-1">Here's what's happening with your links today.</p>
  </div>

  <!-- Quick Stats -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Links -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL LINKS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_links) %></div>
      <p class="text-sm text-gray-500"><%= @active_links %> active links</p>
    </div>

    <!-- Total Clicks -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL CLICKS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_clicks) %></div>
      <p class="text-sm text-gray-500"><%= @clicks_today %> clicks today</p>
    </div>

    <!-- This Week's Clicks -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">THIS WEEK</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-green-500 to-green-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@clicks_this_week) %></div>
      <p class="text-sm text-gray-500">Weekly clicks</p>
    </div>

    <!-- Click Rate -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-amber-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">CLICK RATE</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-amber-500 to-amber-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1">
        <%= @total_links > 0 ? number_to_percentage(@total_clicks.to_f / @total_links * 100, precision: 1) : '0%' %>
      </div>
      <p class="text-sm text-gray-500">Average engagement</p>
    </div>
  </div>

  <!-- Recent Links & Top Performing -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Links -->
    <div class="bg-white rounded-2xl border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Recent Links</h2>
          <p class="text-sm text-gray-600">Your latest shortened links</p>
        </div>
        <%= link_to "View All", links_path, class: "text-sm text-purple-600 hover:text-purple-700 font-medium" %>
      </div>
      
      <div class="space-y-3">
        <% if @recent_links.any? %>
          <% @recent_links.each do |link| %>
            <%= link_to link, class: "block" do %>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group">
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate group-hover:text-purple-600">
                    <%= link.title || link.original_url %>
                  </p>
                  <div class="flex items-center mt-1 text-xs text-gray-500">
                    <span class="truncate"><%= link.short_url(request) %></span>
                    <span class="mx-2">•</span>
                    <span><%= pluralize(link.link_clicks_count, 'click') %></span>
                  </div>
                </div>
                <div class="ml-4 text-gray-400 group-hover:text-purple-600">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </div>
            <% end %>
          <% end %>
        <% else %>
          <p class="text-center text-gray-500 py-8">No links created yet</p>
        <% end %>
      </div>
    </div>

    <!-- Top Performing Links -->
    <div class="bg-white rounded-2xl border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Top Performing</h2>
          <p class="text-sm text-gray-600">Links with the most clicks</p>
        </div>
        <%= link_to "Analytics", analytics_path, class: "text-sm text-purple-600 hover:text-purple-700 font-medium" %>
      </div>
      
      <div class="space-y-3">
        <% if @top_links.any? %>
          <% @top_links.each_with_index do |link, index| %>
            <%= link_to link, class: "block" do %>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group">
                <div class="flex items-center flex-1 min-w-0">
                  <span class="flex items-center justify-center w-7 h-7 bg-gradient-to-br from-purple-500 to-purple-600 text-white text-xs font-bold rounded-full mr-3">
                    <%= index + 1 %>
                  </span>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate group-hover:text-purple-600">
                      <%= link.title || link.original_url %>
                    </p>
                    <span class="text-xs text-gray-500 truncate"><%= link.short_url(request) %></span>
                  </div>
                </div>
                <div class="ml-4 text-right">
                  <p class="text-sm font-bold text-gray-900 group-hover:text-purple-600"><%= number_with_delimiter(link.link_clicks_count) %></p>
                  <p class="text-xs text-gray-500">clicks</p>
                </div>
              </div>
            <% end %>
          <% end %>
        <% else %>
          <p class="text-center text-gray-500 py-8">No clicks yet</p>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mt-8 bg-gradient-to-r from-purple-600 to-purple-700 rounded-2xl p-6 text-white">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-bold mb-1">Ready to create more links?</h3>
        <p class="text-purple-100">Shorten URLs, track clicks, and grow your audience.</p>
      </div>
      <%= link_to new_link_path, class: "inline-flex items-center px-4 py-2 bg-white text-purple-600 font-medium rounded-lg hover:bg-purple-50 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Create New Link
      <% end %>
    </div>
  </div>
</div>