<% content_for :page_title, "Import Details" %>

<% content_for :breadcrumbs do %>
  <nav class="text-sm">
    <ol class="flex items-center space-x-2">
      <li>
        <%= link_to dashboard_path, class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
          Dashboard
        <% end %>
      </li>
      <li class="text-gray-400">/</li>
      <li>
        <%= link_to bulk_imports_path, class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
          Bulk Imports
        <% end %>
      </li>
      <li class="text-gray-400">/</li>
      <li class="text-gray-700 font-medium">Import #<%= @bulk_import.id %></li>
    </ol>
  </nav>
<% end %>

<% content_for :header_actions do %>
  <div class="flex items-center space-x-3">
    <% if @bulk_import.failed_rows > 0 && @bulk_import.error_details['rows'].present? %>
      <%= link_to download_errors_bulk_import_path(@bulk_import), class: "inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Download Errors
      <% end %>
    <% end %>
    
    <%= link_to new_bulk_import_path, class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-semibold shadow-sm" do %>
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      New Import
    <% end %>
  </div>
<% end %>

<div class="px-6">
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Import Overview -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Import Overview</h2>
        </div>
        <div class="p-6">
          <!-- Status Badge -->
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-sm font-medium text-gray-500">Status</h3>
              <div class="mt-1">
                <% case @bulk_import.status %>
                <% when 'pending' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    Pending
                  </span>
                <% when 'processing' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <svg class="animate-spin w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing
                  </span>
                <% when 'completed' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Completed
                  </span>
                <% when 'failed' %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    Failed
                  </span>
                <% end %>
              </div>
            </div>
            <% if @bulk_import.processing? %>
              <button type="button" class="text-sm text-gray-500 hover:text-gray-700" data-controller="refresh" data-action="click->refresh#reload">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
            <% end %>
          </div>
          
          <!-- Progress Bar -->
          <div class="mb-6">
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-gray-600">Progress</span>
              <span class="text-gray-900 font-medium">
                <%= @bulk_import.processed_rows %> / <%= @bulk_import.total_rows %> rows
                (<%= @bulk_import.total_rows > 0 ? "#{((@bulk_import.processed_rows.to_f / @bulk_import.total_rows) * 100).round}%" : "0%" %>)
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div class="bg-blue-600 h-3 rounded-full transition-all duration-500" 
                   style="width: <%= @bulk_import.total_rows > 0 ? ((@bulk_import.processed_rows.to_f / @bulk_import.total_rows) * 100).round : 0 %>%"></div>
            </div>
          </div>
          
          <!-- Statistics Grid -->
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-gray-600">Successful</p>
                  <p class="text-2xl font-semibold text-green-600"><%= @bulk_import.successful_rows %></p>
                </div>
                <div class="bg-green-100 rounded-full p-2">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-gray-600">Failed</p>
                  <p class="text-2xl font-semibold text-red-600"><%= @bulk_import.failed_rows %></p>
                </div>
                <div class="bg-red-100 rounded-full p-2">
                  <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Error Details -->
      <% if @bulk_import.error_details['rows'].present? %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">Import Errors</h2>
            <span class="text-sm text-gray-500">
              <%= pluralize(@bulk_import.error_details['rows'].count, 'error') %>
            </span>
          </div>
          <div class="max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Row
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Error Message
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% @bulk_import.error_details['rows'].first(20).each do |row_num, error| %>
                  <tr>
                    <td class="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                      Row <%= row_num %>
                    </td>
                    <td class="px-6 py-3 text-sm text-red-600">
                      <%= error %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <% if @bulk_import.error_details['rows'].count > 20 %>
            <div class="px-6 py-3 bg-gray-50 text-sm text-gray-600 text-center">
              Showing first 20 errors. 
              <%= link_to "Download all errors", download_errors_bulk_import_path(@bulk_import), class: "text-blue-600 hover:text-blue-800 font-medium" %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    
    <!-- Sidebar -->
    <div class="lg:col-span-1 space-y-6">
      <!-- Import Details -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Details</h2>
        </div>
        <div class="p-6 space-y-4">
          <div>
            <p class="text-sm text-gray-500">File Name</p>
            <p class="text-sm font-medium text-gray-900 mt-1">
              <%= @bulk_import.file_name || "Import ##{@bulk_import.id}" %>
            </p>
          </div>
          
          <% if @bulk_import.team %>
            <div>
              <p class="text-sm text-gray-500">Team</p>
              <p class="text-sm font-medium text-gray-900 mt-1">
                <%= @bulk_import.team.name %>
              </p>
            </div>
          <% end %>
          
          <div>
            <p class="text-sm text-gray-500">Started At</p>
            <p class="text-sm font-medium text-gray-900 mt-1">
              <%= @bulk_import.created_at.strftime("%B %d, %Y at %I:%M %p") %>
            </p>
          </div>
          
          <% if @bulk_import.completed_at %>
            <div>
              <p class="text-sm text-gray-500">Completed At</p>
              <p class="text-sm font-medium text-gray-900 mt-1">
                <%= @bulk_import.completed_at.strftime("%B %d, %Y at %I:%M %p") %>
              </p>
            </div>
          <% end %>
          
          <% if @bulk_import.duration %>
            <div>
              <p class="text-sm text-gray-500">Duration</p>
              <p class="text-sm font-medium text-gray-900 mt-1">
                <%= @bulk_import.formatted_duration %>
              </p>
            </div>
          <% end %>
          
          <div>
            <p class="text-sm text-gray-500">Success Rate</p>
            <p class="text-sm font-medium text-gray-900 mt-1">
              <%= @bulk_import.success_rate %>%
            </p>
          </div>
        </div>
      </div>
      
      <!-- Recent Imports -->
      <% if @recent_imports.any? %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Other Recent Imports</h2>
          </div>
          <div class="divide-y divide-gray-200">
            <% @recent_imports.each do |import| %>
              <%= link_to bulk_import_path(import), class: "block px-6 py-3 hover:bg-gray-50 transition-colors" do %>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      <%= import.file_name || "Import ##{import.id}" %>
                    </p>
                    <p class="text-xs text-gray-500 mt-0.5">
                      <%= import.created_at.strftime("%b %d at %I:%M %p") %>
                    </p>
                  </div>
                  <div class="text-right">
                    <% case import.status %>
                    <% when 'completed' %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                        <%= import.successful_rows %> links
                      </span>
                    <% when 'processing' %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        Processing
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        <%= import.status.capitalize %>
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<% if @bulk_import.processing? %>
  <script>
    // Auto-refresh page every 5 seconds while processing
    setTimeout(function() {
      window.location.reload();
    }, 5000);
  </script>
<% end %>