<% content_for :title, "New Bulk Import - LinkMaster" %>

<!-- Smooth scroll behavior -->
<style>
  html {
    scroll-behavior: smooth;
  }

  .animate-pulse-delay {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    animation-delay: 1s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
</style>

<!-- Enhanced Hero Section -->
<div class="relative bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 overflow-hidden">
  <!-- Background pattern -->
  <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cpattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"%3E%3Cpath d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\"100%25\" height=\"100%25\" fill=\"url(%23grid)\"/%3E%3C/svg%3E')] opacity-20"></div>

  <!-- Floating gradient orbs -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-green-500 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-72 h-72 bg-emerald-500 rounded-full filter blur-3xl opacity-30 animate-pulse-delay"></div>
  </div>

  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to dashboard_path, class: "text-white/60 hover:text-white transition-colors" do %>
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
          <% end %>
        </li>
        <li class="flex items-center">
          <svg class="w-5 h-5 text-white/40" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          <%= link_to bulk_imports_path, class: "ml-4 text-white/60 hover:text-white transition-colors" do %>
            Bulk Imports
          <% end %>
        </li>
        <li class="flex items-center">
          <svg class="w-5 h-5 text-white/40" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span class="ml-4 text-white font-medium">New Import</span>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="text-center">
      <div class="inline-flex items-center space-x-3 mb-6">
        <div class="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center shadow-xl">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
        </div>
      </div>
      <h1 class="text-5xl font-bold text-white mb-4">New Bulk Import</h1>
      <p class="text-xl text-white/80 mb-2">Import thousands of links at once from a CSV file</p>
      <div class="inline-flex items-center space-x-2 text-white/60">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        <span>Fast, reliable, and secure</span>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Content Section -->
<div class="bg-gray-50 py-16">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Import Steps Guide -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">How It Works</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">1. Prepare CSV</h3>
          <p class="text-gray-600">Download our template or format your CSV with URL, title, and tags columns</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">2. Upload File</h3>
          <p class="text-gray-600">Drag and drop your CSV file or click to browse and select from your computer</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">3. Import Links</h3>
          <p class="text-gray-600">We'll process your file and create shortened links for all valid URLs automatically</p>
        </div>
      </div>
    </div>

    <!-- Main Import Form -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <h2 class="text-2xl font-bold text-gray-900 flex items-center">
          <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          Import Links from CSV
        </h2>
        <p class="mt-2 text-gray-600">Upload a CSV file to bulk import multiple links at once. Support for up to 10,000 links per file.</p>
      </div>
    
      <%= form_with url: bulk_imports_path, local: true, html: { multipart: true, class: "px-8 py-8", data: { controller: "form-validation" } } do |f| %>
        <% if flash[:alert] %>
          <div class="mb-8 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Upload Error</h3>
                <div class="mt-1 text-sm text-red-700">
                  <%= flash[:alert] %>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="space-y-8">
          <!-- Enhanced File Upload -->
          <div>
            <label for="file" class="block text-lg font-semibold text-gray-900 mb-4">
              <svg class="w-5 h-5 text-green-600 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Choose Your CSV File
            </label>
            <div class="relative">
              <div class="flex justify-center px-8 pt-8 pb-8 border-2 border-dashed border-gray-300 rounded-2xl hover:border-green-400 hover:bg-green-50 transition-all duration-300 group"
                   data-action="dragover->form-validation#handleDragOver
                               dragleave->form-validation#handleDragLeave
                               drop->form-validation#handleDrop">
                <div class="space-y-4 text-center">
                  <div class="w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center mx-auto group-hover:from-green-200 group-hover:to-emerald-200 transition-colors">
                    <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                  </div>
                  <div>
                    <label for="file" class="cursor-pointer">
                      <span class="text-xl font-semibold text-gray-900 hover:text-green-600 transition-colors">
                        Click to upload
                      </span>
                      <span class="text-gray-600"> or drag and drop your CSV file here</span>
                      <%= file_field_tag :file,
                                         accept: ".csv",
                                         class: "sr-only",
                                         data: {
                                           action: "change->form-validation#validateFile",
                                           "form-validation-target": "fileInput"
                                         } %>
                    </label>
                  </div>
                  <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                      CSV files only
                    </div>
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                      </svg>
                      Up to 10MB
                    </div>
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                      10,000 links max
                    </div>
                  </div>
                </div>
              </div>
              <div class="mt-3 text-sm text-gray-600" data-form-validation-target="fileInfo"></div>
            </div>
          </div>
        
        <!-- Team Selection (if applicable) -->
        <% if @teams&.any? %>
          <div>
            <label for="team_id" class="block text-sm font-medium text-gray-700 mb-2">
              Import to Team (Optional)
            </label>
            <%= select_tag :team_id, 
                           options_for_select([["Personal Links", ""]] + @teams.map { |t| [t.name, t.id] }),
                           class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" %>
            <p class="mt-1 text-sm text-gray-500">
              Leave blank to import to your personal links
            </p>
          </div>
        <% end %>

          <!-- Enhanced CSV Format Instructions -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              CSV Format Requirements
            </h3>
            <p class="text-gray-700 mb-4">
              Your CSV file should include the following columns for optimal import results:
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <span class="w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-xs font-bold mr-2">!</span>
                  Required Column
                </h4>
                <div class="flex items-start">
                  <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800 mr-2">url</code>
                  <span class="text-sm text-gray-600">The URL to shorten (must be valid HTTP/HTTPS)</span>
                </div>
              </div>
              <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <span class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-bold mr-2">?</span>
                  Optional Columns
                </h4>
                <div class="space-y-2 text-sm">
                  <div class="flex items-start">
                    <code class="bg-gray-100 px-2 py-1 rounded text-xs font-mono text-gray-800 mr-2">title</code>
                    <span class="text-gray-600">Custom title for the link</span>
                  </div>
                  <div class="flex items-start">
                    <code class="bg-gray-100 px-2 py-1 rounded text-xs font-mono text-gray-800 mr-2">tags</code>
                    <span class="text-gray-600">Comma-separated tags</span>
                  </div>
                  <div class="flex items-start">
                    <code class="bg-gray-100 px-2 py-1 rounded text-xs font-mono text-gray-800 mr-2">expires_at</code>
                    <span class="text-gray-600">Expiration date (YYYY-MM-DD)</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg p-3 border border-blue-200">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <p class="text-sm font-medium text-blue-900">Alternative Column Names</p>
                  <p class="text-sm text-blue-700">We also accept: <code class="bg-blue-100 px-1 rounded">original_url</code>, <code class="bg-blue-100 px-1 rounded">long_url</code>, <code class="bg-blue-100 px-1 rounded">destination</code></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Template Download -->
          <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-6 text-white">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <div>
                  <h4 class="text-lg font-semibold mb-1">Need a template?</h4>
                  <p class="text-green-100">Download our sample CSV file with proper formatting and example data</p>
                </div>
              </div>
              <%= link_to download_template_bulk_imports_path, class: "inline-flex items-center px-6 py-3 bg-white text-green-600 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg" do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download Template
              <% end %>
            </div>
          </div>
      </div>
      
      <!-- Enhanced Submit Section -->
      <div class="bg-gray-50 rounded-2xl p-6 mt-8">
        <div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span>Files are processed securely and deleted after import</span>
          </div>
          <div class="flex items-center space-x-4">
            <%= link_to "Cancel", bulk_imports_path, class: "px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors font-medium" %>
            <button type="submit"
                    class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    data-form-validation-target="submitButton">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              Start Import
            </button>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  
  <!-- Recent Imports -->
  <% if current_user.bulk_imports.recent.limit(5).any? %>
    <div class="mt-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Imports</h3>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 divide-y divide-gray-200">
        <% current_user.bulk_imports.recent.limit(5).each do |import| %>
          <div class="px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <% case import.status %>
                <% when 'completed' %>
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                <% when 'processing' %>
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="animate-spin w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                <% else %>
                  <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                <% end %>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">
                  <%= import.file_name || "Import ##{import.id}" %>
                </p>
                <p class="text-xs text-gray-500">
                  <%= import.created_at.strftime("%b %d at %I:%M %p") %> • 
                  <%= pluralize(import.successful_rows, 'link') %> imported
                </p>
              </div>
            </div>
            <%= link_to "View", bulk_import_path(import), class: "text-sm text-blue-600 hover:text-blue-800 font-medium" %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<script>
  // Add drag and drop functionality
  document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.querySelector('[data-action*="dragover"]');
    const fileInput = document.querySelector('input[type="file"]');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
      dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
      dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight(e) {
      dropZone.classList.add('border-blue-500', 'bg-blue-50');
    }
    
    function unhighlight(e) {
      dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    }
    
    dropZone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;
      
      if (files.length > 0) {
        fileInput.files = files;
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
      }
    }
  });
</script>

<!-- Simple Footer -->
<div class="text-center mt-16 pt-8 border-t border-gray-200">
  <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
    <span>Secure file processing with automatic cleanup</span>
    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Service is operational"></div>
    <span>Service operational</span>
  </div>
</div>

<!-- Floating Back to Top Button -->
<div class="fixed bottom-8 right-8 z-50">
  <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="w-12 h-12 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
    </svg>
  </button>
</div>