<% content_for :title, "Terms of Service - LinkMaster" %>

<!-- Smooth scroll behavior -->
<style>
  html {
    scroll-behavior: smooth;
  }

  .animate-pulse-delay {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    animation-delay: 1s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
</style>

<!-- Enhanced Hero Section -->
<div class="relative bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
  <!-- Background pattern -->
  <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cpattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"%3E%3Cpath d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\"100%25\" height=\"100%25\" fill=\"url(%23grid)\"/%3E%3C/svg%3E')] opacity-20"></div>

  <!-- Floating gradient orbs -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-purple-500 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-72 h-72 bg-pink-500 rounded-full filter blur-3xl opacity-30 animate-pulse-delay"></div>
  </div>

  <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to root_path, class: "text-white/60 hover:text-white transition-colors" do %>
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
          <% end %>
        </li>
        <li class="flex items-center">
          <svg class="w-5 h-5 text-white/40" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span class="ml-4 text-white font-medium">Terms of Service</span>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="text-center">
      <div class="inline-flex items-center space-x-3 mb-6">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
        </div>
      </div>
      <h1 class="text-5xl font-bold text-white mb-4">Terms of Service</h1>
      <p class="text-xl text-white/80 mb-2">Clear, fair, and transparent terms</p>
      <div class="inline-flex items-center space-x-2 text-white/60">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Last updated: <%= Date.current.strftime("%B %d, %Y") %></span>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Content Section -->
<div class="bg-gray-50 py-16">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Table of Contents -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
        </svg>
        Table of Contents
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <a href="#acceptance" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">1</span>
          <span class="text-gray-700 group-hover:text-purple-700">Acceptance of Terms</span>
        </a>
        <a href="#service" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">2</span>
          <span class="text-gray-700 group-hover:text-purple-700">Description of Service</span>
        </a>
        <a href="#accounts" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">3</span>
          <span class="text-gray-700 group-hover:text-purple-700">User Accounts</span>
        </a>
        <a href="#usage" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">4</span>
          <span class="text-gray-700 group-hover:text-purple-700">Acceptable Use</span>
        </a>
        <a href="#privacy" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">5</span>
          <span class="text-gray-700 group-hover:text-purple-700">Privacy Policy</span>
        </a>
        <a href="#liability" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">6</span>
          <span class="text-gray-700 group-hover:text-purple-700">Limitation of Liability</span>
        </a>
        <a href="#termination" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">7</span>
          <span class="text-gray-700 group-hover:text-purple-700">Termination</span>
        </a>
        <a href="#changes" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">8</span>
          <span class="text-gray-700 group-hover:text-purple-700">Changes to Terms</span>
        </a>
        <a href="#contact" class="flex items-center p-3 rounded-lg hover:bg-purple-50 transition-colors group">
          <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-purple-200">9</span>
          <span class="text-gray-700 group-hover:text-purple-700">Contact Information</span>
        </a>
      </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="p-8 lg:p-12">
          <!-- Section 1 -->
          <section id="acceptance" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">1</div>
              <h2 class="text-2xl font-bold text-gray-900">Acceptance of Terms</h2>
            </div>
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border-l-4 border-purple-500">
              <p class="text-gray-700 leading-relaxed">
                By accessing and using LinkMaster ("the Service"), you accept and agree to be bound by the terms and provisions of this agreement. If you do not agree to these terms, please do not use our service.
              </p>
            </div>
          </section>

          <!-- Section 2 -->
          <section id="service" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">2</div>
              <h2 class="text-2xl font-bold text-gray-900">Description of Service</h2>
            </div>
            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 border-l-4 border-blue-500">
              <p class="text-gray-700 leading-relaxed mb-4">
                LinkMaster is a comprehensive link shortening and analytics service that allows users to:
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700">Create shortened URLs</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700">Track link performance</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700">Access detailed analytics</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-gray-700">Manage link campaigns</span>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 3 -->
          <section id="accounts" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">3</div>
              <h2 class="text-2xl font-bold text-gray-900">User Accounts</h2>
            </div>
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-l-4 border-green-500">
              <p class="text-gray-700 leading-relaxed mb-4">
                To access certain features of the Service, you may be required to create an account. You are responsible for:
              </p>
              <ul class="space-y-2">
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-700">Maintaining the confidentiality of your account credentials</span>
                </li>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-700">All activities that occur under your account</span>
                </li>
                <li class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-700">Notifying us immediately of any unauthorized use</span>
                </li>
              </ul>
            </div>
          </section>

          <!-- Section 4 -->
          <section id="usage" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">4</div>
              <h2 class="text-2xl font-bold text-gray-900">Acceptable Use</h2>
            </div>
            <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-xl p-6 border-l-4 border-red-500">
              <p class="text-gray-700 leading-relaxed mb-4">
                You agree not to use the Service for any unlawful purposes or to conduct any unlawful activity, including but not limited to:
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-red-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  <span class="text-gray-700">Distributing malware or malicious content</span>
                </div>
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-red-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  <span class="text-gray-700">Spamming or phishing</span>
                </div>
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-red-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  <span class="text-gray-700">Violating intellectual property rights</span>
                </div>
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-red-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  <span class="text-gray-700">Engaging in fraudulent activities</span>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 5 -->
          <section id="privacy" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">5</div>
              <h2 class="text-2xl font-bold text-gray-900">Privacy Policy</h2>
            </div>
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border-l-4 border-indigo-500">
              <p class="text-gray-700 leading-relaxed">
                Your privacy is important to us. Please review our <%= link_to "Privacy Policy", privacy_policy_path, class: "text-indigo-600 hover:text-indigo-700 font-semibold underline decoration-2 underline-offset-2" %>, which also governs your use of the Service and explains how we collect, use, and protect your information.
              </p>
            </div>
          </section>

          <!-- Section 6 -->
          <section id="liability" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">6</div>
              <h2 class="text-2xl font-bold text-gray-900">Limitation of Liability</h2>
            </div>
            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border-l-4 border-yellow-500">
              <p class="text-gray-700 leading-relaxed">
                LinkMaster shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the Service. Our total liability shall not exceed the amount paid by you for the Service in the 12 months preceding the claim.
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>

    <!-- Additional Sections -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
      <!-- Section 7: Termination -->
      <section id="termination" class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">7</div>
          <h3 class="text-xl font-bold text-gray-900">Termination</h3>
        </div>
        <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border-l-4 border-orange-500">
          <p class="text-gray-700 leading-relaxed">
            We may terminate or suspend your account and access to the Service at our sole discretion, without prior notice, for conduct that we believe violates these Terms. You may also terminate your account at any time through your account settings.
          </p>
        </div>
        <div class="mt-4 flex items-center text-sm text-gray-600">
          <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
          <span>Account termination is immediate and irreversible</span>
        </div>
      </section>

      <!-- Section 8: Changes to Terms -->
      <section id="changes" class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">8</div>
          <h3 class="text-xl font-bold text-gray-900">Changes to Terms</h3>
        </div>
        <div class="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-lg p-4 border-l-4 border-teal-500">
          <p class="text-gray-700 leading-relaxed">
            We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through the Service. Continued use after changes constitutes acceptance of the new terms.
          </p>
        </div>
        <div class="mt-4 flex items-center text-sm text-gray-600">
          <svg class="w-4 h-4 text-teal-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v2H4a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <span>30-day notice period for major changes</span>
        </div>
      </section>
    </div>

    <!-- Section 9: Contact Information -->
    <section id="contact" class="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 mt-8 text-center shadow-xl">
      <div class="max-w-2xl mx-auto">
        <div class="flex items-center justify-center mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">9</div>
          <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
        </div>
        <h3 class="text-3xl font-bold text-white mb-4">Questions About Our Terms?</h3>
        <p class="text-xl text-white/90 mb-8">
          If you have any questions about these Terms of Service, we're here to help. Our legal team is available to clarify any concerns.
        </p>

        <!-- Contact Options -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div class="flex items-center justify-center mb-2">
              <svg class="w-6 h-6 text-white mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-white font-semibold">Legal Inquiries</span>
            </div>
            <p class="text-white/80 text-sm"><EMAIL></p>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div class="flex items-center justify-center mb-2">
              <svg class="w-6 h-6 text-white mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              <span class="text-white font-semibold">General Support</span>
            </div>
            <p class="text-white/80 text-sm">24/7 available</p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <%= link_to "mailto:<EMAIL>", class: "inline-flex items-center justify-center px-8 py-4 bg-white text-purple-600 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Contact Legal Team
          <% end %>
          <%= link_to contact_path, class: "inline-flex items-center justify-center px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl font-semibold hover:bg-white/20 transition-all duration-300 transform hover:scale-105" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            General Support
          <% end %>
        </div>
      </div>
    </section>

    <!-- Simple Footer -->
    <div class="text-center mt-16 pt-8 border-t border-gray-200">
      <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
        <span>Effective: <%= Date.current.strftime("%B %d, %Y") %></span>
        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Terms are current"></div>
        <span>Terms are current</span>
      </div>
    </div>
  </div>
</div>

<!-- Floating Back to Top Button -->
<div class="fixed bottom-8 right-8 z-50">
  <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
    </svg>
  </button>
</div>