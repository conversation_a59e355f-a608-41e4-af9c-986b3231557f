<% content_for :title, "Privacy Policy - LinkMaster" %>

<!-- Enhanced Hero Section -->
<div class="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 overflow-hidden">
  <!-- Background pattern -->
  <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cpattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"%3E%3Cpath d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\"100%25\" height=\"100%25\" fill=\"url(%23grid)\"/%3E%3C/svg%3E')] opacity-20"></div>

  <!-- Floating gradient orbs -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-500 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-1/4 w-72 h-72 bg-cyan-500 rounded-full filter blur-3xl opacity-30 animate-pulse animation-delay-2000"></div>
  </div>

  <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-4">
        <li>
          <%= link_to root_path, class: "text-white/60 hover:text-white transition-colors" do %>
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
          <% end %>
        </li>
        <li class="flex items-center">
          <svg class="w-5 h-5 text-white/40" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span class="ml-4 text-white font-medium">Privacy Policy</span>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="text-center">
      <div class="inline-flex items-center space-x-3 mb-6">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
      </div>
      <h1 class="text-5xl font-bold text-white mb-4">Privacy Policy</h1>
      <p class="text-xl text-white/80 mb-2">Your privacy is our priority</p>
      <div class="inline-flex items-center space-x-2 text-white/60">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Last updated: <%= Date.current.strftime("%B %d, %Y") %></span>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Content Section -->
<div class="bg-gray-50 py-16">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Privacy Highlights -->
    <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl p-8 mb-8 border border-blue-200">
      <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Privacy at a Glance</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">GDPR Compliant</h3>
          <p class="text-sm text-gray-600">Full compliance with European privacy regulations</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Minimal Data</h3>
          <p class="text-sm text-gray-600">We only collect what's necessary for our service</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Your Control</h3>
          <p class="text-sm text-gray-600">Full control over your data and privacy settings</p>
        </div>
      </div>
    </div>

    <!-- Table of Contents -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
        </svg>
        Table of Contents
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
        <a href="#collection" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors group">
          <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-blue-200">1</span>
          <span class="text-gray-700 group-hover:text-blue-700">Information We Collect</span>
        </a>
        <a href="#usage" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors group">
          <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-blue-200">2</span>
          <span class="text-gray-700 group-hover:text-blue-700">How We Use Information</span>
        </a>
        <a href="#sharing" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors group">
          <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-blue-200">3</span>
          <span class="text-gray-700 group-hover:text-blue-700">Information Sharing</span>
        </a>
        <a href="#security" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors group">
          <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-blue-200">4</span>
          <span class="text-gray-700 group-hover:text-blue-700">Data Security</span>
        </a>
        <a href="#retention" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors group">
          <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-blue-200">5</span>
          <span class="text-gray-700 group-hover:text-blue-700">Data Retention</span>
        </a>
        <a href="#rights" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors group">
          <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 group-hover:bg-blue-200">6</span>
          <span class="text-gray-700 group-hover:text-blue-700">Your Rights</span>
        </a>
      </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="p-8 lg:p-12">
          <!-- Section 1: Information We Collect -->
          <section id="collection" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">1</div>
              <h2 class="text-2xl font-bold text-gray-900">Information We Collect</h2>
            </div>
            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 border-l-4 border-blue-500 mb-6">
              <p class="text-gray-700 leading-relaxed mb-4">
                We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support.
              </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <div class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <h3 class="font-semibold text-gray-900">Account Information</h3>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• Email address</li>
                  <li>• Name</li>
                  <li>• Password (encrypted)</li>
                </ul>
              </div>
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <div class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                  <h3 class="font-semibold text-gray-900">Usage Data</h3>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• Links created</li>
                  <li>• Analytics data</li>
                  <li>• Feature usage</li>
                </ul>
              </div>
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <div class="flex items-center mb-3">
                  <svg class="w-5 h-5 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                  <h3 class="font-semibold text-gray-900">Device Information</h3>
                </div>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• IP address</li>
                  <li>• Browser type</li>
                  <li>• Device identifiers</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- Section 2: How We Use Information -->
          <section id="usage" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">2</div>
              <h2 class="text-2xl font-bold text-gray-900">How We Use Your Information</h2>
            </div>
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-l-4 border-green-500 mb-6">
              <p class="text-gray-700 leading-relaxed">
                We use the information we collect to provide, maintain, and improve our services while respecting your privacy.
              </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-start">
                <svg class="w-6 h-6 text-green-500 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">Service Provision</h3>
                  <p class="text-sm text-gray-600">Provide, maintain, and improve our services</p>
                </div>
              </div>
              <div class="flex items-start">
                <svg class="w-6 h-6 text-green-500 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">Transaction Processing</h3>
                  <p class="text-sm text-gray-600">Process transactions and send related information</p>
                </div>
              </div>
              <div class="flex items-start">
                <svg class="w-6 h-6 text-green-500 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">Communication</h3>
                  <p class="text-sm text-gray-600">Send technical notices and support messages</p>
                </div>
              </div>
              <div class="flex items-start">
                <svg class="w-6 h-6 text-green-500 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">Analytics</h3>
                  <p class="text-sm text-gray-600">Monitor and analyze trends and usage patterns</p>
                </div>
              </div>
            </div>
          </section>

          <!-- Section 3: Information Sharing -->
          <section id="sharing" class="mb-12">
            <div class="flex items-center mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white font-bold text-lg mr-4">3</div>
              <h2 class="text-2xl font-bold text-gray-900">Information Sharing</h2>
            </div>
            <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-xl p-6 border-l-4 border-red-500 mb-6">
              <div class="flex items-start">
                <svg class="w-6 h-6 text-red-500 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-2">We Do Not Sell Your Data</h3>
                  <p class="text-gray-700 leading-relaxed">
                    We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except in the limited circumstances described below.
                  </p>
                </div>
              </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <h3 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  With Your Consent
                </h3>
                <p class="text-sm text-gray-600">When you explicitly agree to share information</p>
              </div>
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <h3 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-1m-3 1l-3-1"></path>
                  </svg>
                  Legal Compliance
                </h3>
                <p class="text-sm text-gray-600">To comply with legal obligations and court orders</p>
              </div>
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <h3 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                  Safety Protection
                </h3>
                <p class="text-sm text-gray-600">To protect our rights, safety, and security</p>
              </div>
              <div class="bg-white border border-gray-200 rounded-xl p-4">
                <h3 class="font-semibold text-gray-900 mb-2 flex items-center">
                  <svg class="w-5 h-5 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                  </svg>
                  Business Transfer
                </h3>
                <p class="text-sm text-gray-600">In connection with a merger or acquisition</p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>

    <!-- Additional Sections Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
      <!-- Data Security -->
      <div id="security" class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">4</div>
          <h3 class="text-lg font-bold text-gray-900">Data Security</h3>
        </div>
        <p class="text-gray-700 text-sm leading-relaxed mb-4">
          We implement industry-standard security measures to protect your personal information.
        </p>
        <div class="space-y-2">
          <div class="flex items-center text-sm">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-600">SSL/TLS encryption</span>
          </div>
          <div class="flex items-center text-sm">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-600">Secure data centers</span>
          </div>
          <div class="flex items-center text-sm">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-600">Regular security audits</span>
          </div>
        </div>
      </div>

      <!-- Data Retention -->
      <div id="retention" class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">5</div>
          <h3 class="text-lg font-bold text-gray-900">Data Retention</h3>
        </div>
        <p class="text-gray-700 text-sm leading-relaxed mb-4">
          We retain your information only as long as necessary to provide our services.
        </p>
        <div class="bg-indigo-50 rounded-lg p-3">
          <p class="text-sm text-indigo-700 font-medium">
            You can request account deletion at any time through your settings.
          </p>
        </div>
      </div>

      <!-- Your Rights -->
      <div id="rights" class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">6</div>
          <h3 class="text-lg font-bold text-gray-900">Your Rights</h3>
        </div>
        <p class="text-gray-700 text-sm leading-relaxed mb-4">
          Under GDPR, you have comprehensive rights over your personal data.
        </p>
        <div class="space-y-2">
          <div class="flex items-center text-sm">
            <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            <span class="text-gray-600">Access your data</span>
          </div>
          <div class="flex items-center text-sm">
            <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            <span class="text-gray-600">Rectify inaccurate data</span>
          </div>
          <div class="flex items-center text-sm">
            <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            <span class="text-gray-600">Erase your data</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Cookies & Tracking Section -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mt-8">
      <div class="flex items-center mb-6">
        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center text-white mr-4">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
        </div>
        <div>
          <h3 class="text-xl font-bold text-gray-900">Cookies & Tracking</h3>
          <p class="text-gray-600">How we use cookies to enhance your experience</p>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 class="font-semibold text-gray-900 mb-3">Essential Cookies</h4>
          <p class="text-sm text-gray-600 mb-4">Required for basic site functionality and security.</p>
          <div class="bg-green-50 rounded-lg p-3">
            <p class="text-sm text-green-700">✓ Always enabled - cannot be disabled</p>
          </div>
        </div>
        <div>
          <h4 class="font-semibold text-gray-900 mb-3">Analytics Cookies</h4>
          <p class="text-sm text-gray-600 mb-4">Help us understand how you use our service.</p>
          <div class="bg-blue-50 rounded-lg p-3">
            <p class="text-sm text-blue-700">⚙️ Can be controlled in your browser settings</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Section -->
    <div class="bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl p-8 mt-8 text-center">
      <div class="max-w-2xl mx-auto">
        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">Privacy Questions?</h3>
        <p class="text-white/90 mb-6">
          We're committed to transparency. If you have any questions about our privacy practices, please reach out.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <%= link_to "mailto:<EMAIL>", class: "inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 rounded-xl font-semibold hover:bg-gray-50 transition-colors" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Contact Privacy Team
          <% end %>
          <%= link_to contact_path, class: "inline-flex items-center justify-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-xl font-semibold hover:bg-white/20 transition-colors" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            General Support
          <% end %>
        </div>
      </div>
    </div>

    <!-- Simple Footer -->
    <div class="text-center mt-16 pt-8 border-t border-gray-200">
      <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
        <span>Effective: <%= Date.current.strftime("%B %d, %Y") %></span>
        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse" title="Privacy policy is current"></div>
        <span>Privacy policy is current</span>
      </div>
    </div>
  </div>
</div>