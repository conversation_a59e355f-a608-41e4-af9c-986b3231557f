<% content_for :page_title, "Dashboard" %>
<% content_for :header_actions do %>
  <%= link_to export_analytics_path(format: :csv), class: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium flex items-center space-x-2" do %>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <span>Export Data</span>
  <% end %>
  <%= link_to new_link_path, class: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium flex items-center space-x-2", data: { turbo_frame: "new_link_modal" } do %>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
    </svg>
    <span>Create Link</span>
  <% end %>
<% end %>

<div class="p-6">
  <!-- Enhanced Welcome Banner for new users -->
  <% if current_user.links.count == 0 %>
    <div class="mb-6 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl border border-purple-100 overflow-hidden">
      <div class="relative">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-indigo-600/5"></div>
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 rounded-full blur-xl"></div>
        
        <div class="relative p-8">
          <div class="flex items-center mb-4">
            <div class="w-14 h-14 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-2xl font-bold text-gray-900 mb-1">Welcome to LinkMaster!</h3>
              <p class="text-sm text-gray-600">
                Start shortening your first link and track its performance in real-time
              </p>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <%= link_to new_link_path, class: "px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 text-sm font-medium inline-flex items-center space-x-2 shadow-lg hover:shadow-xl", data: { turbo_frame: "new_link_modal" } do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              <span>Create your first link</span>
            <% end %>
            
            <div class="flex items-center space-x-2 text-sm text-gray-600">
              <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>No credit card required</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Enhanced Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Links Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL LINKS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_links) %></div>
      <p class="text-sm text-gray-500">All time</p>
    </div>

    <!-- Total Clicks Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL CLICKS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_clicks) %></div>
      <p class="text-sm text-gray-500">Last 30 days</p>
    </div>

    <!-- Unique Visitors Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">UNIQUE VISITORS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-green-500 to-green-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@unique_visitors) %></div>
      <p class="text-sm text-gray-500">Last 30 days</p>
    </div>

    <!-- Click Rate Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-yellow-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">AVG. CLICK RATE</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-yellow-500 to-yellow-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= @avg_clicks_per_visitor.round(1) %></div>
      <p class="text-sm text-gray-500">Clicks per link</p>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Left Column -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Enhanced Geographic Distribution -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Geographic Distribution</h2>
            <p class="text-sm text-gray-600">Where your clicks are coming from</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        
        <% if @clicks_by_country.any? %>
          <div class="bg-gradient-to-br from-blue-50/50 to-indigo-50/50 rounded-xl p-4">
            <%= bar_chart @clicks_by_country.first(10), label: 'Clicks', height: '250px' %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <p class="text-sm text-gray-500">No geographic data yet</p>
          </div>
        <% end %>
      </div>

      <!-- Enhanced Click Trends -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Click Trends</h2>
            <p class="text-sm text-gray-600">Daily click activity</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
        </div>
        <div class="flex items-center justify-between mb-4">
          <p class="text-xs text-gray-500">Over the last 30 days</p>
          <select class="text-sm border border-gray-300 rounded-lg px-3 py-1 hover:border-purple-400 transition-colors">
            <option>Last 30 days</option>
            <option>Last 7 days</option>
            <option>Last 24 hours</option>
          </select>
        </div>
        <div class="bg-gradient-to-br from-purple-50/50 to-indigo-50/50 rounded-xl p-4">
          <%= line_chart @clicks_by_date, label: 'Clicks', height: '250px' %>
        </div>
      </div>

      <!-- Enhanced Device & Browser Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-amber-500/5 transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-gray-900">Device Types</h3>
            <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
          <div class="bg-gradient-to-br from-amber-50/30 to-orange-50/30 rounded-xl p-4">
            <%= doughnut_chart @clicks_by_device, height: '200px' %>
          </div>
        </div>
        
        <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-pink-500/5 transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-gray-900">Referrer Sources</h3>
            <div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>
            </div>
          </div>
          <% if @clicks_by_referrer && @clicks_by_referrer.any? %>
            <div class="bg-gradient-to-br from-pink-50/30 to-rose-50/30 rounded-xl p-4">
              <%= pie_chart @clicks_by_referrer.first(5), height: '200px' %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <p class="text-sm text-gray-500">No referrer data yet</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="space-y-6">
      <!-- Enhanced Recent Clicks -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-violet-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Recent Activity</h2>
            <p class="text-xs text-gray-600">Latest clicks</p>
          </div>
          <div class="w-10 h-10 bg-gradient-to-br from-violet-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        
        <div class="bg-gradient-to-br from-violet-50/30 to-purple-50/30 rounded-xl p-4">
          <div class="max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-violet-300 scrollbar-track-violet-50 pr-2">
            <div class="space-y-3">
              <% recent_clicks = LinkClick.joins(:link).where(link: { user: current_user }).order(created_at: :desc).limit(10) %>
              <% if recent_clicks.any? %>
                <% recent_clicks.each do |click| %>
                  <div class="flex items-start space-x-3 p-2 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
                    <div class="w-8 h-8 bg-gradient-to-br from-violet-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900">
                        <%= click.link.short_code %>
                      </p>
                      <p class="text-xs text-gray-500">
                        <%= click.tracking_data&.dig('country_code') || 'Unknown' %> • <%= time_ago_in_words(click.clicked_at) %> ago
                      </p>
                    </div>
                  </div>
                <% end %>
              <% else %>
                <p class="text-sm text-gray-500 text-center py-4">No clicks yet</p>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Quick Stats -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-emerald-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Quick Stats</h2>
            <p class="text-xs text-gray-600">Overview metrics</p>
          </div>
          <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
        
        <div class="bg-gradient-to-br from-emerald-50/30 to-teal-50/30 rounded-xl p-4">
          <div class="space-y-3">
            <div class="flex items-center justify-between p-2 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
              <span class="text-sm font-medium text-gray-700">Active Links</span>
              <span class="text-sm font-bold text-gray-900 bg-emerald-100 px-2 py-1 rounded-full"><%= current_user.links.active.count %></span>
            </div>
            <div class="flex items-center justify-between p-2 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
              <span class="text-sm font-medium text-gray-700">Archived Links</span>
              <span class="text-sm font-bold text-gray-900 bg-emerald-100 px-2 py-1 rounded-full"><%= current_user.links.archived.count %></span>
            </div>
            <div class="flex items-center justify-between p-2 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
              <span class="text-sm font-medium text-gray-700">Today's Clicks</span>
              <span class="text-sm font-bold text-gray-900 bg-emerald-100 px-2 py-1 rounded-full"><%= LinkClick.joins(:link).where(link: { user: current_user }).where('link_clicks.created_at >= ?', Date.current.beginning_of_day).count %></span>
            </div>
            <div class="flex items-center justify-between p-2 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
              <span class="text-sm font-medium text-gray-700">API Tokens</span>
              <span class="text-sm font-bold text-gray-900 bg-emerald-100 px-2 py-1 rounded-full">0</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Top Links -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-indigo-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Top Links</h2>
            <p class="text-xs text-gray-600">Best performing links</p>
          </div>
          <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
        </div>
        
        <div class="bg-gradient-to-br from-indigo-50/30 to-blue-50/30 rounded-xl p-4">
          <div class="max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-indigo-300 scrollbar-track-indigo-50 pr-2">
            <div class="space-y-3">
              <% @top_links.each do |link| %>
                <div class="p-3 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
                  <%= link_to analytic_path(link), class: "group flex items-center justify-between" do %>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-semibold text-gray-900 truncate group-hover:text-indigo-600 transition-colors">
                        <%= link.short_code %>
                      </p>
                      <p class="text-xs text-gray-500 truncate"><%= link.original_url %></p>
                    </div>
                    <div class="text-right ml-4 flex-shrink-0">
                      <p class="text-sm font-bold text-gray-900 bg-indigo-100 px-3 py-1 rounded-full"><%= number_with_delimiter(link.clicks_in_period) %></p>
                      <p class="text-xs text-gray-500 mt-1">clicks</p>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>