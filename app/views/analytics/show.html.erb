<% content_for :page_title, "Link Analytics" %>
<% content_for :header_actions do %>
  <%= link_to analytics_path, class: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium flex items-center space-x-2" do %>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>
    <span>Back</span>
  <% end %>
  <%= link_to link_path(@link), class: "px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium flex items-center space-x-2" do %>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
    </svg>
    <span>View Details</span>
  <% end %>
  <%= link_to edit_link_path(@link), class: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium flex items-center space-x-2", data: { turbo_frame: "edit_link_modal" } do %>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
    </svg>
    <span>Edit Link</span>
  <% end %>
<% end %>

<div class="p-6">
  <!-- Enhanced Link Header Card -->
  <div class="mb-8 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl border border-purple-100 overflow-hidden">
    <div class="relative">
      <!-- Background Pattern -->
      <div class="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-indigo-600/5"></div>
      <div class="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 rounded-full blur-xl"></div>
      
      <div class="relative p-8">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <!-- Link Icon and Title -->
            <div class="flex items-center mb-4">
              <div class="w-14 h-14 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-1"><%= @link.short_code %></h1>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Created <%= @link.created_at.strftime('%B %d, %Y') %>
                  <% if @link.expires_at.present? %>
                    <span class="mx-2">•</span>
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Expires <%= @link.expires_at.strftime('%B %d, %Y') %>
                  <% end %>
                </div>
              </div>
            </div>
            
            <!-- Destination URL -->
            <div class="bg-white/70 backdrop-blur rounded-xl p-4 border border-white/50">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <p class="text-xs font-medium text-gray-500 mb-1">DESTINATION URL</p>
                  <p class="text-gray-800 break-all font-medium"><%= @link.original_url %></p>
                </div>
                <button onclick="copyToClipboard('<%= @link.original_url %>')" 
                        class="ml-4 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium flex items-center space-x-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                  <span>Copy</span>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Quick Actions -->
          <div class="ml-6 flex items-center space-x-3">
            <%= link_to @link.short_url(request), target: "_blank", class: "px-4 py-2 bg-white/80 backdrop-blur border border-white/50 text-gray-700 rounded-xl hover:bg-white transition-all duration-200 text-sm font-medium flex items-center space-x-2 shadow-sm" do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              <span>Visit</span>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Clicks Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL CLICKS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@total_clicks) %></div>
      <p class="text-sm text-gray-500">Last 30 days</p>
    </div>

    <!-- Unique Visitors Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">UNIQUE VISITORS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-green-500 to-green-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@unique_visitors) %></div>
      <p class="text-sm text-gray-500">
        <% if @total_clicks > 0 %>
          <%= number_to_percentage((@unique_visitors.to_f / @total_clicks * 100), precision: 1) %> of clicks
        <% else %>
          No data yet
        <% end %>
      </p>
    </div>

    <!-- All Time Clicks Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">ALL TIME CLICKS</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@link.link_clicks_count) %></div>
      <p class="text-sm text-gray-500">Since creation</p>
    </div>

    <!-- Daily Average Card -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-orange-500/10 hover:-translate-y-1 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-1">DAILY AVERAGE</h3>
          <div class="w-8 h-1 bg-gradient-to-r from-orange-500 to-orange-400 rounded-full"></div>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
      </div>
      <div class="text-3xl font-bold text-gray-900 mb-1"><%= @daily_average_clicks %></div>
      <p class="text-sm text-gray-500">Clicks per day</p>
    </div>
  </div>

  <!-- Enhanced Charts Row -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-8">
    <!-- Click Trends Chart -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Click Trends</h2>
          <p class="text-sm text-gray-600">Daily performance over time</p>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
      </div>
      <div class="bg-gradient-to-br from-purple-50/50 to-indigo-50/50 rounded-xl p-4">
        <%= line_chart @clicks_by_date, label: 'Clicks', height: '250px' %>
      </div>
    </div>

    <!-- Hourly Distribution -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-indigo-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Hourly Activity</h2>
          <p class="text-sm text-gray-600">Peak engagement hours</p>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
      </div>
      <div class="bg-gradient-to-br from-indigo-50/50 to-blue-50/50 rounded-xl p-4">
        <%= bar_chart @clicks_by_hour, label: 'Clicks', height: '250px' %>
      </div>
    </div>
  </div>

  <!-- Compact Campaign Attribution -->
  <% if @utm_campaigns.any? || @utm_sources.any? %>
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 mb-8 hover:shadow-lg hover:shadow-emerald-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h2 class="text-lg font-bold text-gray-900 mb-1">Campaign Attribution</h2>
          <p class="text-xs text-gray-600">Traffic source breakdown</p>
        </div>
        <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
          </svg>
        </div>
      </div>
      
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <% if @utm_campaigns.any? %>
          <div class="bg-gradient-to-br from-emerald-50/30 to-teal-50/30 rounded-xl p-4">
            <h3 class="text-xs font-bold text-gray-800 mb-3 flex items-center">
              <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2"></div>
              Campaigns
            </h3>
            <div class="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-emerald-300 scrollbar-track-emerald-50 pr-1">
              <div class="space-y-2">
                <% @utm_campaigns.first(8).each do |campaign, count| %>
                  <div class="flex justify-between items-center py-1.5 px-2 bg-white/50 rounded-lg text-xs">
                    <span class="font-medium text-gray-700 truncate mr-2"><%= campaign %></span>
                    <span class="font-bold text-gray-900 bg-emerald-100 px-2 py-0.5 rounded-full whitespace-nowrap">
                      <%= number_with_delimiter(count) %>
                    </span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
        
        <% if @utm_sources.any? %>
          <div class="bg-gradient-to-br from-teal-50/30 to-cyan-50/30 rounded-xl p-4">
            <h3 class="text-xs font-bold text-gray-800 mb-3 flex items-center">
              <div class="w-1.5 h-1.5 bg-teal-500 rounded-full mr-2"></div>
              Sources
            </h3>
            <div class="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-teal-300 scrollbar-track-teal-50 pr-1">
              <div class="space-y-2">
                <% @utm_sources.first(8).each do |source, count| %>
                  <div class="flex justify-between items-center py-1.5 px-2 bg-white/50 rounded-lg text-xs">
                    <span class="font-medium text-gray-700 truncate mr-2"><%= source %></span>
                    <span class="font-bold text-gray-900 bg-teal-100 px-2 py-0.5 rounded-full whitespace-nowrap">
                      <%= number_with_delimiter(count) %>
                    </span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Enhanced Geographic and Device Data -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-8">
    <!-- Countries -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Geographic Distribution</h2>
          <p class="text-sm text-gray-600">Top countries by clicks</p>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
      
      <div class="bg-gradient-to-br from-blue-50/30 to-cyan-50/30 rounded-xl p-4">
        <div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-blue-50 pr-2">
          <div class="space-y-4">
            <% @clicks_by_country.first(15).each do |country, count| %>
              <div class="flex items-center justify-between p-3 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
                <div class="flex items-center space-x-3">
                  <span class="text-2xl"><%= country_flag(country) %></span>
                  <p class="text-sm font-semibold text-gray-900">
                    <%= country_name(country) %>
                  </p>
                </div>
                <div class="text-right">
                  <p class="text-sm font-bold text-gray-900">
                    <%= number_with_delimiter(count) %>
                  </p>
                  <p class="text-xs text-gray-500 font-medium">
                    <%= number_to_percentage(count.to_f / @total_clicks * 100, precision: 1) %>
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Devices & Browsers -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-amber-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Device Analytics</h2>
          <p class="text-sm text-gray-600">Devices, browsers & platforms</p>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
        </div>
      </div>
      
      <div class="space-y-6">
        <!-- Device Types -->
        <div class="bg-gradient-to-br from-amber-50/30 to-orange-50/30 rounded-xl p-4">
          <h3 class="text-sm font-bold text-gray-800 mb-4 flex items-center">
            <div class="w-2 h-2 bg-amber-500 rounded-full mr-2"></div>
            Device Types
          </h3>
          <div class="space-y-3">
            <% @clicks_by_device.each do |device, count| %>
              <div class="p-3 bg-white/60 backdrop-blur rounded-lg">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700"><%= device.presence || 'Unknown' %></span>
                  <span class="text-sm font-bold text-gray-900">
                    <%= number_to_percentage(count.to_f / @total_clicks * 100, precision: 1) %>
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-gradient-to-r from-amber-500 to-orange-500 h-2 rounded-full transition-all duration-500" 
                       style="width: <%= count.to_f / @total_clicks * 100 %>%"></div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Browsers -->
        <div class="bg-gradient-to-br from-orange-50/30 to-red-50/30 rounded-xl p-4">
          <h3 class="text-sm font-bold text-gray-800 mb-4 flex items-center">
            <div class="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
            Top Browsers
          </h3>
          <div class="space-y-2">
            <% @clicks_by_browser.each do |browser, count| %>
              <div class="flex justify-between items-center p-2 bg-white/60 backdrop-blur rounded-lg">
                <span class="text-sm font-medium text-gray-700"><%= browser.presence || 'Unknown' %></span>
                <span class="text-sm font-bold text-gray-900 bg-orange-100 px-2 py-1 rounded-full">
                  <%= number_with_delimiter(count) %>
                </span>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Operating Systems -->
        <div class="bg-gradient-to-br from-red-50/30 to-pink-50/30 rounded-xl p-4">
          <h3 class="text-sm font-bold text-gray-800 mb-4 flex items-center">
            <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
            Operating Systems
          </h3>
          <div class="space-y-2">
            <% @clicks_by_os.first(5).each do |os, count| %>
              <div class="flex justify-between items-center p-2 bg-white/60 backdrop-blur rounded-lg">
                <span class="text-sm font-medium text-gray-700"><%= os.presence || 'Unknown' %></span>
                <span class="text-sm font-bold text-gray-900 bg-red-100 px-2 py-1 rounded-full">
                  <%= number_with_delimiter(count) %>
                </span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Referrers and Recent Clicks -->
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
    <!-- Top Referrers -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-pink-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Top Referrers</h2>
          <p class="text-sm text-gray-600">Traffic sources</p>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
        </div>
      </div>
      
      <div class="bg-gradient-to-br from-pink-50/30 to-rose-50/30 rounded-xl p-4">
        <div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-pink-300 scrollbar-track-pink-50 pr-2">
          <div class="space-y-3">
            <% @top_referrers.each do |referrer, count| %>
              <div class="flex items-center justify-between p-3 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
                <p class="text-sm font-medium text-gray-900 truncate flex-1 mr-3">
                  <%= referrer.presence || 'Direct' %>
                </p>
                <span class="text-sm font-bold text-gray-900 bg-pink-100 px-3 py-1 rounded-full whitespace-nowrap">
                  <%= number_with_delimiter(count) %>
                </span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Clicks -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-violet-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-1">Recent Activity</h2>
          <p class="text-sm text-gray-600">Latest clicks</p>
        </div>
        <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
      
      <div class="bg-gradient-to-br from-violet-50/30 to-purple-50/30 rounded-xl p-4">
        <div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-violet-300 scrollbar-track-violet-50 pr-2">
          <div class="space-y-3">
            <% @recent_clicks.first(20).each do |click| %>
              <div class="flex items-center justify-between p-3 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-1">
                    <% if click.tracking_data&.dig('country_code').present? %>
                      <span class="text-sm"><%= country_flag(click.tracking_data['country_code']) %></span>
                      <p class="text-sm font-medium text-gray-900">
                        <%= country_name(click.tracking_data['country_code']) %>
                      </p>
                    <% else %>
                      <p class="text-sm font-medium text-gray-900">Unknown Location</p>
                    <% end %>
                  </div>
                  <p class="text-xs text-gray-500">
                    <%= click.tracking_data&.dig('device_type') || 'Unknown device' %> • 
                    <%= click.tracking_data&.dig('browser') || 'Unknown browser' %>
                  </p>
                </div>
                <div class="text-right">
                  <p class="text-xs font-medium text-gray-600">
                    <%= time_ago_in_words(click.clicked_at) %> ago
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals -->
  <%= turbo_frame_tag "edit_link_modal" %>
</div>