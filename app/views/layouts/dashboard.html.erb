<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Linklysis Dashboard" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <!-- SEO Meta Tags for Dashboard -->
    <%= seo_meta_tags(
      title: content_for(:title) || "Dashboard | Linklysis",
      description: "Manage your links, view analytics, and track performance with Linklysis dashboard.",
      robots: "noindex, nofollow"
    ) %>
    
    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
    
    <style>
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      }
    </style>
  </head>

  <body class="bg-gray-50 font-inter">
    <div class="flex h-screen">
      <!-- Enhanced Sidebar -->
      <aside class="w-16 lg:w-72 bg-white border-r border-gray-200 flex flex-col transition-all duration-300 shadow-sm">
        <!-- Logo Section with Enhanced Branding -->
        <div class="p-4 lg:p-6 border-b border-gray-200">
          <%= link_to authenticated_root_path, class: "flex items-center justify-center lg:justify-start space-x-3 group" do %>
            <div class="w-10 h-10 bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-105 transition-transform duration-200 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>
            </div>
            <div class="hidden lg:block">
              <span class="text-xl font-bold text-gray-900 group-hover:text-purple-700 transition-colors">LinkMaster</span>
              <p class="text-xs text-gray-500 font-medium">Link Management Platform</p>
            </div>
          <% end %>
        </div>

        <!-- Enhanced User Profile Section -->
        <div class="hidden lg:block px-4 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div class="px-3 py-3 bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="flex items-center space-x-3">
              <div class="relative">
                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-md">
                  <span class="text-white text-sm font-semibold"><%= current_user.display_name[0].upcase %></span>
                </div>
                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-semibold text-gray-900 truncate"><%= current_user.display_name %></p>
                <p class="text-xs text-gray-500 flex items-center gap-1">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                  </svg>
                  Free Plan
                </p>
              </div>
              <div class="relative" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
                <button data-dropdown-target="trigger" data-action="click->dropdown#toggle"
                        class="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>

                <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50" data-dropdown-target="menu" role="menu">
                  <%= link_to edit_user_registration_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Account Settings
                  <% end %>
                  <%= link_to settings_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Preferences
                  <% end %>
                  <hr class="my-2 border-gray-100">
                  <%= button_to destroy_user_session_path, method: :delete, class: "flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Sign Out
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Usage Stats -->
          <div class="mt-4 px-3">
            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <div class="flex items-center justify-between mb-3">
                <span class="text-xs font-semibold text-gray-600 uppercase tracking-wide">Usage</span>
                <span class="text-xs font-bold text-purple-600"><%= current_user.links.count %> / 1,000</span>
              </div>
              <div class="relative">
                <div class="bg-gray-100 rounded-full h-2 overflow-hidden">
                  <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full transition-all duration-500 shadow-sm"
                       style="width: <%= [(current_user.links.count.to_f / 1000 * 100), 100].min %>%"></div>
                </div>
              </div>
              <div class="mt-3 grid grid-cols-2 gap-3 text-xs">
                <div class="text-center">
                  <p class="font-semibold text-gray-900"><%= current_user.total_clicks %></p>
                  <p class="text-gray-500">Total Clicks</p>
                </div>
                <div class="text-center">
                  <p class="font-semibold text-gray-900"><%= current_user.links.active.count %></p>
                  <p class="text-gray-500">Active Links</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Navigation -->
        <nav class="flex-1 px-3 py-4 space-y-2 overflow-y-auto scrollbar-thin">
          <%= render 'shared/sidebar_nav' %>
        </nav>

        <!-- Enhanced Bottom Section -->
        <div class="p-3 lg:p-4 border-t border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div class="hidden lg:block space-y-3">
            <%= link_to "#", class: "w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-semibold rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 text-center block shadow-lg hover:shadow-xl transform hover:scale-105" do %>
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
                <span>Upgrade Plan</span>
              </div>
            <% end %>

            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
              <%= link_to api_docs_path, class: "hover:text-purple-600 transition-colors font-medium" do %>
                API
              <% end %>
              <span class="text-gray-300">•</span>
              <%= link_to "#", class: "hover:text-purple-600 transition-colors font-medium" do %>
                Status
              <% end %>
              <span class="text-gray-300">•</span>
              <%= link_to help_path, class: "hover:text-purple-600 transition-colors font-medium" do %>
                Help
              <% end %>
            </div>
          </div>

          <!-- Mobile Quick Actions -->
          <div class="lg:hidden flex justify-center">
            <div class="relative" data-controller="dropdown" data-dropdown-placement-value="top-center">
              <button data-dropdown-target="trigger" data-action="click->dropdown#toggle"
                      class="p-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </button>

              <div class="hidden absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50" data-dropdown-target="menu">
                <%= link_to links_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50", method: :post do %>
                  <svg class="w-4 h-4 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Create Link
                <% end %>
                <%= link_to analytics_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50" do %>
                  <svg class="w-4 h-4 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                  View Analytics
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </aside>

      <!-- Enhanced Main Content -->
      <main class="flex-1 flex flex-col overflow-hidden">
        <!-- Enhanced Top Bar -->
        <header class="bg-white border-b border-gray-200 shadow-sm">
          <div class="px-4 lg:px-6 py-4 flex items-center justify-between">
            <!-- Left Section: Breadcrumbs/Title -->
            <div class="flex items-center space-x-4 min-w-0 flex-1">
              <% if content_for?(:breadcrumbs) %>
                <%= yield(:breadcrumbs) %>
              <% else %>
                <div class="flex items-center space-x-3">
                  <h1 class="text-xl lg:text-2xl font-bold text-gray-900 truncate"><%= content_for(:page_title) || "Dashboard" %></h1>
                  <% if content_for?(:page_subtitle) %>
                    <span class="hidden lg:block text-sm text-gray-500 font-medium">•</span>
                    <p class="hidden lg:block text-sm text-gray-600"><%= yield(:page_subtitle) %></p>
                  <% end %>
                </div>
              <% end %>
            </div>

            <!-- Right Section: Actions & User Menu -->
            <div class="flex items-center space-x-3 lg:space-x-4">
              <!-- Quick Action Buttons -->
              <div class="hidden lg:flex items-center space-x-2">
                <%= link_to new_link_path, class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-semibold rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Create Link
                <% end %>
              </div>

              <!-- Custom Header Actions -->
              <% if content_for?(:header_actions) %>
                <%= yield(:header_actions) %>
              <% end %>

              <!-- Notification Bell -->
              <div class="relative" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
                <button data-dropdown-target="trigger" data-action="click->dropdown#toggle"
                        class="relative p-2.5 text-gray-400 hover:text-gray-600 rounded-xl hover:bg-gray-100 transition-all duration-200">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82l-.9.9a2 2 0 000 2.83L10.9 8.28a2 2 0 002.83 0l.9-.9a2 2 0 000-2.83L12.9 2.82a2 2 0 00-2.83 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                  </svg>
                  <!-- Notification Badge -->
                  <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </button>

                <div class="hidden absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50" data-dropdown-target="menu">
                  <div class="px-4 py-3 border-b border-gray-100">
                    <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                    <p class="text-xs text-gray-500">Stay updated with your link activity</p>
                  </div>
                  <div class="max-h-64 overflow-y-auto">
                    <!-- Sample notifications -->
                    <div class="px-4 py-3 hover:bg-gray-50 transition-colors">
                      <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                          </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-medium text-gray-900">Link performance spike</p>
                          <p class="text-xs text-gray-500">Your link received 50+ clicks in the last hour</p>
                          <p class="text-xs text-gray-400 mt-1">2 minutes ago</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="px-4 py-2 border-t border-gray-100">
                    <%= link_to "#", class: "text-xs text-purple-600 hover:text-purple-700 font-medium" do %>
                      View all notifications →
                    <% end %>
                  </div>
                </div>
              </div>

              <!-- Search Button -->
              <button class="p-2.5 text-gray-400 hover:text-gray-600 rounded-xl hover:bg-gray-100 transition-all duration-200"
                      data-action="click->search#open" title="Search">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>

              <!-- Mobile Menu Toggle -->
              <button class="lg:hidden p-2.5 text-gray-400 hover:text-gray-600 rounded-xl hover:bg-gray-100 transition-all duration-200"
                      data-action="click->mobile-menu#toggle">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
              </button>

              <!-- Enhanced User Menu -->
              <div class="relative" data-controller="dropdown" data-dropdown-placement-value="bottom-end">
                <button class="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 transition-all duration-200"
                        data-dropdown-target="trigger" data-action="click->dropdown#toggle">
                  <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-md">
                    <span class="text-white text-sm font-semibold"><%= current_user.display_name[0].upcase %></span>
                  </div>
                  <div class="hidden lg:block text-left">
                    <p class="text-sm font-medium text-gray-900 truncate max-w-32"><%= current_user.display_name %></p>
                    <p class="text-xs text-gray-500">Free Plan</p>
                  </div>
                  <svg class="hidden lg:block w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>

                <div class="hidden absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50" data-dropdown-target="menu" role="menu">
                  <!-- User Info Header -->
                  <div class="px-4 py-3 border-b border-gray-100">
                    <p class="text-sm font-semibold text-gray-900 truncate"><%= current_user.display_name %></p>
                    <p class="text-xs text-gray-500 truncate"><%= current_user.email %></p>
                  </div>

                  <!-- Menu Items -->
                  <%= link_to edit_user_registration_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Account Settings
                  <% end %>

                  <%= link_to settings_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Preferences
                  <% end %>

                  <%= link_to help_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Help & Support
                  <% end %>

                  <hr class="my-2 border-gray-100">

                  <%= button_to destroy_user_session_path, method: :delete, class: "flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors", role: "menuitem" do %>
                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Sign Out
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Page Content -->
        <div class="flex-1 overflow-y-auto">
          <% if notice.present? %>
            <div class="bg-green-50 border-l-4 border-green-400 p-4 m-4">
              <p class="text-sm text-green-700"><%= notice %></p>
            </div>
          <% end %>
          
          <% if alert.present? %>
            <div class="bg-red-50 border-l-4 border-red-400 p-4 m-4">
              <p class="text-sm text-red-700"><%= alert %></p>
            </div>
          <% end %>
          
          <%= yield %>
        </div>
      </main>
    </div>
    <%= render 'shared/bot_widget' %>
  </body>
</html>