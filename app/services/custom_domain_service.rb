class CustomDomainService
  attr_reader :user, :domain

  def initialize(user, domain = nil)
    @user = user
    @domain = domain
  end

  def add_domain(domain_name)
    # Clean and validate the domain
    domain_name = clean_domain(domain_name)

    # Check if domain already exists
    if CustomDomain.exists?(domain: domain_name)
      return ServiceResult.failure(
        errors: { domain: [ "has already been taken" ] }
      )
    end

    # Check user's domain limit based on subscription
    if user_at_domain_limit?
      return ServiceResult.failure(
        errors: { base: [ "You have reached your custom domain limit for your subscription plan" ] }
      )
    end

    # Create the custom domain
    custom_domain = user.custom_domains.build(
      domain: domain_name,
      verification_token: generate_verification_token,
      verified: false
    )

    if custom_domain.save
      ServiceResult.success(
        data: { custom_domain: custom_domain, verification_instructions: verification_instructions(custom_domain) },
        custom_domain: custom_domain,
        verification_instructions: verification_instructions(custom_domain)
      )
    else
      ServiceResult.failure(
        errors: custom_domain.errors
      )
    end
  end

  def verify_domain(custom_domain)
    # In production, this would check DNS records
    # For now, we'll simulate verification
    dns_records = check_dns_records(custom_domain)

    if dns_records[:txt_valid] && dns_records[:cname_valid]
      custom_domain.verify!
      ServiceResult.success(
        data: { custom_domain: custom_domain, message: "Domain verified successfully!" },
        custom_domain: custom_domain,
        message: "Domain verified successfully!"
      )
    else
      ServiceResult.failure(
        errors: { base: [ "DNS records not found or invalid" ] },
        dns_status: dns_records
      )
    end
  end

  def remove_domain(custom_domain)
    if custom_domain.destroy
      ServiceResult.success(data: { message: "Domain removed successfully" }, message: "Domain removed successfully")
    else
      ServiceResult.failure(
        errors: custom_domain.errors
      )
    end
  end

  def set_primary_domain(custom_domain)
    unless custom_domain.verified?
      return ServiceResult.failure(
        errors: { base: [ "Domain must be verified before setting as primary" ] }
      )
    end

    ActiveRecord::Base.transaction do
      # Remove primary status from other domains
      user.custom_domains.where.not(id: custom_domain.id).update_all(is_primary: false)

      # Set this domain as primary
      custom_domain.update!(is_primary: true)
    end

    ServiceResult.success(
      data: { custom_domain: custom_domain, message: "Primary domain updated successfully" },
      custom_domain: custom_domain,
      message: "Primary domain updated successfully"
    )
  rescue ActiveRecord::RecordInvalid => e
    ServiceResult.failure(
      errors: { base: [ e.message ] }
    )
  end

  private

  def clean_domain(domain_name)
    domain_name.to_s.downcase.strip.gsub(/^https?:\/\//, "").gsub(/\/$/, "")
  end

  def generate_verification_token
    "linklysis-verify-#{SecureRandom.hex(16)}"
  end

  def user_at_domain_limit?
    !user.can_add_custom_domain?
  end

  def verification_instructions(custom_domain)
    {
      txt_record: {
        type: "TXT",
        name: "_linklysis-verify",
        value: custom_domain.verification_token,
        ttl: "3600"
      },
      cname_record: {
        type: "CNAME",
        name: "@",
        value: "links.linklysis.com",
        ttl: "3600"
      },
      instructions: [
        "Add the TXT record to verify ownership",
        "Add the CNAME record to point your domain to Linklysis",
        "Verification usually takes 5-30 minutes",
        'Click "Verify Domain" once DNS records are added'
      ]
    }
  end

  def check_dns_records(custom_domain)
    # In production, this would use a DNS resolver to check actual records
    # For development, we'll simulate the check
    {
      txt_valid: simulate_dns_check,
      cname_valid: simulate_dns_check,
      txt_value: custom_domain.verification_token,
      cname_value: "links.linklysis.com"
    }
  end

  def simulate_dns_check
    # In development, randomly succeed 70% of the time
    Rails.env.development? ? rand > 0.3 : false
  end
end
