# Standardized Result class for all services
# Provides consistent return structure with success/failure states and flexible data/error handling
class ServiceResult
  attr_reader :data, :errors

  def initialize(success:, data: nil, errors: nil, **extra_attributes)
    @success = success
    @data = data
    @errors = normalize_errors(errors)

    # Allow additional attributes for backward compatibility
    extra_attributes.each do |key, value|
      instance_variable_set("@#{key}", value)
      define_singleton_method(key) { instance_variable_get("@#{key}") }
    end
  end

  def success?
    @success
  end

  def failure?
    !@success
  end

  # Convenience methods for common patterns
  def self.success(data: nil, **extra_attributes)
    new(success: true, data: data, errors: [], **extra_attributes)
  end

  def self.failure(errors: nil, data: nil, **extra_attributes)
    new(success: false, data: data, errors: errors, **extra_attributes)
  end

  # For backward compatibility with existing code expecting specific attributes
  def method_missing(method_name, *args, &block)
    # If it's an instance variable getter, try to return it
    if args.empty? && instance_variable_defined?("@#{method_name}")
      instance_variable_get("@#{method_name}")
    else
      super
    end
  end

  def respond_to_missing?(method_name, include_private = false)
    instance_variable_defined?("@#{method_name}") || super
  end

  private

  def normalize_errors(errors)
    case errors
    when nil
      []
    when String
      [ errors ]
    when Array
      errors
    when Hash
      errors
    when ActiveModel::Errors
      errors.to_hash
    else
      [ errors.to_s ]
    end
  end
end
