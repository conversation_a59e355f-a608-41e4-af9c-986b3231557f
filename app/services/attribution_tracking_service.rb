class AttributionTrackingService
  attr_reader :link, :request

  def initialize(link:, request:)
    @link = link
    @request = request
  end

  def track_click(utm_params = {})
    dnt_enabled = respect_dnt?

    click = link.link_clicks.build(
      clicked_at: Time.current,
      attribution_data: build_attribution_data(utm_params),
      tracking_data: dnt_enabled ? {} : build_tracking_data,
      dnt_enabled: dnt_enabled,
      retention_expires_at: calculate_retention_expiry
    )

    if click.save
      ServiceResult.success(data: { click: click }, click: click)
    else
      ServiceResult.failure(errors: click.errors.to_hash)
    end
  end

  def original_url_with_attribution(utm_params)
    uri = URI.parse(link.original_url)
    existing_params = Rack::Utils.parse_query(uri.query || "")

    # Merge UTM params, with new params taking precedence
    merged_params = existing_params.merge(utm_params.stringify_keys)

    uri.query = merged_params.to_query
    uri.to_s
  end

  private

  def build_attribution_data(utm_params)
    # Handle both string and symbol keys
    utm_params = utm_params.with_indifferent_access if utm_params.respond_to?(:with_indifferent_access)

    {
      utm_source: utm_params[:utm_source] || utm_params["utm_source"],
      utm_medium: utm_params[:utm_medium] || utm_params["utm_medium"],
      utm_campaign: utm_params[:utm_campaign] || utm_params["utm_campaign"],
      utm_term: utm_params[:utm_term] || utm_params["utm_term"],
      utm_content: utm_params[:utm_content] || utm_params["utm_content"],
      referrer: request.referrer
    }.compact
  end

  def build_tracking_data
    data = {}

    # If Do Not Track is enabled, don't collect any tracking data
    return data if respect_dnt?

    # Get original IP for geolocation lookup (before anonymization)
    original_ip = request.remote_ip

    # Basic tracking info with anonymized IP
    data[:ip_address] = anonymize_ip(original_ip)
    data[:user_agent] = request.user_agent

    # Parse user agent for device info
    if request.user_agent
      user_agent_data = parse_user_agent(request.user_agent)
      data.merge!(user_agent_data)
    end

    # Geolocation lookup using original IP (more accurate than anonymized)
    # But we don't store the original IP, only the geographic data
    if original_ip.present?
      begin
        geo_data = GeolocationService.lookup(original_ip)
        data.merge!(geo_data) if geo_data
      rescue StandardError => e
        Rails.logger.debug "Geolocation lookup failed for anonymized IP: #{e.message}"
        # Silently fail geolocation - this is expected and acceptable
      end
    end

    data
  end

  def parse_user_agent(user_agent_string)
    data = {}

    # Device type detection
    data[:device_type] = detect_device_type(user_agent_string)

    # Browser detection
    data[:browser] = detect_browser(user_agent_string)

    # OS detection
    data[:os] = detect_os(user_agent_string)

    # Bot detection
    data[:bot] = bot?(user_agent_string)

    data
  end

  def detect_device_type(user_agent)
    case user_agent
    when /iPad/i
      "tablet"
    when /iPhone|Android.*Mobile|Windows Phone/i
      "mobile"
    when /Android/i
      "tablet" # Android without Mobile is usually tablet
    else
      "desktop"
    end
  end

  def detect_browser(user_agent)
    case user_agent
    when /Chrome/i
      "Chrome"
    when /Safari/i
      "Safari"
    when /Firefox/i
      "Firefox"
    when /Edge/i
      "Edge"
    when /Opera|OPR/i
      "Opera"
    else
      "Other"
    end
  end

  def detect_os(user_agent)
    case user_agent
    when /iPhone|iPad/i
      "iOS"
    when /Android/i
      "Android"
    when /Windows NT/i
      "Windows"
    when /Mac OS X/i
      "macOS"
    when /Linux/i
      "Linux"
    else
      "Other"
    end
  end

  def bot?(user_agent)
    bot_patterns = [
      /bot/i,
      /spider/i,
      /crawl/i,
      /scraper/i,
      /mediapartners/i,
      /adsbot/i,
      /googlebot/i,
      /bingbot/i,
      /slurp/i,
      /duckduckbot/i,
      /baiduspider/i,
      /yandexbot/i,
      /facebookexternalhit/i,
      /twitterbot/i,
      /linkedinbot/i,
      /whatsapp/i,
      /applebot/i
    ]

    bot_patterns.any? { |pattern| user_agent.match?(pattern) }
  end

  def anonymize_ip(ip)
    return nil if ip.blank?

    # Always anonymize IPs for GDPR compliance by default
    # This is more privacy-focused and safer than storing full IPs
    begin
      addr = IPAddr.new(ip)

      if addr.ipv4?
        # For IPv4: Zero out the last octet (e.g., ************* -> ***********)
        # This maintains geographic accuracy while protecting individual privacy
        masked = addr.mask(24)
        masked.to_s
      elsif addr.ipv6?
        # For IPv6: Zero out the last 80 bits (keep only first 48 bits)
        # This maintains ISP/organization accuracy while protecting individual privacy
        masked = addr.mask(48)
        masked.to_s
      else
        nil
      end
    rescue IPAddr::InvalidAddressError => e
      Rails.logger.warn "Invalid IP address for anonymization: #{ip} - #{e.message}"
      nil
    end
  end

  def respect_dnt?
    request.headers["DNT"] == "1"
  end

  def calculate_retention_expiry
    # Calculate when this data should be automatically deleted
    retention_period = Rails.application.config.data_retention_period || 13.months
    Time.current + retention_period
  end
end
