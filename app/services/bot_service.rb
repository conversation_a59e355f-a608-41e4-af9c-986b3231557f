require "timeout"

class BotService
  SYSTEM_PROMPT = <<~PROMPT
    You are Linklysis Assistant, a helpful AI assistant for the Linklysis link shortening platform.

    Your capabilities include:
    - Helping users understand how to use link shortening features
    - Explaining analytics and click tracking
    - Guiding users through custom domain setup
    - Assisting with team collaboration features
    - Providing onboarding support for new users
    - Answering questions about subscription plans and features

    Key platform features you should know about:
    - Link shortening with custom short codes
    - QR code generation for links
    - Detailed click analytics and tracking
    - Custom domain support
    - Team collaboration features
    - Bulk link imports
    - API access with token authentication
    - GDPR-compliant data handling

    Always be helpful, concise, and friendly. If you don't know something specific about the platform,
    suggest the user contact support or check the documentation.
  PROMPT

  def initialize(user:, session_context: {})
    @user = user
    @session_context = session_context
  end

  def process_message(message)
    # Build context from user data
    user_context = build_user_context

    # Analyze message intent
    intent = analyze_intent(message)

    # Generate contextual response
    response = generate_response(message, intent, user_context)

    # Track the interaction
    track_interaction(message, response, intent)

    ServiceResult.success(
      data: {
        response: response,
        intent: intent,
        suggestions: generate_suggestions(intent),
        context: user_context.slice(:subscription_plan, :total_links, :recent_activity)
      }
    )
  rescue StandardError => e
    Rails.logger.error "Bot service failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    ServiceResult.failure(
      errors: [ "I'm experiencing technical difficulties. Please try again later." ],
      data: { fallback_response: get_fallback_response }
    )
  end

  private

  def build_user_context
    {
      subscription_plan: @user.subscription_plan || "free",
      total_links: @user.links.count,
      active_links: @user.links.active.count,
      recent_activity: @user.links.recent.limit(5).pluck(:original_url, :short_code),
      has_custom_domains: @user.custom_domains.any?,
      is_team_member: @user.teams.any?,
      created_at: @user.created_at,
      last_sign_in: @user.respond_to?(:current_sign_in_at) ? @user.current_sign_in_at : nil
    }
  end

  def analyze_intent(message)
    message_lower = message.downcase

    case message_lower
    when /hello|hi|hey|greeting/
      :greeting
    when /help|support|how.*do|what.*is|explain/
      :help_request
    when /link|short|create|generate/
      :link_creation
    when /analytics|stats|click|track/
      :analytics
    when /domain|custom.*domain|cname/
      :custom_domain
    when /team|collaborate|share/
      :team_features
    when /api|token|integration/
      :api_help
    when /subscription|plan|upgrade|billing/
      :subscription
    when /qr.*code|qr/
      :qr_code
    when /bulk|import|csv/
      :bulk_import
    when /gdpr|privacy|data/
      :privacy
    when /onboard|getting.*started|new.*user|first.*time|walk.*through|tutorial/
      :onboarding
    when /tour|guide|show.*me|demo/
      :guided_tour
    else
      :general
    end
  end

  def generate_response(message, intent, user_context)
    case intent
    when :greeting
      generate_greeting_response(user_context)
    when :help_request
      generate_help_response(message, user_context)
    when :link_creation
      generate_link_creation_response(user_context)
    when :analytics
      generate_analytics_response(user_context)
    when :custom_domain
      generate_custom_domain_response(user_context)
    when :team_features
      generate_team_response(user_context)
    when :api_help
      generate_api_response(user_context)
    when :subscription
      generate_subscription_response(user_context)
    when :qr_code
      generate_qr_code_response(user_context)
    when :bulk_import
      generate_bulk_import_response(user_context)
    when :privacy
      generate_privacy_response
    when :onboarding
      generate_onboarding_response(user_context)
    when :guided_tour
      generate_guided_tour_response(user_context)
    else
      generate_general_response(message, user_context)
    end
  end

  def generate_greeting_response(context)
    time_of_day = Time.current.hour < 12 ? "morning" : (Time.current.hour < 18 ? "afternoon" : "evening")

    if context[:total_links] == 0
      days_since_signup = (Time.current - context[:created_at]).to_i / 1.day

      if days_since_signup == 0
        "Good #{time_of_day}! 👋 Welcome to Linklysis! I'm your personal assistant, and I'm excited to help you get started.\n\n" \
        "Since this is your first day, would you like me to:\n" \
        "• **Give you a quick tour** of the platform\n" \
        "• **Walk you through creating your first link**\n" \
        "• **Show you the key features** you have access to\n\n" \
        "What sounds most helpful right now?"
      else
        "Good #{time_of_day}! 👋 Welcome back to Linklysis! I noticed you haven't created any links yet. " \
        "No worries - I'm here to help you get started whenever you're ready. Would you like me to walk you through creating your first short link?"
      end
    else
      days_since_signup = (Time.current - context[:created_at]).to_i / 1.day

      if days_since_signup <= 7 && context[:total_links] < 5
        "Good #{time_of_day}! 👋 Great to see you're getting started with Linklysis! You have #{context[:total_links]} link#{'s' if context[:total_links] != 1} so far. " \
        "Would you like to explore more features like analytics, QR codes, or organizing your links with tags?"
      else
        "Good #{time_of_day}! 👋 I see you have #{context[:total_links]} link#{'s' if context[:total_links] != 1} in your account. How can I assist you today?"
      end
    end
  end

  def generate_help_response(message, context)
    response = "I'm here to help! "

    # Try to find relevant knowledge base articles with timeout
    begin
      Timeout.timeout(1.5) do # 1.5 second timeout for faster response
        search_service = KnowledgeSearchService.new(message)
        search_result = search_service.search(limit: 2) # Reduce limit for speed

        if search_result.success? && search_result.data.any?
          articles = search_result.data
          response += "I found some relevant articles:\n\n"

          articles.each_with_index do |article, index|
            response += "**#{index + 1}. #{article.title}**\n"
            response += "#{article.content[0..100]}...\n\n" # Shorter preview
          end

          response += "Would you like me to explain any of these topics?"
          return response
        end
      end
    rescue Timeout::Error, StandardError => e
      Rails.logger.warn "Knowledge search timeout: #{e.message}"
      # Fall through to default response
    end

    # Fast default response if search fails or times out
    response += "I can assist with:\n\n" \
               "• Creating and managing short links\n" \
               "• Understanding your analytics and click tracking\n" \
               "• Setting up custom domains\n" \
               "• Team collaboration features\n" \
               "• API integration and tokens\n" \
               "• Subscription plans and features\n\n" \
               "What would you like to know more about?"

    response
  end

  def generate_link_creation_response(context)
    if context[:total_links] == 0
      "Creating your first link is easy! 🎉\n\n" \
      "1. Click the \"Create Link\" button\n" \
      "2. Paste your long URL\n" \
      "3. Optionally customize your short code\n" \
      "4. Click \"Create\" and you're done!\n\n" \
      "Would you like me to explain any specific features like custom domains or QR codes?"
    else
      "You can create new links from the dashboard or use our API. " \
      "On your #{context[:subscription_plan]} plan, you #{subscription_limits_text(context[:subscription_plan])}. " \
      "Need help with custom short codes or other advanced features?"
    end
  end

  def generate_analytics_response(context)
    if context[:total_links] == 0
      "Analytics will show up once you create some links and they start getting clicks! " \
      "You'll see metrics like click counts, geographic data, device types, and referrers."
    else
      "Your analytics dashboard shows detailed insights for your #{context[:total_links]} link#{'s' if context[:total_links] != 1}. " \
      "You can view click counts, geographic data, device breakdowns, and referrer information. " \
      "Would you like me to explain any specific analytics features?"
    end
  end

  def generate_custom_domain_response(context)
    if context[:subscription_plan] == "free"
      "Custom domains are available on Professional plans and above! " \
      "With a custom domain, your links would look like 'yourdomain.com/abc123' instead of 'linklysis.com/abc123'. " \
      "Would you like to learn about upgrading your plan?"
    elsif context[:has_custom_domains]
      "I see you have custom domains set up! You can manage them in your settings. " \
      "Need help with DNS configuration or setting a primary domain?"
    else
      "You can add custom domains in your settings. You'll need to:\n\n" \
      "1. Add your domain\n" \
      "2. Verify ownership with DNS records\n" \
      "3. Point your domain to Linklysis\n\n" \
      "Would you like step-by-step guidance?"
    end
  end

  def generate_team_response(context)
    if context[:subscription_plan] == "free"
      "Team features are available on Business and Enterprise plans! " \
      "Teams allow you to collaborate on links, share analytics, and manage permissions. " \
      "Would you like to learn about upgrading?"
    elsif context[:is_team_member]
      "I see you're part of a team! You can collaborate on links, share analytics, and manage team settings. " \
      "Need help with team permissions or link sharing?"
    else
      "You can create teams to collaborate with colleagues. Team features include shared links, " \
      "role-based permissions, and collaborative analytics. Would you like help setting up a team?"
    end
  end

  def generate_api_response(context)
    "Our API lets you programmatically manage links! You can:\n\n" \
    "• Create and manage links\n" \
    "• Access analytics data\n" \
    "• Manage custom domains\n" \
    "• Bulk operations\n\n" \
    "You'll need an API token from your settings. Would you like help with authentication or specific endpoints?"
  end

  def generate_subscription_response(context)
    current_plan = context[:subscription_plan].capitalize

    "You're currently on the #{current_plan} plan. Here's what each plan offers:\n\n" \
    "**Free**: 1,000 links/month, basic analytics\n" \
    "**Professional**: 10,000 links/month, custom domains, advanced analytics\n" \
    "**Business**: 50,000 links/month, teams, API access, priority support\n" \
    "**Enterprise**: Unlimited links, white-label options, dedicated support\n\n" \
    "Would you like to learn about upgrading or need help with billing?"
  end

  def generate_qr_code_response(context)
    "QR codes are generated automatically for all your links! 📱\n\n" \
    "You can:\n" \
    "• Download QR codes in SVG, PNG, or PDF format\n" \
    "• Customize colors and error correction\n" \
    "• Adjust size and border settings\n\n" \
    "Find the QR code option on each link's detail page. Need help with customization?"
  end

  def generate_bulk_import_response(context)
    if context[:subscription_plan] == "free"
      "Bulk import is available on Professional plans and above! " \
      "You can upload CSV files with hundreds or thousands of URLs. " \
      "Would you like to learn about upgrading?"
    else
      "You can bulk import links using CSV files! The format should include:\n\n" \
      "• URL or original_url column (required)\n" \
      "• Optional: title, tags, expires_at\n\n" \
      "Maximum file size is 10MB with up to 10,000 rows. Need help with the CSV format?"
    end
  end

  def generate_privacy_response
    "Linklysis is fully GDPR compliant! 🔒\n\n" \
    "We:\n" \
    "• Anonymize IP addresses for privacy\n" \
    "• Respect Do Not Track headers\n" \
    "• Automatically clean up old data\n" \
    "• Provide data export and deletion\n\n" \
    "Your data is secure and handled responsibly. Need more details about our privacy practices?"
  end

  def generate_general_response(message, context)
    # Fast fallback response without knowledge search for better performance
    if context[:total_links] == 0
      "I'm here to help with Linklysis! Since you're just getting started, I can help you create your first link, " \
      "explain how analytics work, or give you a tour of the platform. What would be most helpful?"
    else
      "I'm here to help with Linklysis! I can assist with link creation, analytics, custom domains, " \
      "team features, API integration, and more. Could you be more specific about what you need help with?"
    end
  end

  def generate_onboarding_response(context)
    if context[:total_links] == 0
      "Welcome to Linklysis! 🎉 Let me guide you through getting started:\n\n" \
      "**Step 1: Create Your First Link**\n" \
      "• Click \"Create Link\" in your dashboard\n" \
      "• Paste any URL you'd like to shorten\n" \
      "• Choose a custom short code (optional)\n" \
      "• Click \"Create\" and you're done!\n\n" \
      "**Step 2: Share & Track**\n" \
      "• Copy your new short link and share it anywhere\n" \
      "• Watch real-time analytics as clicks come in\n\n" \
      "**Step 3: Explore Features**\n" \
      "• Generate QR codes for offline sharing\n" \
      "• Organize links with tags\n" \
      "• Set up custom domains (Pro plans)\n\n" \
      "Ready to create your first link? I'm here to help!"
    else
      days_since_signup = (Time.current - context[:created_at]).to_i / 1.day

      if days_since_signup <= 7
        "Great to see you're getting started! 🚀 You've created #{context[:total_links]} link#{'s' if context[:total_links] != 1} so far.\n\n" \
        "Here are some next steps to explore:\n\n" \
        "• **Analytics**: Check out detailed click data and geographic insights\n" \
        "• **Organization**: Use tags to organize your links by campaign or project\n" \
        "• **QR Codes**: Generate scannable codes for offline marketing\n" \
        "• **Custom Domains**: Brand your links with your own domain (Pro plans)\n\n" \
        "What would you like to explore next?"
      else
        "Welcome back! You seem to be getting comfortable with Linklysis. " \
        "Is there a specific feature you'd like to learn more about, or would you like me to show you some advanced tips?"
      end
    end
  end

  def generate_guided_tour_response(context)
    if context[:total_links] == 0
      "I'd love to give you a tour! 🗺️ Let's start with the basics:\n\n" \
      "**🏠 Dashboard Overview**\n" \
      "Your dashboard shows all your links, recent activity, and quick stats. The \"Create Link\" button is your starting point.\n\n" \
      "**🔗 Link Creation**\n" \
      "Creating links is super simple - just paste a URL and we'll generate a short version. You can customize the short code or let us create one.\n\n" \
      "**📊 Analytics**\n" \
      "Every link gets detailed analytics - see clicks, locations, devices, and referrers in real-time.\n\n" \
      "**⚙️ Settings**\n" \
      "Manage your account, set up custom domains, generate API tokens, and configure team settings.\n\n" \
      "Would you like me to walk you through creating your first link step-by-step?"
    else
      "Let me show you around! 🎯 Here's what you have access to:\n\n" \
      "**📈 Analytics Dashboard**\n" \
      "View performance across all #{context[:total_links]} of your links. See trends, top performers, and audience insights.\n\n" \
      "**🏷️ Link Organization**\n" \
      "Use tags, folders, and search to keep your links organized as you scale.\n\n" \
      "**🛠️ Advanced Features**\n" \
      "• Bulk import/export\n" \
      "• API integration\n" \
      "• Custom domains (Pro+)\n" \
      "• Team collaboration (Business+)\n\n" \
      "**📱 QR Codes & Sharing**\n" \
      "Every link has a QR code for offline sharing. Customize colors and download formats.\n\n" \
      "Which area would you like to explore in detail?"
    end
  end

  def generate_suggestions(intent)
    case intent
    when :greeting
      [ "Create my first link", "Show me analytics", "How do custom domains work?" ]
    when :link_creation
      [ "How do I customize short codes?", "Can I set expiration dates?", "How do I use teams?" ]
    when :analytics
      [ "Export analytics data", "Understanding click tracking", "Geographic data explained" ]
    when :custom_domain
      [ "DNS setup help", "Verify domain ownership", "Set primary domain" ]
    when :subscription
      [ "Compare plans", "Upgrade account", "Billing questions" ]
    when :api_help
      [ "Generate API token", "API documentation", "Rate limits" ]
    when :onboarding
      [ "Walk me through creating a link", "Show me the dashboard", "What can I do with analytics?", "Tell me about QR codes" ]
    when :guided_tour
      [ "Explain analytics in detail", "Show me advanced features", "How do I organize links?", "What are the subscription benefits?" ]
    else
      [ "Create a link", "View analytics", "Account settings" ]
    end
  end

  def subscription_limits_text(plan)
    case plan
    when "free"
      "can create up to 1,000 links per month"
    when "professional"
      "can create up to 10,000 links per month"
    when "business"
      "can create up to 50,000 links per month"
    when "enterprise"
      "have unlimited link creation"
    else
      "have plan-specific limits"
    end
  end

  def track_interaction(message, response, intent)
    # This would typically save to a bot_interactions table
    # For now, we'll log it
    Rails.logger.info "Bot interaction: user=#{@user.id}, intent=#{intent}, message_length=#{message.length}"
  end

  def get_fallback_response
    "I'm sorry, I'm having trouble right now. Please try refreshing the page or contact support if the issue persists."
  end
end
