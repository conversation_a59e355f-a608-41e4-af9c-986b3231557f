class KnowledgeSearchService
  include ResultHelper

  SEARCH_WEIGHTS = {
    title_exact: 10,
    title_partial: 5,
    keywords_exact: 8,
    keywords_partial: 4,
    content_exact: 3,
    content_partial: 1
  }.freeze

  def initialize(query)
    @query = query.to_s.strip.downcase
    @keywords = extract_keywords(@query)
  end

  def search(limit: 10)
    return success([]) if @query.blank?

    begin
      articles = find_articles(limit)
      success(articles)
    rescue StandardError => e
      Rails.logger.error "KnowledgeSearchService error: #{e.message}"
      failure("Unable to search knowledge base")
    end
  end

  def find_by_category(category_slug, limit: 5)
    return success([]) if category_slug.blank?

    begin
      category = KnowledgeCategory.active.find_by(slug: category_slug)
      return success([]) unless category

      articles = category.knowledge_articles
                        .published
                        .ordered
                        .limit(limit)

      success(articles)
    rescue StandardError => e
      Rails.logger.error "KnowledgeSearchService category error: #{e.message}"
      failure("Unable to find articles in category")
    end
  end

  def find_featured(limit: 3)
    begin
      articles = KnowledgeArticle.published
                                .featured
                                .ordered
                                .limit(limit)
      success(articles)
    rescue StandardError => e
      Rails.logger.error "KnowledgeSearchService featured error: #{e.message}"
      failure("Unable to find featured articles")
    end
  end

  def find_related(article, limit: 3)
    return success([]) unless article.is_a?(KnowledgeArticle)

    begin
      # Find articles with similar keywords or in same category
      keywords = extract_keywords(article.keywords.to_s + " " + article.title)

      related = KnowledgeArticle.published
                               .where.not(id: article.id)
                               .joins(:knowledge_category)
                               .where(
                                 knowledge_categories: { active: true },
                                 knowledge_category_id: article.knowledge_category_id
                               )
                               .or(
                                 KnowledgeArticle.published
                                                .where.not(id: article.id)
                                                .where("keywords ILIKE ANY(ARRAY[?])",
                                                      keywords.map { |k| "%#{k}%" })
                               )
                               .ordered
                               .limit(limit)

      success(related)
    rescue StandardError => e
      Rails.logger.error "KnowledgeSearchService related error: #{e.message}"
      failure("Unable to find related articles")
    end
  end

  def suggest_topics
    begin
      categories = KnowledgeCategory.active
                                  .joins(:knowledge_articles)
                                  .where(knowledge_articles: { published: true })
                                  .select("knowledge_categories.*, COUNT(knowledge_articles.id) as article_count")
                                  .group("knowledge_categories.id")
                                  .having("COUNT(knowledge_articles.id) > 0")
                                  .ordered

      popular_articles = KnowledgeArticle.published
                                        .joins(:knowledge_category)
                                        .where(knowledge_categories: { active: true })
                                        .order(view_count: :desc, helpful_count: :desc)
                                        .limit(5)

      success({
        categories: categories,
        popular_articles: popular_articles
      })
    rescue StandardError => e
      Rails.logger.error "KnowledgeSearchService suggest error: #{e.message}"
      failure("Unable to suggest topics")
    end
  end

  private

  def find_articles(limit)
    # Use Rails' secure search methods to prevent SQL injection
    search_term = @query.to_s.strip
    
    articles = KnowledgeArticle.published
                              .joins(:knowledge_category)
                              .where(knowledge_categories: { active: true })
    
    # Build search conditions using Rails' secure where methods
    search_conditions = articles.none # Start with empty relation
    
    # Search in title
    if search_term.present?
      title_results = articles.where("title ILIKE ?", "%#{search_term}%")
      search_conditions = search_conditions.or(title_results)
    end
    
    # Search in content
    if search_term.present?
      content_results = articles.where("content ILIKE ?", "%#{search_term}%")
      search_conditions = search_conditions.or(content_results)
    end
    
    # Search in keywords if present
    if @keywords.any?
      @keywords.each do |keyword|
        # Sanitize keyword input
        safe_keyword = keyword.to_s.strip
        next if safe_keyword.blank?
        
        keyword_results = articles.where("keywords ILIKE ?", "%#{safe_keyword}%")
        search_conditions = search_conditions.or(keyword_results)
      end
    end
    
    # Return ordered results
    search_conditions.includes(:knowledge_category)
                    .order(helpful_count: :desc, view_count: :desc, created_at: :desc)
                    .limit(limit)
                    .to_a
  end

  def extract_keywords(text)
    return [] if text.blank?

    # Remove common stop words and extract meaningful keywords
    stop_words = %w[the and or but if then else when where what how why who which that this these those]

    text.downcase
        .gsub(/[^a-z0-9\s]/, " ")
        .split(/\s+/)
        .reject { |word| word.length < 3 || stop_words.include?(word) }
        .uniq
        .first(10) # Limit to prevent overly complex queries
  end
end
