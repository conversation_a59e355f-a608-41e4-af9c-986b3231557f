class QrCodeService
  attr_reader :link, :options, :request

  def initialize(link, options = {})
    @link = link
    @options = default_options.merge(options)
    @request = options.delete(:request) # Extract request object for analytics
  end

  def generate_svg
    result = Rails.cache.fetch("#{cache_key}:svg", expires_in: 1.hour) do
      size = calculate_svg_size
      qr_code.as_svg(
        offset: 0,
        color: options[:color],
        shape_rendering: "crispEdges",
        module_size: options[:module_size],
        svg_attributes: {
          class: "qr-code-svg",
          viewBox: "0 0 #{size} #{size}",
          width: size,
          height: size
        }
      )
    end

    # Track analytics (async to avoid slowing down generation)
    track_analytics("svg")

    result
  end

  def generate_png
    result = Rails.cache.fetch("#{cache_key}:png", expires_in: 1.hour) do
      qr_code.as_png(
        bit_depth: 1,
        border_modules: options[:border_modules],
        color_mode: ChunkyPNG::COLOR_GRAYSCALE,
        color: format_color_for_png(options[:color]),
        file: nil,
        fill: format_color_for_png(options[:background_color]),
        module_px_size: options[:module_px_size],
        resize_exactly_to: options[:png_size],
        resize_gte_to: false
      )
    end

    # Track analytics (async to avoid slowing down generation)
    track_analytics("png")

    result
  end

  def generate_pdf
    result = Rails.cache.fetch("#{cache_key}:pdf", expires_in: 1.hour) do
      require "prawn"

      Prawn::Document.new(page_size: "A4", margin: 72) do |pdf|
        # Add title
        pdf.text "QR Code for #{link.short_url}", size: 16, style: :bold, align: :center
        pdf.move_down 20

        # Add QR code description
        pdf.text "Scan this QR code to visit: #{link.original_url}", size: 12, align: :center
        pdf.move_down 30

        # Generate PNG for embedding in PDF (without caching to avoid nested cache calls)
        png_data = qr_code.as_png(
          bit_depth: 1,
          border_modules: options[:border_modules],
          color_mode: ChunkyPNG::COLOR_GRAYSCALE,
          color: format_color_for_png(options[:color]),
          file: nil,
          fill: format_color_for_png(options[:background_color]),
          module_px_size: options[:module_px_size],
          resize_exactly_to: options[:png_size],
          resize_gte_to: false
        ).to_s

        # Calculate size for PDF (max 300 points, centered)
        qr_size = [ 300, options[:png_size] ].min
        x_position = (pdf.bounds.width - qr_size) / 2

        # Add QR code image
        pdf.image StringIO.new(png_data), at: [ x_position, pdf.cursor ], width: qr_size

        # Add footer with link details
        pdf.move_down qr_size + 30
        pdf.text "Short URL: #{link.short_url}", size: 10, align: :center
        pdf.text "Created: #{link.created_at.strftime('%B %d, %Y')}", size: 8, align: :center, color: "666666"
      end.render
    end

    # Track analytics (async to avoid slowing down generation)
    track_analytics("pdf")

    result
  end

  def to_data_url
    "data:image/png;base64,#{Base64.strict_encode64(generate_png.to_s)}"
  end

  def cache_key
    "qr_code:#{link.id}:#{options.hash}"
  end

  def clear_cache
    Rails.cache.delete("#{cache_key}:svg")
    Rails.cache.delete("#{cache_key}:png")
    Rails.cache.delete("#{cache_key}:pdf")
  end

  def self.clear_all_cache_for_link(link)
    # Clear all cached QR codes for a link (useful when link URL changes)
    Rails.cache.delete_matched("qr_code:#{link.id}:*")
  end

  private

  def track_analytics(format)
    return unless Rails.env.production? || Rails.env.development?

    # Track asynchronously to avoid slowing down QR code generation
    QrCodeAnalyticsJob.perform_later(
      link.id,
      format,
      analytics_options,
      request&.remote_ip,
      request&.user_agent
    )
  rescue => e
    # Log error but don't fail QR code generation
    Rails.logger.error "Failed to track QR code analytics: #{e.message}"
  end

  def analytics_options
    options.except(:request).slice(
      :module_size, :color, :background_color,
      :error_correction, :png_size, :border_modules
    )
  end

  private

  def qr_code
    @qr_code ||= ::RQRCode::QRCode.new(
      link.short_url,
      level: options[:error_correction],
      mode: options[:mode]
    )
  end

  def calculate_svg_size
    # Calculate size based on QR code modules and module size
    modules = qr_code.modules.size
    border = options[:border_modules] * 2
    (modules + border) * options[:module_size]
  end

  def format_color(color)
    # Remove any existing # prefix and add one
    clean_color = color.to_s.gsub(/^#+/, "")
    "##{clean_color}"
  end

  def format_color_for_png(color)
    # ChunkyPNG expects colors in different format
    return color if color.is_a?(Integer)
    color_hex = color.gsub("#", "")
    color_hex.to_i(16)
  end

  def default_options
    {
      module_size: 6,             # Module size for SVG
      module_px_size: 10,         # Module pixel size for PNG
      png_size: 400,              # PNG size in pixels
      border_modules: 4,          # Border size in modules
      color: "000000",            # QR code color (hex without #)
      background_color: "ffffff", # Background color (hex without #)
      error_correction: :m,       # Error correction level (l, m, q, h)
      mode: nil,                  # Let rqrcode decide the best mode
      format: :svg                # Default format
    }
  end
end
