class GeolocationService
  def self.lookup(ip_address)
    new.lookup(ip_address)
  end

  def lookup(ip_address)
    return nil if ip_address.blank?
    return nil if private_ip?(ip_address)

    # For development/testing, return mock data for certain IPs
    if Rails.env.development? || Rails.env.test?
      return mock_geolocation_data(ip_address)
    end

    # In production, you would integrate with a real geolocation service
    # like MaxMind GeoIP2, IPinfo, or similar
    # For now, return nil to avoid external dependencies
    nil
  end

  private

  def private_ip?(ip)
    # Check if IP is in private ranges
    addr = IPAddr.new(ip) rescue nil
    return false unless addr

    private_ranges = [
      IPAddr.new("10.0.0.0/8"),
      IPAddr.new("**********/12"),
      IPAddr.new("***********/16"),
      IPAddr.new("*********/8"),
      IPAddr.new("::1/128"),
      IPAddr.new("fc00::/7")
    ]

    private_ranges.any? { |range| range.include?(addr) }
  end

  def mock_geolocation_data(ip)
    case ip
    when "*******"
      {
        country_code: "US",
        city: "Mountain View",
        region: "California"
      }
    when "*******"
      {
        country_code: "US",
        city: "San Francisco",
        region: "California"
      }
    else
      {
        country_code: "XX",
        city: "Unknown",
        region: "Unknown"
      }
    end
  end
end
