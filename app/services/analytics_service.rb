class AnalyticsService
  def initialize(user:, date_range: nil)
    @user = user
    @date_range = date_range
  end

  def dashboard_analytics
    # Use a single query to get base metrics
    base_stats = calculate_base_stats

    # Batch all analytics queries to minimize N+1
    analytics_data = {
      total_links: base_stats[:total_links],
      active_links: base_stats[:active_links],
      total_clicks: base_stats[:total_clicks],
      unique_visitors: base_stats[:unique_visitors],

      # Top performing links with click counts in single query
      top_links: fetch_top_performing_links,

      # All chart data in batched queries
      clicks_by_date: fetch_clicks_by_date,
      clicks_by_device: fetch_clicks_by_device,
      clicks_by_country: fetch_clicks_by_country,
      clicks_by_referrer: fetch_clicks_by_referrer,
      clicks_by_hour: fetch_clicks_by_hour,
      clicks_by_day_of_week: fetch_clicks_by_day_of_week,
      clicks_by_browser: fetch_clicks_by_browser,
      clicks_by_os: fetch_clicks_by_os,
      clicks_by_city: fetch_clicks_by_city,

      # Calculated metrics
      return_visitor_rate: calculate_return_visitor_rate(base_stats[:unique_visitors]),
      avg_clicks_per_visitor: calculate_avg_clicks_per_visitor(base_stats),
      new_links_count: calculate_new_links_count,
      growth_rate: calculate_growth_rate(base_stats[:total_clicks])
    }

    ServiceResult.success(data: analytics_data)
  rescue StandardError => e
    Rails.logger.error "Analytics calculation failed: #{e.message}"
    ServiceResult.failure(data: {}, errors: [ e.message ])
  end

  def link_analytics(link)
    # Single optimized query for link-specific analytics
    link_stats = calculate_link_stats(link)

    analytics_data = {
      total_clicks: link_stats[:total_clicks],
      unique_visitors: link_stats[:unique_visitors],

      # Batch all link-specific queries
      clicks_by_date: fetch_link_clicks_by_date(link),
      clicks_by_country: fetch_link_clicks_by_country(link),
      clicks_by_device: fetch_link_clicks_by_device(link),
      clicks_by_browser: fetch_link_clicks_by_browser(link),
      clicks_by_os: fetch_link_clicks_by_os(link),
      top_referrers: fetch_link_top_referrers(link),
      recent_clicks: fetch_link_recent_clicks(link),
      clicks_by_hour: fetch_link_clicks_by_hour(link),
      utm_campaigns: fetch_link_utm_campaigns(link),
      utm_sources: fetch_link_utm_sources(link),

      # Calculated metrics
      daily_average_clicks: calculate_daily_average_clicks(link),
      peak_hour: calculate_peak_hour(link)
    }

    ServiceResult.success(data: analytics_data)
  rescue StandardError => e
    Rails.logger.error "Link analytics calculation failed: #{e.message}"
    ServiceResult.failure(data: {}, errors: [ e.message ])
  end

  def export_data
    # Simple approach - get all links and calculate per link
    export_data = @user.links.includes(:link_clicks, :custom_domain).map do |link|
      clicks_in_range = link.link_clicks.select { |click| @date_range.cover?(click.clicked_at) }
      unique_ips = clicks_in_range.map { |click| click.tracking_data&.dig("ip_address") }.uniq.compact

      # Get top country and referrer for this link
      countries = clicks_in_range.map { |click| click.tracking_data&.dig("country_code") }.compact
      top_country = countries.group_by(&:itself).max_by { |_, v| v.length }&.first || "N/A"

      referrers = clicks_in_range.map { |click| click.attribution_data&.dig("referrer") }.compact
      top_referrer = referrers.group_by(&:itself).max_by { |_, v| v.length }&.first || "Direct"

      {
        link: link.short_url,
        original_url: link.original_url,
        total_clicks: link.link_clicks_count,
        clicks_in_period: clicks_in_range.count,
        unique_visitors: link.unique_visitors,
        unique_in_period: unique_ips.count,
        created_at: link.created_at.strftime("%Y-%m-%d"),
        last_clicked: link.link_clicks.maximum(:clicked_at)&.strftime("%Y-%m-%d %H:%M"),
        top_country: top_country,
        top_referrer: top_referrer
      }
    end

    ServiceResult.success(data: export_data)
  rescue StandardError => e
    Rails.logger.error "Export data calculation failed: #{e.message}"
    ServiceResult.failure(data: [], errors: [ e.message ])
  end

  private

  def base_link_clicks_query
    LinkClick.joins(:link)
      .where(links: { user_id: @user.id })
      .where(clicked_at: @date_range)
  end

  def calculate_base_stats
    {
      total_links: @user.links.count,
      active_links: @user.links.active.count,
      total_clicks: base_link_clicks_query.count,
      unique_visitors: base_link_clicks_query.distinct.count("tracking_data->>'ip_address'")
    }
  end

  def fetch_top_performing_links
    # Get top links with click counts in the period
    link_click_counts = @user.links
      .left_joins(:link_clicks)
      .where(link_clicks: { clicked_at: @date_range })
      .group("links.id")
      .order("COUNT(link_clicks.id) DESC")
      .limit(10)
      .pluck("links.id", "COUNT(link_clicks.id)")
      .to_h

    # Get the actual link objects
    links = @user.links.where(id: link_click_counts.keys)

    # Add clicks_in_period method to each link
    links.each do |link|
      link.define_singleton_method(:clicks_in_period) do
        link_click_counts[link.id] || 0
      end
    end

    # Sort by click count (since the original order might be lost)
    links.sort_by { |link| -link.clicks_in_period }
  end

  def fetch_clicks_by_date
    base_link_clicks_query.group_by_day(:clicked_at).count
  end

  def fetch_clicks_by_device
    base_link_clicks_query
      .group("link_clicks.tracking_data->>'device_type'")
      .count
      .sort_by { |_, v| -v }
      .to_h
  end

  def fetch_clicks_by_country
    base_link_clicks_query
      .where("link_clicks.tracking_data->>'country_code' IS NOT NULL")
      .group("link_clicks.tracking_data->>'country_code'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
      .to_h
  end

  def fetch_clicks_by_referrer
    base_link_clicks_query
      .where("link_clicks.attribution_data->>'referrer' IS NOT NULL")
      .group("link_clicks.attribution_data->>'referrer'")
      .count
      .sort_by { |_, v| -v }
      .first(5)
  end

  def fetch_clicks_by_hour
    base_link_clicks_query.group_by_hour_of_day(:clicked_at, format: "%l %P").count
  end

  def fetch_clicks_by_day_of_week
    raw_data = base_link_clicks_query.group_by_day_of_week(:clicked_at, format: "%A").count
    weekday_order = [ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday" ]
    weekday_order.index_with { |day| raw_data[day] || 0 }
  end

  def fetch_clicks_by_browser
    base_link_clicks_query
      .group("link_clicks.tracking_data->>'browser'")
      .count
      .sort_by { |_, v| -v }
      .first(5)
      .to_h
  end

  def fetch_clicks_by_os
    base_link_clicks_query
      .group("link_clicks.tracking_data->>'os'")
      .count
      .sort_by { |_, v| -v }
      .first(5)
      .to_h
  end

  def fetch_clicks_by_city
    base_link_clicks_query
      .where("link_clicks.tracking_data->>'city' IS NOT NULL")
      .group("link_clicks.tracking_data->>'city'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
      .to_h
  end

  def calculate_return_visitor_rate(unique_visitors)
    return_visitors = base_link_clicks_query
      .group("tracking_data->>'ip_address'")
      .having("COUNT(*) > 1")
      .count
      .keys
      .count

    unique_visitors > 0 ? (return_visitors.to_f / unique_visitors * 100).round(2) : 0
  end

  def calculate_avg_clicks_per_visitor(base_stats)
    unique_visitors = base_stats[:unique_visitors]
    total_clicks = base_stats[:total_clicks]
    unique_visitors > 0 ? (total_clicks.to_f / unique_visitors).round(2) : 0
  end

  def calculate_new_links_count
    @user.links.where(created_at: @date_range).count
  end

  def calculate_growth_rate(total_clicks)
    previous_range = (@date_range.first - (@date_range.last - @date_range.first))..@date_range.first
    previous_clicks = @user.links.joins(:link_clicks)
      .where(link_clicks: { clicked_at: previous_range }).count

    previous_clicks > 0 ? ((total_clicks - previous_clicks).to_f / previous_clicks * 100).round(2) : 0
  end

  # Link-specific methods
  def calculate_link_stats(link)
    {
      total_clicks: link.link_clicks.where(clicked_at: @date_range).count,
      unique_visitors: link.link_clicks.where(clicked_at: @date_range)
        .distinct.count("tracking_data->>'ip_address'")
    }
  end

  def fetch_link_clicks_by_date(link)
    link.link_clicks.where(clicked_at: @date_range).group_by_day(:clicked_at).count
  end

  def fetch_link_clicks_by_country(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .where("tracking_data->>'country_code' IS NOT NULL")
      .group("tracking_data->>'country_code'")
      .count
      .sort_by { |_, v| -v }
      .to_h
  end

  def fetch_link_clicks_by_device(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .group("tracking_data->>'device_type'")
      .count
  end

  def fetch_link_clicks_by_browser(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .group("tracking_data->>'browser'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
      .to_h
  end

  def fetch_link_clicks_by_os(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .group("tracking_data->>'os'")
      .count
      .sort_by { |_, v| -v }
      .to_h
  end

  def fetch_link_top_referrers(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .where("attribution_data->>'referrer' IS NOT NULL")
      .group("attribution_data->>'referrer'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
      .to_h
  end

  def fetch_link_recent_clicks(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .order(clicked_at: :desc)
      .limit(50)
  end

  def fetch_link_clicks_by_hour(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .group_by_hour_of_day(:clicked_at, format: "%l %P")
      .count
  end

  def fetch_link_utm_campaigns(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .where("attribution_data->>'utm_campaign' IS NOT NULL")
      .group("attribution_data->>'utm_campaign'")
      .count
      .sort_by { |_, v| -v }
      .to_h
  end

  def fetch_link_utm_sources(link)
    link.link_clicks
      .where(clicked_at: @date_range)
      .where("attribution_data->>'utm_source' IS NOT NULL")
      .group("attribution_data->>'utm_source'")
      .count
      .sort_by { |_, v| -v }
      .to_h
  end

  def calculate_daily_average_clicks(link)
    days_since_creation = [ (Date.current - link.created_at.to_date).to_i, 1 ].max
    (link.link_clicks_count.to_f / days_since_creation).round(2)
  end

  def calculate_peak_hour(link)
    clicks_by_hour = fetch_link_clicks_by_hour(link)
    peak_hour = clicks_by_hour.max_by { |_, v| v }
    peak_hour ? peak_hour[0] : "N/A"
  end
end
