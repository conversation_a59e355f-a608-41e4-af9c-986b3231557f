class LinkShorteningService
  attr_reader :user

  def initialize(user:)
    @user = user
  end

  def create_link(params)
    link = user.links.build(
      original_url: params[:original_url],
      short_code: params[:custom_short_code],
      team_id: params[:team_id],
      expires_at: params[:expires_at]
    )

    # Validate team membership if team_id provided
    if params[:team_id].present?
      team = Team.find_by(id: params[:team_id])
      unless team && user_belongs_to_team?(team)
        return ServiceResult.failure(errors: { team: [ "not authorized" ] })
      end
    end

    if link.save
      ServiceResult.success(
        data: { link: link, short_url: link.short_url },
        link: link,
        short_url: link.short_url
      )
    else
      ServiceResult.failure(errors: link.errors.to_hash)
    end
  end

  def update_link(link, params)
    unless can_manage_link?(link)
      return ServiceResult.failure(errors: { base: [ "not authorized" ] })
    end

    # Only update allowed fields
    link.original_url = params[:original_url] if params.key?(:original_url)
    link.short_code = params[:custom_short_code] if params.key?(:custom_short_code)
    link.expires_at = params[:expires_at] if params.key?(:expires_at)

    if link.save
      ServiceResult.success(data: { link: link }, link: link)
    else
      ServiceResult.failure(errors: link.errors.to_hash)
    end
  end

  def archive_link(link)
    unless can_manage_link?(link)
      return ServiceResult.failure(errors: { base: [ "not authorized" ] })
    end

    link.archived_at = Time.current

    if link.save
      ServiceResult.success(data: { link: link }, link: link)
    else
      ServiceResult.failure(errors: link.errors.to_hash)
    end
  end

  def bulk_create_links(urls, team_id: nil)
    links = []
    errors = {}

    urls.each do |url|
      result = create_link(
        original_url: url,
        custom_short_code: nil,
        team_id: team_id
      )

      if result.success?
        links << result.link
      else
        errors[url] = result.errors[:original_url] || [ "failed to create" ]
      end
    end

    ServiceResult.new(
      success: errors.empty?,
      data: { links: links, errors: errors },
      links: links,
      errors: errors.empty? ? [] : errors
    )
  end

  private

  def can_manage_link?(link)
    link.user_id == user.id || (link.team && user_belongs_to_team?(link.team))
  end

  def user_belongs_to_team?(team)
    # This assumes a many-to-many relationship through team_memberships
    # Will need to be implemented based on actual team structure
    team.users.exists?(id: user.id)
  end
end
