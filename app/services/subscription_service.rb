class SubscriptionService
  include ResultHelper

  # Define subscription plans with features and limits
  PLANS = {
    free: {
      name: "Free",
      price: 0,
      links_limit: 1_000,
      monthly_links_limit: 1_000,
      custom_domains_limit: 0,
      team_members_limit: 0,
      api_access: false,
      bulk_import: false,
      custom_branding: false,
      priority_support: false,
      analytics_retention_days: 30,
      features: [
        "1,000 links per month",
        "Basic analytics (30 days)",
        "Standard support",
        "Linklysis branding"
      ]
    },
    professional: {
      name: "Professional",
      price: 9,
      links_limit: 10_000,
      monthly_links_limit: 10_000,
      custom_domains_limit: 3,
      team_members_limit: 0,
      api_access: false,
      bulk_import: true,
      custom_branding: false,
      priority_support: true,
      analytics_retention_days: 365,
      features: [
        "10,000 links per month",
        "Advanced analytics (1 year)",
        "Up to 3 custom domains",
        "Bulk import/export",
        "Priority support",
        "Remove Linklysis branding"
      ]
    },
    business: {
      name: "Business",
      price: 29,
      links_limit: 100_000,
      monthly_links_limit: 100_000,
      custom_domains_limit: 10,
      team_members_limit: 10,
      api_access: true,
      bulk_import: true,
      custom_branding: true,
      priority_support: true,
      analytics_retention_days: 730,
      features: [
        "100,000 links per month",
        "Advanced analytics (2 years)",
        "Up to 10 custom domains",
        "Team collaboration (10 members)",
        "API access",
        "Bulk import/export",
        "Custom branding",
        "Priority support"
      ]
    },
    enterprise: {
      name: "Enterprise",
      price: 99,
      links_limit: Float::INFINITY,
      monthly_links_limit: Float::INFINITY,
      custom_domains_limit: Float::INFINITY,
      team_members_limit: Float::INFINITY,
      api_access: true,
      bulk_import: true,
      custom_branding: true,
      priority_support: true,
      analytics_retention_days: Float::INFINITY,
      features: [
        "Unlimited links",
        "Unlimited analytics retention",
        "Unlimited custom domains",
        "Unlimited team members",
        "API access",
        "Bulk import/export",
        "White-label solution",
        "SSO integration",
        "Dedicated support manager"
      ]
    }
  }.freeze

  def initialize(user)
    @user = user
  end

  # Check if user can create a new link
  def can_create_link?
    return true if enterprise_plan?
    monthly_link_count < monthly_links_limit
  end

  # Check if user can access a specific feature
  def can_access_feature?(feature)
    plan_config = current_plan_config
    return false unless plan_config

    case feature.to_sym
    when :api_access
      plan_config[:api_access]
    when :bulk_import
      plan_config[:bulk_import]
    when :custom_branding
      plan_config[:custom_branding]
    when :priority_support
      plan_config[:priority_support]
    when :team_collaboration
      plan_config[:team_members_limit] > 0
    when :custom_domains
      plan_config[:custom_domains_limit] > 0
    else
      false
    end
  end

  # Check if user can add more custom domains
  def can_add_custom_domain?
    return true if enterprise_plan?
    @user.custom_domains.count < custom_domains_limit
  end

  # Check if user can invite team members
  def can_invite_team_member?
    return true if enterprise_plan?
    @user.team_memberships.count < team_members_limit
  end

  # Get usage statistics for the current billing period
  def usage_stats
    {
      links_created_this_month: monthly_link_count,
      links_limit: monthly_links_limit,
      links_remaining: [ monthly_links_limit - monthly_link_count, 0 ].max,
      usage_percentage: usage_percentage,
      custom_domains_count: @user.custom_domains.count,
      custom_domains_limit: custom_domains_limit,
      team_members_count: @user.team_memberships.count,
      team_members_limit: team_members_limit,
      total_clicks_this_month: monthly_clicks_count
    }
  end

  # Get plan comparison data
  def self.plan_comparison
    PLANS.map do |plan_id, config|
      {
        id: plan_id.to_s,
        name: config[:name],
        price: config[:price],
        features: config[:features],
        monthly_links: config[:monthly_links_limit] == Float::INFINITY ? "Unlimited" : config[:monthly_links_limit].to_s.gsub(/\B(?=(\d{3})+(?!\d))/, ","),
        custom_domains: config[:custom_domains_limit] == Float::INFINITY ? "Unlimited" : config[:custom_domains_limit],
        team_members: config[:team_members_limit] == Float::INFINITY ? "Unlimited" : config[:team_members_limit],
        api_access: config[:api_access],
        analytics_retention: config[:analytics_retention_days] == Float::INFINITY ? "Forever" : "#{config[:analytics_retention_days]} days"
      }
    end
  end

  # Upgrade user to a specific plan
  def upgrade_to_plan(plan_id)
    plan_key = plan_id.to_sym

    unless PLANS.key?(plan_key)
      return failure([ "Invalid subscription plan" ])
    end

    # In production, this would integrate with Stripe/payment processor
    # For now, just update the user's plan
    if @user.update(subscription_plan: plan_id.to_s)
      success({
        plan: plan_key,
        message: "Successfully upgraded to #{PLANS[plan_key][:name]} plan"
      })
    else
      failure(@user.errors.full_messages)
    end
  end

  # Get limits exceeded warnings
  def limits_warnings
    warnings = []

    if usage_percentage >= 90
      warnings << {
        type: "links_limit",
        message: "You've used #{usage_percentage}% of your monthly link limit. Consider upgrading your plan.",
        severity: usage_percentage >= 95 ? "critical" : "warning"
      }
    end

    if @user.custom_domains.count >= custom_domains_limit && custom_domains_limit > 0
      warnings << {
        type: "custom_domains",
        message: "You've reached your custom domain limit (#{custom_domains_limit}). Upgrade to add more domains.",
        severity: "warning"
      }
    end

    warnings
  end

  private

  def current_plan
    (@user.subscription_plan || "free").to_sym
  end

  def current_plan_config
    PLANS[current_plan]
  end

  def enterprise_plan?
    current_plan == :enterprise
  end

  def monthly_link_count
    @monthly_link_count ||= @user.links
      .where(created_at: Date.current.beginning_of_month..Date.current.end_of_month)
      .count
  end

  def monthly_links_limit
    current_plan_config[:monthly_links_limit]
  end

  def custom_domains_limit
    current_plan_config[:custom_domains_limit]
  end

  def team_members_limit
    current_plan_config[:team_members_limit]
  end

  def usage_percentage
    return 0 if monthly_links_limit == Float::INFINITY
    ((monthly_link_count.to_f / monthly_links_limit) * 100).round(1)
  end

  def monthly_clicks_count
    @monthly_clicks_count ||= @user.links
      .joins(:link_clicks)
      .where(link_clicks: { created_at: Date.current.beginning_of_month..Date.current.end_of_month })
      .count
  end

  # Class methods for global feature checks
  def self.can_create_link?(user)
    new(user).can_create_link?
  end

  def self.can_access_feature?(user, feature)
    new(user).can_access_feature?(feature)
  end

  def self.usage_stats_for(user)
    new(user).usage_stats
  end

  def self.limits_warnings_for(user)
    new(user).limits_warnings
  end
end
