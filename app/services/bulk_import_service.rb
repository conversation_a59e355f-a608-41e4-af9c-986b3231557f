require "csv"

class BulkImportService
  attr_reader :user, :file, :team, :bulk_import

  ALLOWED_HEADERS = %w[url original_url long_url destination title tags expires_at].freeze
  MAX_FILE_SIZE = 10.megabytes
  MAX_ROWS = 10_000

  def initialize(user, file, team: nil)
    @user = user
    @file = file
    @team = team
  end

  def import
    validate_file!

    @bulk_import = create_bulk_import

    # Process synchronously for small files, use background job for large ones
    if estimated_row_count < 100
      process_import
    else
      BulkImportJob.perform_later(@bulk_import)
    end

    ServiceResult.success(data: { bulk_import: @bulk_import }, bulk_import: @bulk_import)
  rescue StandardError => e
    Rails.logger.error "Bulk import failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")

    if @bulk_import
      @bulk_import.mark_as_failed!(e.message)
    end

    ServiceResult.failure(errors: [ e.message ], error: e.message)
  end

  def process_import
    @bulk_import.mark_as_processing!

    row_number = 0
    successful_imports = 0
    failed_imports = 0

    CSV.foreach(file.path, headers: true, header_converters: :downcase) do |row|
      row_number += 1

      # Skip empty rows
      next if row.to_h.values.all?(&:blank?)

      begin
        import_row(row)
        successful_imports += 1
      rescue StandardError => e
        failed_imports += 1
        @bulk_import.add_error(row_number, e.message)
      end

      # Update progress every 10 rows
      if row_number % 10 == 0
        @bulk_import.update!(
          processed_rows: row_number,
          successful_rows: successful_imports,
          failed_rows: failed_imports
        )
      end
    end

    # Final update
    @bulk_import.update!(
      total_rows: row_number,
      processed_rows: row_number,
      successful_rows: successful_imports,
      failed_rows: failed_imports
    )

    @bulk_import.mark_as_completed!
  end

  private

  def validate_file!
    raise "No file provided" unless file.present?
    raise "File too large (max 10MB)" if file.size > MAX_FILE_SIZE
    raise "Invalid file type. Please upload a CSV file" unless valid_csv_file?

    # Quick check for row count
    row_count = estimated_row_count
    raise "Too many rows (max #{MAX_ROWS})" if row_count > MAX_ROWS

    # Validate headers
    headers = CSV.open(file.path, "r", headers: true) { |csv| csv.first&.headers&.map(&:downcase) }
    raise "CSV file is empty" if headers.nil?

    url_header = headers.find { |h| %w[url original_url long_url destination].include?(h) }
    raise "CSV must contain a URL column (url, original_url, long_url, or destination)" unless url_header
  end

  def valid_csv_file?
    return false unless file.respond_to?(:content_type)

    allowed_types = [ "text/csv", "application/csv", "application/vnd.ms-excel" ]
    allowed_types.include?(file.content_type) || file.original_filename&.downcase&.end_with?(".csv")
  end

  def estimated_row_count
    @estimated_row_count ||= begin
      count = 0
      CSV.foreach(file.path, headers: true) { count += 1 }
      count
    rescue
      0
    end
  end

  def create_bulk_import
    BulkImport.create!(
      user: user,
      team: team,
      file_name: file.original_filename,
      status: "pending",
      total_rows: estimated_row_count
    )
  end

  def import_row(row)
    # Find URL from various possible column names
    url = row["url"] || row["original_url"] || row["long_url"] || row["destination"]
    raise "No URL found in row" if url.blank?

    # Extract additional metadata
    metadata = {}
    metadata[:title] = row["title"] if row["title"].present?
    metadata[:tags] = parse_tags(row["tags"]) if row["tags"].present?

    # Parse expiration date if provided
    expires_at = nil
    if row["expires_at"].present?
      expires_at = DateTime.parse(row["expires_at"]) rescue nil
    end

    # Use LinkShorteningService to create the link
    service = LinkShorteningService.new(user)
    result = service.create_link(
      original_url: url.strip,
      team_id: team&.id,
      metadata: metadata,
      expires_at: expires_at
    )

    raise result.errors.values.flatten.join(", ") unless result.success?
  end

  def parse_tags(tags_string)
    tags_string.split(/[,;]/).map(&:strip).reject(&:blank?)
  end
end
