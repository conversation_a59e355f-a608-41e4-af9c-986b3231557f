require "csv"

class BulkImportsController < ApplicationController
  layout "dashboard"
  before_action :authenticate_user!
  before_action :set_bulk_import, only: [ :show, :download_errors ]

  def index
    @bulk_imports = current_user.bulk_imports.includes(:team).recent.limit(20)
  end

  def new
    @bulk_import = current_user.bulk_imports.build
    @teams = current_user.teams if current_user.respond_to?(:teams)
  end

  def create
    service = BulkImportService.new(
      current_user,
      params[:file],
      team: find_team
    )

    result = service.import

    if result.success?
      redirect_to bulk_import_path(result.bulk_import),
                  notice: "Import started successfully. Large imports will be processed in the background."
    else
      flash.now[:alert] = result.error
      @bulk_import = current_user.bulk_imports.build
      @teams = current_user.teams if current_user.respond_to?(:teams)
      render :new, status: :unprocessable_entity
    end
  end

  def show
    @recent_imports = current_user.bulk_imports
                                  .where.not(id: @bulk_import.id)
                                  .recent
                                  .limit(5)
  end

  def download_errors
    if @bulk_import.error_details["rows"].present?
      csv_data = generate_errors_csv
      send_data csv_data,
                filename: "import_errors_#{@bulk_import.id}.csv",
                type: "text/csv"
    else
      redirect_to bulk_import_path(@bulk_import),
                  alert: "No errors to download"
    end
  end

  def download_template
    csv_data = CSV.generate(headers: true) do |csv|
      csv << [ "url", "title", "tags", "expires_at" ]
      csv << [ "https://example.com", "Example Site", "example, demo", "2024-12-31" ]
      csv << [ "https://github.com", "GitHub", "development, code", "" ]
      csv << [ "https://google.com", "Google", "search", "2025-01-01" ]
    end

    send_data csv_data,
              filename: "linkmaster_import_template.csv",
              type: "text/csv"
  end

  private

  def set_bulk_import
    @bulk_import = current_user.bulk_imports.find(params[:id])
  end

  def find_team
    return nil unless params[:team_id].present?
    current_user.teams.find(params[:team_id]) if current_user.respond_to?(:teams)
  end

  def generate_errors_csv
    CSV.generate(headers: true) do |csv|
      csv << [ "Row Number", "Error Message" ]
      @bulk_import.error_details["rows"].each do |row_num, error|
        csv << [ row_num, error ]
      end
    end
  end
end
