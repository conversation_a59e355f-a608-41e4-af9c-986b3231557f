module Settings
  class BillingSettingsController < ApplicationController
    layout "dashboard"
    before_action :authenticate_user!

  def show
    @user = current_user
    @subscription_plans = SubscriptionService.plan_comparison
    @current_plan = current_user.subscription_plan || "free"
    @usage_stats = current_user.usage_stats
    @limits_warnings = current_user.limits_warnings
  end

  def update
    @user = current_user
    subscription_service = SubscriptionService.new(@user)

    result = subscription_service.upgrade_to_plan(billing_params[:plan])

    if result.success?
      respond_to do |format|
        format.turbo_stream {
          render turbo_stream: turbo_stream.replace(
            "billing-settings",
            partial: "billing_settings/form",
            locals: {
              user: @user.reload,
              success: result.data[:message]
            }
          )
        }
        format.html { redirect_to settings_billing_settings_path, notice: result.data[:message] }
      end
    else
      respond_to do |format|
        format.turbo_stream {
          render turbo_stream: turbo_stream.replace(
            "billing-settings",
            partial: "billing_settings/form",
            locals: {
              user: @user,
              error: result.errors.join(", ")
            }
          )
        }
        format.html {
          @subscription_plans = SubscriptionService.plan_comparison
          @current_plan = current_user.subscription_plan || "free"
          @usage_stats = current_user.usage_stats
          @limits_warnings = current_user.limits_warnings
          render :show, status: :unprocessable_entity
        }
      end
    end
  end

  private

  def billing_params
    params.require(:billing).permit(:plan)
  end
  end
end
