class Settings::CustomDomainsController < ApplicationController
  layout "dashboard"
  before_action :authenticate_user!
  before_action :set_custom_domain, only: [ :update, :destroy ]

  def index
    @custom_domains = current_user.custom_domains.order(is_primary: :desc, created_at: :desc)
    @can_add_domain = can_add_custom_domain?
  end

  def create
    service = CustomDomainService.new(current_user)
    result = service.add_domain(custom_domain_params[:domain])

    if result.success?
      @custom_domain = result.custom_domain
      @verification_instructions = result.verification_instructions

      respond_to do |format|
        format.html { redirect_to settings_custom_domains_path, notice: "Custom domain added successfully. Please verify it." }
        format.turbo_stream
      end
    else
      @errors = result.errors
      respond_to do |format|
        format.html { redirect_to settings_custom_domains_path, alert: result.errors.values.flatten.first }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response(result.errors.values.flatten.first) }
      end
    end
  end

  def update
    service = CustomDomainService.new(current_user)

    case params[:action_type]
    when "verify"
      result = service.verify_domain(@custom_domain)
    when "set_primary"
      result = service.set_primary_domain(@custom_domain)
    else
      result = CustomDomainService::ServiceResult.new(
        success: false,
        errors: { base: [ "Invalid action" ] }
      )
    end

    if result.success?
      respond_to do |format|
        format.html { redirect_to settings_custom_domains_path, notice: result.message }
        format.turbo_stream
      end
    else
      respond_to do |format|
        format.html { redirect_to settings_custom_domains_path, alert: result.errors.values.flatten.first }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response(result.errors.values.flatten.first) }
      end
    end
  end

  def destroy
    service = CustomDomainService.new(current_user)
    result = service.remove_domain(@custom_domain)

    if result.success?
      respond_to do |format|
        format.html { redirect_to settings_custom_domains_path, notice: result.message }
        format.turbo_stream
      end
    else
      respond_to do |format|
        format.html { redirect_to settings_custom_domains_path, alert: "Unable to remove domain" }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response("Unable to remove domain") }
      end
    end
  end

  private

  def set_custom_domain
    @custom_domain = current_user.custom_domains.find(params[:id])
  end

  def custom_domain_params
    params.require(:custom_domain).permit(:domain)
  end

  def can_add_custom_domain?
    case current_user.subscription_plan
    when "free"
      false
    when "professional"
      current_user.custom_domains.count < 3
    when "business"
      current_user.custom_domains.count < 10
    when "enterprise"
      true
    else
      current_user.custom_domains.count < 1
    end
  end

  def turbo_stream_error_response(error)
    turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { alert: error } })
  end
end
