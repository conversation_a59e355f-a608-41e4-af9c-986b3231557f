class LandingPagesController < ApplicationController
  def index
    # Landing page data
    @features = [
      {
        icon: "link",
        title: "Smart Link Shortening",
        description: "Create custom short links with powerful analytics and tracking capabilities"
      },
      {
        icon: "chart-bar",
        title: "Real-time Analytics",
        description: "Track clicks, locations, devices, and referrers with comprehensive dashboards"
      },
      {
        icon: "users",
        title: "Team Collaboration",
        description: "Work together with your team to manage and analyze links efficiently"
      },
      {
        icon: "code",
        title: "Developer API",
        description: "Integrate link shortening into your applications with our robust API"
      },
      {
        icon: "shield-check",
        title: "GDPR Compliant",
        description: "Privacy-first analytics with anonymized tracking and data retention policies"
      },
      {
        icon: "lightning-bolt",
        title: "Lightning Fast",
        description: "Sub-100ms redirects powered by Rails 8's Solid Trifecta architecture"
      }
    ]

    @pricing_plans = [
      {
        name: "Free",
        price: "$0",
        period: "forever",
        features: [
          "Up to 1,000 links/month",
          "Basic analytics",
          "30-day data retention",
          "Standard support"
        ],
        cta: "Get Started",
        popular: false
      },
      {
        name: "Professional",
        price: "$29",
        period: "per month",
        features: [
          "Up to 10,000 links/month",
          "Advanced analytics",
          "1-year data retention",
          "Custom domains",
          "Priority support"
        ],
        cta: "Start Free Trial",
        popular: true
      },
      {
        name: "Business",
        price: "$99",
        period: "per month",
        features: [
          "Up to 50,000 links/month",
          "Team collaboration",
          "Unlimited data retention",
          "Multiple custom domains",
          "API access",
          "Premium support"
        ],
        cta: "Start Free Trial",
        popular: false
      },
      {
        name: "Enterprise",
        price: "Custom",
        period: "contact us",
        features: [
          "Unlimited links",
          "Advanced team features",
          "SSO integration",
          "Dedicated account manager",
          "SLA guarantee",
          "Custom integrations"
        ],
        cta: "Contact Sales",
        popular: false
      }
    ]

    @testimonials = [
      {
        name: "Sarah Chen",
        role: "Marketing Director",
        company: "TechStart Inc.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah&backgroundColor=65c3ff",
        content: "LinkMaster transformed how we track our marketing campaigns. The analytics are incredible, and our click-through rates improved by 40%!",
        rating: 5
      },
      {
        name: "Marcus Rodriguez",
        role: "CEO",
        company: "Digital Agency Pro",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Marcus&backgroundColor=ffd965",
        content: "We manage links for 50+ clients. LinkMaster's team features and API integration saved us hours every week. Best investment we made!",
        rating: 5
      },
      {
        name: "Emily Thompson",
        role: "Product Manager",
        company: "E-commerce Plus",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Emily&backgroundColor=ff65d9",
        content: "The real-time analytics help us make data-driven decisions instantly. Our conversion rates are up 25% since switching to LinkMaster.",
        rating: 5
      },
      {
        name: "Alex Kumar",
        role: "Developer",
        company: "StartupHub",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alex&backgroundColor=65ff8f",
        content: "The API is well-documented and super fast. Integration took less than an hour. Performance is outstanding!",
        rating: 5
      }
    ]

    # Real statistics from the database
    total_links = Link.count
    total_clicks = LinkClick.count
    total_users = User.count

    @stats = [
      {
        number: total_links > 0 ? ActionController::Base.helpers.number_with_delimiter(total_links) : "0",
        label: "Links Created",
        color: "from-blue-500 to-indigo-500"
      },
      {
        number: total_clicks > 0 ? ActionController::Base.helpers.number_with_delimiter(total_clicks) : "0",
        label: "Clicks Tracked",
        color: "from-teal-500 to-blue-500"
      },
      {
        number: total_users > 0 ? ActionController::Base.helpers.number_with_delimiter(total_users) : "0",
        label: "Happy Users",
        color: "from-green-500 to-teal-500"
      },
      {
        number: "99.9%",
        label: "Uptime",
        color: "from-indigo-500 to-blue-600"
      }
    ]
  end
end
