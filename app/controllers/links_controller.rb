class LinksController < ApplicationController
  include Pagy::Backend

  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_link, only: [ :show, :edit, :update, :destroy, :archive, :unarchive, :qr_code ]

  def index
    @pagy, @links = pagy(current_user.links
                                      .active
                                      .search(params[:q])
                                      .order(created_at: :desc))

    respond_to do |format|
      format.html
      format.turbo_stream
    end
  end

  def archived
    @pagy, @links = pagy(current_user.links
                                      .archived
                                      .search(params[:q])
                                      .order(created_at: :desc))

    render :index
  end

  def show
    # Get recent clicks for this link
    @recent_clicks = @link.link_clicks
                          .recent
                          .limit(10)
  end

  def new
    @link = current_user.links.build(original_url: params[:url])
    preload_user_associations
  end

  def create
    @service = LinkShorteningService.new(user: current_user)
    result = @service.create_link(link_params)

    if result.success?
      @link = result.link
      respond_to do |format|
        format.html { redirect_to link_path(@link), notice: "Short link created successfully! Your link is ready to share." }
        format.turbo_stream { redirect_to link_path(@link), notice: "Short link created successfully! Your link is ready to share." }
      end
    else
      @link = current_user.links.build(link_params)
      preload_user_associations
      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response(result.errors.values.flatten.first) }
      end
    end
  end

  def edit
    preload_user_associations
  end

  def update
    # Handle custom_short_code separately
    update_params = link_params.except(:custom_short_code)

    if link_params[:custom_short_code].present? && link_params[:custom_short_code] != @link.short_code
      update_params[:short_code] = link_params[:custom_short_code]
    end

    if @link.update(update_params)
      respond_to do |format|
        format.html { redirect_to link_path(@link), notice: "Link updated successfully! Your changes have been saved." }
        format.turbo_stream { redirect_to link_path(@link), notice: "Link updated successfully! Your changes have been saved." }
      end
    else
      preload_user_associations
      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response(@link.errors.full_messages.first) }
      end
    end
  end

  def destroy
    @link.destroy!

    respond_to do |format|
      format.html { redirect_to links_url, notice: "Link was successfully deleted." }
      format.turbo_stream { render turbo_stream: turbo_stream_destroy_response }
    end
  end

  def archive
    @link.update!(is_archived: true)

    respond_to do |format|
      format.html { redirect_to links_url, notice: "Link was archived." }
      format.turbo_stream { render turbo_stream: turbo_stream_archive_response }
    end
  end

  def unarchive
    @link.update!(is_archived: false)

    respond_to do |format|
      format.html { redirect_to archived_links_url, notice: "Link was unarchived." }
      format.turbo_stream { render turbo_stream: turbo_stream_unarchive_response }
    end
  end

  def qr_code
    # Extract QR code options from parameters
    qr_options = {
      module_size: params[:module_size]&.to_i || 6,
      module_px_size: params[:module_px_size]&.to_i || 10,
      png_size: params[:png_size]&.to_i || 400,
      border_modules: params[:border_modules]&.to_i || 4,
      color: params[:color] || "000000",
      background_color: params[:background_color] || "ffffff",
      error_correction: (params[:error_correction]&.to_sym || :m),
      request: request # Pass request for analytics
    }

    respond_to do |format|
      format.svg do
        svg_data = @link.qr_code_svg(qr_options)
        send_data svg_data,
                  type: "image/svg+xml",
                  disposition: "inline",
                  filename: "#{@link.short_code}_qr.svg"
      end

      format.png do
        png_data = @link.qr_code_png(qr_options).to_s
        send_data png_data,
                  type: "image/png",
                  disposition: "attachment",
                  filename: "#{@link.short_code}_qr.png"
      end

      format.pdf do
        pdf_data = QrCodeService.new(@link, qr_options).generate_pdf
        send_data pdf_data,
                  type: "application/pdf",
                  disposition: "attachment",
                  filename: "#{@link.short_code}_qr.pdf"
      end
    end
  end

  private

  def set_link
    @link = current_user.links.find_by(id: params[:id])
    render_not_found unless @link
  end

  def link_params
    params.require(:link).permit(:original_url, :custom_short_code, :team_id, :custom_domain_id)
  end

  def render_not_found
    respond_to do |format|
      format.html { render "errors/not_found", status: :not_found, layout: false }
      format.any { head :not_found }
    end
  end

  # Turbo Stream responses
  def turbo_stream_create_response
    turbo_stream.action(:redirect, target: "turbo-frame", url: links_path)
  end

  def turbo_stream_update_response
    [
      turbo_stream.replace(@link, partial: "links/link", locals: { link: @link }),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link updated successfully!" } })
    ]
  end

  def turbo_stream_destroy_response
    [
      turbo_stream.remove(@link),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link deleted successfully!" } })
    ]
  end

  def turbo_stream_archive_response
    [
      turbo_stream.remove(@link),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link archived!" } })
    ]
  end

  def turbo_stream_unarchive_response
    [
      turbo_stream.remove(@link),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link unarchived!" } })
    ]
  end

  def turbo_stream_error_response(error)
    turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { alert: error } })
  end

  def preload_user_associations
    # Preload associations to prevent N+1 queries
    @teams = current_user.teams.to_a
    @custom_domains = current_user.custom_domains.verified.order(is_primary: :desc, domain: :asc).to_a
  end
end
