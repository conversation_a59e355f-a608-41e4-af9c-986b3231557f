require "ostruct"

class PagesController < ApplicationController
  def about
  end

  def contact
    @contact = OpenStruct.new
  end

  def create_contact
    @contact = OpenStruct.new(contact_params)

    if contact_form_valid?
      # Here you would typically send an email or save to database
      # For now, we'll just redirect with a success message
      redirect_to contact_path, notice: "Thank you for your message! We'll get back to you soon."
    else
      flash.now[:alert] = "Please fill in all required fields."
      render :contact
    end
  end

  def terms_of_service
  end

  def privacy_policy
  end

  private

  def contact_params
    params.require(:contact).permit(:name, :email, :subject, :message)
  end

  def contact_form_valid?
    @contact.name.present? && @contact.email.present? &&
    @contact.subject.present? && @contact.message.present? &&
    @contact.email.match?(/\A[\w+\-.]+@[a-z\d\-.]+\.[a-z]+\z/i)
  end
end
