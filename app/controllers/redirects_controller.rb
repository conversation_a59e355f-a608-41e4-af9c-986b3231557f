class RedirectsController < ApplicationController
  # No authentication required for public link redirects

  def show
    @link = Link.find_by(short_code: params[:short_code])

    if @link.nil?
      render "errors/not_found", status: :not_found
      return
    end

    if @link.expires_at && @link.expires_at < Time.current
      render "errors/gone", status: :gone
      return
    end

    # Track the click
    track_click

    # Redirect to the original URL with comprehensive validation
    # This validation prevents open redirect attacks by ensuring only safe URLs are redirected to
    original_url = @link.original_url
    
    if valid_redirect_url?(original_url)
      redirect_to original_url, allow_other_host: true
    else
      # Log potential security issue
      Rails.logger.warn "Invalid redirect URL detected: #{original_url} for link #{@link.id}"
      render "errors/not_found", status: :not_found
    end
  end

  private

  def valid_redirect_url?(url)
    # Comprehensive URL validation to prevent open redirect attacks
    return false if url.blank?

    begin
      uri = URI.parse(url)
      
      # Only allow HTTP and HTTPS schemes
      return false unless uri.scheme&.match?(/\Ahttps?\z/i)
      
      # Must have a valid host
      return false if uri.host.blank?
      
      # Block localhost and private IP ranges for security
      return false if uri.host.match?(/\A(localhost|127\.\d+\.\d+\.\d+|10\.\d+\.\d+\.\d+|192\.168\.\d+\.\d+|172\.(1[6-9]|2\d|3[01])\.\d+\.\d+)\z/i)
      
      # Block file:// and other dangerous schemes
      return false if uri.to_s.match?(/\A(file|ftp|data|javascript):/i)
      
      true
    rescue URI::InvalidURIError
      false
    end
  end

  def track_click
    # Skip tracking if Do Not Track is enabled
    do_not_track = request.headers["DNT"] == "1"

    # Extract UTM parameters
    utm_params = {
      utm_source: params[:utm_source],
      utm_medium: params[:utm_medium],
      utm_campaign: params[:utm_campaign],
      utm_term: params[:utm_term],
      utm_content: params[:utm_content]
    }.compact

    # Extract request information
    request_info = {
      request: request,
      do_not_track: do_not_track
    }

    # Use the AttributionTrackingService to track the click
    tracking_service = AttributionTrackingService.new(link: @link, request: request)
    tracking_service.track_click(utm_params)
  rescue => e
    # Log the error but don't prevent the redirect
    Rails.logger.error "Failed to track click: #{e.message}"
  end
end
