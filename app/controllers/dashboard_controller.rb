class DashboardController < ApplicationController
  layout "dashboard"
  before_action :authenticate_user!

  def index
    @total_links = current_user.links.count
    @total_clicks = current_user.links.joins(:link_clicks).count
    @recent_links = current_user.links.includes(:custom_domain).order(created_at: :desc).limit(5)
    @top_links = current_user.links
                            .includes(:custom_domain)
                            .left_joins(:link_clicks)
                            .group(:id)
                            .order("COUNT(link_clicks.id) DESC")
                            .limit(5)

    # Quick stats for the dashboard
    @clicks_today = current_user.links
                               .joins(:link_clicks)
                               .where(link_clicks: { clicked_at: Date.current.beginning_of_day..Date.current.end_of_day })
                               .count

    @clicks_this_week = current_user.links
                                   .joins(:link_clicks)
                                   .where(link_clicks: { clicked_at: Date.current.beginning_of_week..Date.current.end_of_week })
                                   .count

    @active_links = current_user.links.active.count
  end
end
