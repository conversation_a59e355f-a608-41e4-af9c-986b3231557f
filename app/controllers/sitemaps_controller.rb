class SitemapsController < ApplicationController
  before_action :set_format

  def index
    @pages = [
      {
        url: root_url,
        lastmod: Date.current,
        changefreq: "daily",
        priority: 1.0
      },
      {
        url: about_url,
        lastmod: Date.current,
        changefreq: "monthly",
        priority: 0.8
      },
      {
        url: pricing_url,
        lastmod: Date.current,
        changefreq: "weekly",
        priority: 0.9
      },
      {
        url: features_url,
        lastmod: Date.current,
        changefreq: "weekly",
        priority: 0.8
      },
      {
        url: blog_url,
        lastmod: Date.current,
        changefreq: "daily",
        priority: 0.7
      },
      {
        url: contact_url,
        lastmod: Date.current,
        changefreq: "monthly",
        priority: 0.6
      },
      {
        url: privacy_policy_url,
        lastmod: Date.current,
        changefreq: "yearly",
        priority: 0.3
      },
      {
        url: terms_of_service_url,
        lastmod: Date.current,
        changefreq: "yearly",
        priority: 0.3
      }
    ]

    respond_to do |format|
      format.xml { render layout: false }
    end
  end

  private

  def set_format
    request.format = :xml
  end
end
