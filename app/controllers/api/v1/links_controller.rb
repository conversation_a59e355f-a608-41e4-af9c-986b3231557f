module Api
  module V1
    class LinksController < BaseController
      include Pagy::Backend

      before_action :set_link, only: [ :show, :update, :destroy, :archive, :unarchive, :stats, :qr_code ]

      def index
        links = current_api_user.links.includes(:team, :custom_domain)
        links = apply_search(links)
        links = links.order(created_at: :desc)

        # Manual pagination for API
        page = (params[:page] || 1).to_i
        per_page = (params[:per_page] || 25).to_i
        per_page = [ per_page, 100 ].min # Max 100 per page

        total = links.count
        offset = (page - 1) * per_page
        paginated_links = links.limit(per_page).offset(offset)

        render_success({
          links: serialize_links(paginated_links),
          meta: {
            current_page: page,
            per_page: per_page,
            total_pages: (total.to_f / per_page).ceil,
            total: total
          }
        })
      end

      def show
        render_success(serialize_link(@link))
      end

      def create
        service = LinkShorteningService.new(user: current_api_user)
        result = service.create_link(link_params)

        if result.success?
          render_success(serialize_link(result.link), status: :created)
        else
          render_error("Failed to create link", result.errors)
        end
      end

      def update
        service = LinkShorteningService.new(user: current_api_user)
        result = service.update_link(@link, link_params)

        if result.success?
          render_success(serialize_link(result.link))
        else
          render_error("Failed to update link", result.errors)
        end
      end

      def destroy
        @link.destroy
        head :no_content
      end

      def archive
        service = LinkShorteningService.new(user: current_api_user)
        result = service.archive_link(@link)

        if result.success?
          render_success(serialize_link(result.link))
        else
          render_error("Failed to archive link", result.errors)
        end
      end

      def unarchive
        @link.archived_at = nil

        if @link.save
          render_success(serialize_link(@link))
        else
          render_error("Failed to unarchive link", @link.errors.to_hash)
        end
      end

      def stats
        stats = {
          total_clicks: @link.link_clicks.count,
          unique_clicks: @link.unique_visitors,
          clicks_by_date: clicks_by_date,
          clicks_by_country: clicks_by_country,
          clicks_by_device: clicks_by_device,
          top_referrers: top_referrers,
          recent_clicks: recent_clicks
        }

        render_success(stats)
      end

      def qr_code
        # Extract QR code options from parameters
        qr_options = extract_qr_options

        format = params[:format] || "svg"

        case format.downcase
        when "svg"
          svg_data = @link.qr_code_svg(qr_options)
          render_success({
            format: "svg",
            data: svg_data,
            data_url: "data:image/svg+xml;base64,#{Base64.strict_encode64(svg_data)}"
          })
        when "png"
          png_data = @link.qr_code_png(qr_options).to_s
          render_success({
            format: "png",
            data_url: "data:image/png;base64,#{Base64.strict_encode64(png_data)}"
          })
        when "pdf"
          pdf_data = @link.qr_code_pdf(qr_options)
          render_success({
            format: "pdf",
            data_url: "data:application/pdf;base64,#{Base64.strict_encode64(pdf_data)}"
          })
        when "embed"
          embed_code = @link.qr_code_embed_code(qr_options.except(:request), request)
          render_success({
            format: "embed",
            embed_code: embed_code,
            share_data: @link.qr_code_share_data
          })
        else
          render_error("Unsupported format. Use 'svg', 'png', 'pdf', or 'embed'")
        end
      end

      def bulk_qr_codes
        link_ids = params[:link_ids] || []
        format = params[:format] || "svg"
        qr_options = extract_qr_options

        if link_ids.empty?
          render_error("No link IDs provided")
          return
        end

        links = current_api_user.links.where(id: link_ids)

        if links.count != link_ids.count
          render_error("Some links not found or not accessible")
          return
        end

        qr_codes = links.map do |link|
          case format.downcase
          when "svg"
            svg_data = link.qr_code_svg(qr_options)
            {
              link_id: link.id,
              short_code: link.short_code,
              format: "svg",
              data_url: "data:image/svg+xml;base64,#{Base64.strict_encode64(svg_data)}"
            }
          when "png"
            png_data = link.qr_code_png(qr_options).to_s
            {
              link_id: link.id,
              short_code: link.short_code,
              format: "png",
              data_url: "data:image/png;base64,#{Base64.strict_encode64(png_data)}"
            }
          when "pdf"
            pdf_data = link.qr_code_pdf(qr_options)
            {
              link_id: link.id,
              short_code: link.short_code,
              format: "pdf",
              data_url: "data:application/pdf;base64,#{Base64.strict_encode64(pdf_data)}"
            }
          end
        end

        render_success({
          qr_codes: qr_codes,
          total: qr_codes.count
        })
      end

      private

      def set_link
        @link = current_api_user.links.find_by(id: params[:id])
        render_not_found("Link") unless @link
      end

      def link_params
        params.require(:link).permit(:original_url, :custom_short_code, :team_id, :expires_at)
      end

      def apply_search(links)
        return links unless params[:q].present?

        links.where(
          "original_url ILIKE :query OR short_code ILIKE :query",
          query: "%#{params[:q]}%"
        )
      end

      def serialize_link(link)
        {
          id: link.id,
          original_url: link.original_url,
          short_code: link.short_code,
          short_url: link.short_url,
          clicks_count: link.link_clicks_count,
          archived: link.is_archived,
          expires_at: link.expires_at,
          created_at: link.created_at,
          updated_at: link.updated_at
        }
      end

      def serialize_links(links)
        links.map { |link| serialize_link(link) }
      end

      def extract_qr_options
        {
          module_size: params[:module_size]&.to_i || 6,
          module_px_size: params[:module_px_size]&.to_i || 10,
          png_size: params[:png_size]&.to_i || 400,
          border_modules: params[:border_modules]&.to_i || 4,
          color: params[:color] || "000000",
          background_color: params[:background_color] || "ffffff",
          error_correction: (params[:error_correction]&.to_sym || :m),
          request: request # Pass request for analytics
        }
      end

      def clicks_by_date
        # Use groupdate directly to avoid method conflict
        clicks = @link.link_clicks.where("clicked_at >= ?", 30.days.ago)

        # Group by day and count
        result = {}
        (0..29).each do |days_ago|
          date = days_ago.days.ago.to_date
          result[date.to_s] = clicks.where(clicked_at: date.all_day).count
        end
        result
      end

      def clicks_by_country
        @link.link_clicks
          .where("tracking_data->>'country_code' IS NOT NULL")
          .group("tracking_data->>'country_code'")
          .order("count_all DESC")
          .limit(10)
          .count
      end

      def clicks_by_device
        @link.link_clicks
          .group("tracking_data->>'device_type'")
          .count
      end

      def top_referrers
        @link.link_clicks
          .where("attribution_data->>'referrer' IS NOT NULL")
          .group("attribution_data->>'referrer'")
          .order("count_all DESC")
          .limit(10)
          .count
      end

      def recent_clicks
        @link.link_clicks
          .order(created_at: :desc)
          .limit(10)
          .map do |click|
            {
              created_at: click.created_at,
              country: click.country_code,
              city: click.city,
              device_type: click.device_type,
              browser: click.browser
            }
          end
      end
    end
  end
end
