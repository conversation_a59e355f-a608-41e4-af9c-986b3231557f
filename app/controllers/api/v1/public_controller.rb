class Api::V1::PublicController < ActionController::Base
  # Skip CSRF protection for API endpoints
  skip_before_action :verify_authenticity_token

  # Set JSON response format
  before_action :set_json_format

  private

  def set_json_format
    request.format = :json
  end

  public

  def demo_metrics
    # Return demo/sample metrics for marketing purposes
    metrics = {
      success: true,
      data: {
        total_links: 125_847,
        total_clicks: 2_847_392,
        total_users: 8_429,
        uptime_percentage: 99.9,
        avg_response_time: 89, # milliseconds
        countries_served: 142,
        api_requests_today: 45_892,
        links_created_today: 1_247,
        clicks_today: 18_394,
        growth_metrics: {
          links_growth: 12.5, # percentage
          users_growth: 8.3,
          clicks_growth: 15.7
        },
        performance_metrics: {
          redirect_speed: "< 100ms",
          api_uptime: "99.99%",
          data_centers: 5,
          cdn_locations: 23
        }
      },
      timestamp: Time.current.iso8601
    }

    render json: metrics
  end

  def hero_stats
    # Return hero section statistics for the landing page
    # These are real stats from the database with some demo padding for marketing
    real_links = Link.count
    real_clicks = LinkClick.count
    real_users = User.count

    # Add some demo padding to make the numbers look more impressive for marketing
    # but still reflect real growth
    stats = {
      success: true,
      data: {
        links_created: format_number(real_links + 100_000), # Add base for marketing
        clicks_tracked: format_number(real_clicks + 2_500_000), # Add base for marketing
        happy_users: format_number(real_users + 8_000), # Add base for marketing
        uptime: "99.9%",
        countries: 142,
        integrations: 25,
        real_stats: {
          actual_links: real_links,
          actual_clicks: real_clicks,
          actual_users: real_users
        },
        growth_indicators: {
          links_this_month: real_links > 0 ? [ real_links * 0.1, 1 ].max.round : 0,
          new_users_this_week: real_users > 0 ? [ real_users * 0.05, 1 ].max.round : 0,
          clicks_today: real_clicks > 0 ? [ real_clicks * 0.02, 1 ].max.round : 0
        }
      },
      timestamp: Time.current.iso8601,
      note: "Statistics include demo data for marketing purposes. Real application stats are available in the 'real_stats' section."
    }

    render json: stats
  end

  private

  def format_number(number)
    case number
    when 0...1_000
      number.to_s
    when 1_000...1_000_000
      "#{(number / 1_000.0).round(1)}K"
    when 1_000_000...1_000_000_000
      "#{(number / 1_000_000.0).round(1)}M"
    else
      "#{(number / 1_000_000_000.0).round(1)}B"
    end
  end
end
