module Api
  module V1
    class BaseController < ActionController::API
      include ActionController::HttpAuthentication::Token::ControllerMethods

      before_action :authenticate_api_user!

      private

      def authenticate_api_user!
        authenticate_or_request_with_http_token do |token|
          result = ApiAuthenticationService.new.authenticate(token)
          if result.success?
            @current_api_user = result.user
            @current_api_token = result.api_token
            true
          else
            render_unauthorized(result.message)
            false
          end
        end
      end

      def current_api_user
        @current_api_user
      end

      def render_success(data = {}, status: :ok)
        render json: { success: true, data: data }, status: status
      end

      def render_error(message, errors = {}, status: :unprocessable_entity)
        render json: {
          success: false,
          message: message,
          errors: errors
        }, status: status
      end

      def render_unauthorized(message = "Unauthorized")
        render json: {
          success: false,
          message: message
        }, status: :unauthorized
      end

      def render_not_found(resource = "Resource")
        render json: {
          success: false,
          message: "#{resource} not found"
        }, status: :not_found
      end
    end
  end
end
