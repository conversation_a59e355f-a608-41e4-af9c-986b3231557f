require "csv"

class AnalyticsController < ApplicationController
  include Pagy::Backend

  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_date_range
  before_action :set_link, only: [ :show ]

  def index
    service = AnalyticsService.new(user: current_user, date_range: @date_range)
    result = service.dashboard_analytics

    if result.success?
      data = result.data

      # Assign variables for view compatibility
      @total_links = data[:total_links]
      @active_links = data[:active_links]
      @total_clicks = data[:total_clicks]
      @unique_visitors = data[:unique_visitors]
      @top_links = data[:top_links]
      @clicks_by_date = data[:clicks_by_date]
      @clicks_by_device = data[:clicks_by_device]
      @clicks_by_country = data[:clicks_by_country]
      @clicks_by_referrer = data[:clicks_by_referrer]
      @clicks_by_hour = data[:clicks_by_hour]
      @clicks_by_day_of_week = data[:clicks_by_day_of_week]
      @clicks_by_browser = data[:clicks_by_browser]
      @clicks_by_os = data[:clicks_by_os]
      @clicks_by_city = data[:clicks_by_city]
      @return_visitor_rate = data[:return_visitor_rate]
      @avg_clicks_per_visitor = data[:avg_clicks_per_visitor]
      @new_links_count = data[:new_links_count]
      @growth_rate = data[:growth_rate]
    else
      # Fallback to zero values if service fails
      @total_links = @active_links = @total_clicks = @unique_visitors = 0
      @top_links = []
      @clicks_by_date = @clicks_by_device = @clicks_by_country = {}
      @clicks_by_referrer = @clicks_by_hour = @clicks_by_browser = {}
      @clicks_by_os = @clicks_by_city = {}
      @clicks_by_day_of_week = {}
      @return_visitor_rate = @avg_clicks_per_visitor = @new_links_count = @growth_rate = 0

      flash.now[:alert] = "Analytics data temporarily unavailable. Please try again."
    end

    respond_to do |format|
      format.html
      format.json { render json: {
        total_links: @total_links,
        total_clicks: @total_clicks,
        unique_visitors: @unique_visitors,
        clicks_by_date: @clicks_by_date,
        clicks_by_country: @clicks_by_country,
        clicks_by_device: @clicks_by_device
      }}
      format.any { render :index }
    end
  end

  def show
    service = AnalyticsService.new(user: current_user, date_range: @date_range)
    result = service.link_analytics(@link)

    if result.success?
      data = result.data

      # Assign variables for view compatibility
      @total_clicks = data[:total_clicks]
      @unique_visitors = data[:unique_visitors]
      @clicks_by_date = data[:clicks_by_date]
      @clicks_by_country = data[:clicks_by_country]
      @clicks_by_device = data[:clicks_by_device]
      @clicks_by_browser = data[:clicks_by_browser]
      @clicks_by_os = data[:clicks_by_os]
      @top_referrers = data[:top_referrers]
      @recent_clicks = data[:recent_clicks]
      @clicks_by_hour = data[:clicks_by_hour]
      @utm_campaigns = data[:utm_campaigns]
      @utm_sources = data[:utm_sources]
      @daily_average_clicks = data[:daily_average_clicks]
      @peak_hour = data[:peak_hour]
    else
      # Fallback to zero values if service fails
      @total_clicks = @unique_visitors = 0
      @clicks_by_date = @clicks_by_country = @clicks_by_device = {}
      @clicks_by_browser = @clicks_by_os = @top_referrers = {}
      @recent_clicks = []
      @clicks_by_hour = @utm_campaigns = @utm_sources = {}
      @daily_average_clicks = 0
      @peak_hour = "N/A"

      flash.now[:alert] = "Link analytics temporarily unavailable. Please try again."
    end
  end

  def export
    service = AnalyticsService.new(user: current_user, date_range: @date_range)
    result = service.export_data

    links_data = result.success? ? result.data : []

    respond_to do |format|
      format.html {
        redirect_to analytics_path, alert: "Export is only available in CSV format. Please use the Export button."
      }
      format.csv do
        send_data generate_csv(links_data),
          filename: "analytics-export-#{Date.current}.csv",
          type: "text/csv"
      end
    end
  end

  private

  def set_date_range
    if params[:date_range].present?
      case params[:date_range]
      when "7"
        @date_range = 7.days.ago..Time.current
        @date_range_label = "Last 7 days"
      when "30"
        @date_range = 30.days.ago..Time.current
        @date_range_label = "Last 30 days"
      when "90"
        @date_range = 90.days.ago..Time.current
        @date_range_label = "Last 90 days"
      else
        @date_range = 30.days.ago..Time.current
        @date_range_label = "Last 30 days"
      end
    elsif params[:start_date].present? && params[:end_date].present?
      @date_range = Date.parse(params[:start_date])..Date.parse(params[:end_date])
      @date_range_label = "#{params[:start_date]} - #{params[:end_date]}"
    else
      @date_range = 30.days.ago..Time.current
      @date_range_label = "Last 30 days"
    end
  rescue ArgumentError
    @date_range = 30.days.ago..Time.current
    @date_range_label = "Last 30 days"
  end

  def set_link
    @link = current_user.links.includes(:custom_domain).find_by(id: params[:id])
    render_not_found unless @link
  end

  def render_not_found
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404.html", status: :not_found, layout: false }
      format.any { head :not_found }
    end
  end

  def generate_csv(data)
    CSV.generate(headers: true) do |csv|
      csv << [ "Link", "Original URL", "Total Clicks", "Clicks in Period", "Unique Visitors", "Unique in Period", "Created", "Last Clicked", "Top Country", "Top Referrer" ]

      data.each do |row|
        csv << [
          row[:link],
          row[:original_url],
          row[:total_clicks],
          row[:clicks_in_period],
          row[:unique_visitors],
          row[:unique_in_period],
          row[:created_at],
          row[:last_clicked],
          row[:top_country],
          row[:top_referrer]
        ]
      end
    end
  end
end
