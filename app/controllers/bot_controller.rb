class BotController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_bot_service

  def show
    # Show the chat interface
    @recent_messages = recent_bot_messages
  end

  def chat
    message = params[:message]&.strip

    if message.blank?
      return render json: {
        error: "Message cannot be empty"
      }, status: :unprocessable_entity
    end

    # Process message with bot service
    result = @bot_service.process_message(message)

    if result.success?
      # Store conversation history in session
      store_conversation_message(message, result.data[:response])

      render json: {
        success: true,
        response: result.data[:response],
        intent: result.data[:intent],
        suggestions: result.data[:suggestions],
        context: result.data[:context]
      }
    else
      render json: {
        success: false,
        error: result.errors.first,
        fallback_response: result.data[:fallback_response]
      }, status: :internal_server_error
    end
  end

  def suggestions
    # Return contextual suggestions based on user state
    user_context = build_user_context

    suggestions = generate_contextual_suggestions(user_context)

    render json: {
      suggestions: suggestions,
      context: user_context
    }
  end

  def feedback
    feedback_type = params[:type] # 'helpful', 'not_helpful', 'suggestion'
    message_id = params[:message_id]
    feedback_text = params[:feedback]

    # Store feedback for improvement
    store_feedback(feedback_type, message_id, feedback_text)

    render json: {
      success: true,
      message: "Thank you for your feedback!"
    }
  end

  private

  def set_bot_service
    session_context = session[:bot_context] || {}
    @bot_service = BotService.new(user: current_user, session_context: session_context)
  end

  def recent_bot_messages
    # Return recent conversation from session
    session[:bot_conversations] ||= []
    session[:bot_conversations].last(10)
  end

  def store_conversation_message(user_message, bot_response)
    session[:bot_conversations] ||= []

    conversation_entry = {
      id: SecureRandom.uuid,
      user_message: user_message,
      bot_response: bot_response,
      timestamp: Time.current.to_i,
      user_id: current_user.id
    }

    session[:bot_conversations] << conversation_entry

    # Keep only last 20 messages to prevent session bloat
    session[:bot_conversations] = session[:bot_conversations].last(20)
  end

  def build_user_context
    {
      subscription_plan: current_user.subscription_plan || "free",
      total_links: current_user.links.count,
      active_links: current_user.links.active.count,
      has_custom_domains: current_user.custom_domains.any?,
      is_team_member: current_user.teams.any?,
      recent_activity: current_user.links.recent.limit(3).exists?
    }
  end

  def generate_contextual_suggestions(context)
    base_suggestions = [ "How do I create a link?", "Show me my analytics", "Help me get started" ]

    suggestions = []

    if context[:total_links] == 0
      suggestions += [ "Create my first link", "What is link shortening?", "How do I get started?" ]
    else
      suggestions += [ "View my analytics", "How do I customize short codes?", "Export my data" ]
    end

    if context[:subscription_plan] == "free"
      suggestions += [ "What are the subscription benefits?", "How do I upgrade my plan?" ]
    end

    unless context[:has_custom_domains]
      suggestions += [ "How do custom domains work?", "Set up a custom domain" ]
    end

    unless context[:is_team_member]
      suggestions += [ "How do teams work?", "Invite team members" ]
    end

    suggestions.first(6) # Limit to 6 suggestions
  end

  def store_feedback(type, message_id, feedback_text)
    feedback_data = {
      type: type,
      message_id: message_id,
      feedback: feedback_text,
      user_id: current_user.id,
      timestamp: Time.current.to_i
    }

    # Store in session for now, could be moved to database later
    session[:bot_feedback] ||= []
    session[:bot_feedback] << feedback_data

    # Keep only last 50 feedback entries
    session[:bot_feedback] = session[:bot_feedback].last(50)

    # Log for analysis
    Rails.logger.info "Bot feedback: #{feedback_data.to_json}"
  end
end
