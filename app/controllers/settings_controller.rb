class SettingsController < ApplicationController
  layout "dashboard"
  before_action :authenticate_user!

  def show
    @user = current_user
    @current_section = params[:section] || "account"
  end

  def update_account
    @user = current_user

    if @user.update(account_params)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("account-settings", partial: "settings/sections/account", locals: { user: @user, success: true }) }
        format.html { redirect_to settings_path(section: "account"), notice: "Account settings updated successfully." }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("account-settings", partial: "settings/sections/account", locals: { user: @user }) }
        format.html { render :show, status: :unprocessable_entity }
      end
    end
  end

  def update_notifications
    @user = current_user

    if @user.update(notification_params)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("notification-settings", partial: "settings/sections/notifications", locals: { user: @user, success: true }) }
        format.html { redirect_to settings_path(section: "notifications"), notice: "Notification preferences updated successfully." }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("notification-settings", partial: "settings/sections/notifications", locals: { user: @user }) }
        format.html { render :show, status: :unprocessable_entity }
      end
    end
  end

  def update_security
    @user = current_user

    if @user.update(security_params)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("security-settings", partial: "settings/sections/security", locals: { user: @user, success: true }) }
        format.html { redirect_to settings_path(section: "security"), notice: "Security settings updated successfully." }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("security-settings", partial: "settings/sections/security", locals: { user: @user }) }
        format.html { render :show, status: :unprocessable_entity }
      end
    end
  end

  private

  def account_params
    params.require(:user).permit(:first_name, :last_name, :email, :company, :job_title, :timezone)
  end

  def notification_params
    params.require(:user).permit(:email_notifications, :marketing_emails, :weekly_reports, :real_time_alerts)
  end

  def security_params
    params.require(:user).permit(:two_factor_enabled, :session_timeout)
  end
end
