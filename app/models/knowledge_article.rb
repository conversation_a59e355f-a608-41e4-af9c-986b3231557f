class KnowledgeArticle < ApplicationRecord
  belongs_to :knowledge_category

  validates :title, presence: true
  validates :slug, uniqueness: { scope: :knowledge_category_id }, allow_blank: true
  validates :content, presence: true
  validates :position, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :helpful_count, :not_helpful_count, :view_count,
            numericality: { only_integer: true, greater_than_or_equal_to: 0 }

  before_validation :generate_slug, if: -> { title.present? && slug.blank? }
  before_validation :ensure_slug_present
  before_validation :set_defaults, on: :create

  scope :published, -> { where(published: true) }
  scope :featured, -> { where(featured: true) }
  scope :ordered, -> { order(position: :asc, created_at: :asc) }
  scope :search, ->(query) {
    return none if query.blank?
    where("title ILIKE ? OR content ILIKE ? OR keywords ILIKE ?",
          "%#{query}%", "%#{query}%", "%#{query}%")
  }

  def increment_views!
    increment!(:view_count)
  end

  def increment_helpful!
    increment!(:helpful_count)
  end

  def increment_not_helpful!
    increment!(:not_helpful_count)
  end

  def helpfulness_ratio
    total = helpful_count + not_helpful_count
    return 0 if total.zero?
    (helpful_count.to_f / total * 100).round(1)
  end

  def published?
    published && published_at.present? && published_at <= Time.current
  end

  private

  def generate_slug
    self.slug = title.parameterize
  end

  def ensure_slug_present
    self.slug = title.parameterize if slug.blank? && title.present?
  end

  def set_defaults
    self.helpful_count ||= 0
    self.not_helpful_count ||= 0
    self.view_count ||= 0
    self.position ||= 0
    self.published_at ||= Time.current if published
  end
end
