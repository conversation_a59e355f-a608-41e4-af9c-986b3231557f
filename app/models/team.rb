class Team < ApplicationRecord
  # Associations
  has_many :team_memberships, dependent: :destroy
  has_many :users, through: :team_memberships
  has_many :links, dependent: :nullify
  has_many :custom_domains, dependent: :destroy

  # Validations
  validates :name, presence: true
  validates :slug, presence: true, uniqueness: true

  # Callbacks
  before_validation :generate_slug, on: :create

  private

  def generate_slug
    return if slug.present?
    self.slug = name.parameterize if name.present?
  end
end
