class QrCodeAnalytic < ApplicationRecord
  belongs_to :link

  # Validations
  validates :format, presence: true, inclusion: { in: %w[svg png pdf] }
  validates :generated_at, presence: true

  # Scopes
  scope :recent, -> { order(generated_at: :desc) }
  scope :by_format, ->(format) { where(format: format) }
  scope :by_date_range, ->(start_date, end_date) { where(generated_at: start_date..end_date) }
  scope :today, -> { where(generated_at: Date.current.all_day) }
  scope :this_week, -> { where(generated_at: 1.week.ago..Time.current) }
  scope :this_month, -> { where(generated_at: 1.month.ago..Time.current) }

  # Store accessor for options
  store_accessor :options, :module_size, :color, :background_color, :error_correction, :png_size

  def self.track_generation(link, format, options = {}, request = nil)
    create!(
      link: link,
      format: format.to_s,
      options: options,
      generated_at: Time.current,
      ip_address: request&.remote_ip,
      user_agent: request&.user_agent
    )
  end

  def self.popular_formats
    group(:format).count.sort_by { |_, count| -count }
  end

  def self.generation_stats_for_period(period = 30.days)
    where(generated_at: period.ago..Time.current)
      .group("DATE(generated_at)")
      .count
  end

  def self.format_breakdown
    group(:format).count
  end
end
