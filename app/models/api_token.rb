class ApiToken < ApplicationRecord
  belongs_to :user

  validates :name, presence: true
  validates :token, presence: true, uniqueness: true

  scope :active, -> { where(revoked_at: nil) }

  before_validation :generate_token_if_not_provided, on: :create
  before_save :set_default_scopes

  def active?
    revoked_at.nil?
  end

  def revoke!
    update!(revoked_at: Time.current)
  end

  def refresh!
    update!(last_used_at: Time.current)
  end

  def self.generate_token
    "link_#{SecureRandom.alphanumeric(32)}"
  end

  private

  def generate_token_if_not_provided
    self.token ||= self.class.generate_token
  end

  def set_default_scopes
    self.scopes ||= [ "read", "write" ]
  end
end
