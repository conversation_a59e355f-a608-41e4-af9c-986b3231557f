class CustomDomain < ApplicationRecord
  belongs_to :user, optional: true
  belongs_to :team, optional: true

  validates :domain, presence: true, uniqueness: true
  validates :domain, format: {
    with: /\A([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}\z/i,
    message: "must be a valid domain"
  }
  validate :belongs_to_user_or_team

  scope :verified, -> { where(verified: true) }
  scope :primary, -> { where(is_primary: true) }

  before_save :downcase_domain
  before_save :ensure_single_primary_domain

  def verify!
    # In production, this would perform DNS verification
    # For now, we'll just mark as verified
    update!(verified: true, verified_at: Time.current)
  end

  def status
    return "verified" if verified?
    return "pending" if verification_attempted?
    "unverified"
  end

  def status_badge_class
    case status
    when "verified"
      "bg-green-100 text-green-800"
    when "pending"
      "bg-yellow-100 text-yellow-800"
    else
      "bg-gray-100 text-gray-800"
    end
  end

  private

  def downcase_domain
    self.domain = domain.downcase if domain.present?
  end

  def ensure_single_primary_domain
    if is_primary? && is_primary_changed?
      if user.present?
        user.custom_domains.where.not(id: id).update_all(is_primary: false)
      elsif team.present?
        team.custom_domains.where.not(id: id).update_all(is_primary: false)
      end
    end
  end

  def verification_attempted?
    verification_token.present?
  end

  def belongs_to_user_or_team
    if user.blank? && team.blank?
      errors.add(:base, "Custom domain must belong to either a user or a team")
    elsif user.present? && team.present?
      errors.add(:base, "Custom domain cannot belong to both a user and a team")
    end
  end
end
