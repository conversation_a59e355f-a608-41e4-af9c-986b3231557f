class BlogPost < ApplicationRecord
  # Validations
  validates :title, presence: true
  validates :slug, presence: true, uniqueness: true
  validates :content, presence: true

  # Scopes
  scope :published, -> { where("published_at <= ?", Time.current).order(published_at: :desc) }
  scope :featured, -> { where(featured: true) }
  scope :by_category, ->(category) { where(category: category) }

  # Callbacks
  before_validation :generate_slug, on: :create

  # Instance methods
  def to_param
    slug
  end

  def published?
    published_at.present? && published_at <= Time.current
  end

  def excerpt(length = 150)
    return "" unless content.present?
    content.gsub(/\#\#?\s.*/, "").strip.truncate(length)
  end

  private

  def generate_slug
    return if slug.present?
    self.slug = title.parameterize if title.present?
  end
end
