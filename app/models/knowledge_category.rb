class KnowledgeCategory < ApplicationRecord
  has_many :knowledge_articles, dependent: :destroy

  validates :name, presence: true, uniqueness: true
  validates :slug, uniqueness: true, allow_blank: true
  validates :position, numericality: { only_integer: true, greater_than_or_equal_to: 0 }

  before_validation :generate_slug, if: -> { name.present? && slug.blank? }
  before_validation :ensure_slug_present

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(position: :asc, created_at: :asc) }

  private

  def generate_slug
    self.slug = name.parameterize
  end

  def ensure_slug_present
    self.slug = name.parameterize if slug.blank? && name.present?
  end
end
