class BulkImport < ApplicationRecord
  belongs_to :user
  belongs_to :team, optional: true

  STATUSES = %w[pending processing completed failed].freeze

  validates :status, inclusion: { in: STATUSES }

  scope :recent, -> { order(created_at: :desc) }
  scope :by_status, ->(status) { where(status: status) }

  def pending?
    status == "pending"
  end

  def processing?
    status == "processing"
  end

  def completed?
    status == "completed"
  end

  def failed?
    status == "failed"
  end

  def success_rate
    return 0 if processed_rows.zero?
    ((successful_rows.to_f / processed_rows) * 100).round(2)
  end

  def mark_as_processing!
    update!(status: "processing", started_at: Time.current)
  end

  def mark_as_completed!
    update!(status: "completed", completed_at: Time.current)
  end

  def mark_as_failed!(error_message = nil)
    updated_error_details = error_message ? error_details.merge(general_error: error_message) : error_details

    update!(
      status: "failed",
      completed_at: Time.current,
      error_details: updated_error_details
    )
  end

  def add_error(row_number, error_message)
    errors_for_row = (error_details["rows"] ||= {})
    errors_for_row[row_number.to_s] = error_message
    save
  end

  def duration
    return nil unless started_at
    end_time = completed_at || Time.current
    end_time - started_at
  end

  def formatted_duration
    return nil unless duration

    seconds = duration.to_i
    return "#{seconds}s" if seconds < 60

    minutes = seconds / 60
    remaining_seconds = seconds % 60
    return "#{minutes}m #{remaining_seconds}s" if minutes < 60

    hours = minutes / 60
    remaining_minutes = minutes % 60
    "#{hours}h #{remaining_minutes}m"
  end
end
