class LinkClick < ApplicationRecord
  belongs_to :link, counter_cache: true

  # JSONB store accessors for flexible data
  store_accessor :attribution_data, :utm_source, :utm_medium, :utm_campaign,
                 :utm_term, :utm_content, :referrer
  store_accessor :tracking_data, :ip_address, :user_agent, :country_code,
                 :city, :region, :device_type, :browser, :os, :bot

  # Validations
  validates :link, presence: true
  validates :clicked_at, presence: true

  # Callbacks
  after_create_commit :update_analytics_cache
  after_create_commit :broadcast_real_time_update

  # Scopes
  scope :recent, -> { order(clicked_at: :desc) }
  scope :today, -> { where(clicked_at: Date.current.all_day) }
  scope :this_week, -> { where(clicked_at: 1.week.ago..Time.current) }
  scope :this_month, -> { where(clicked_at: 1.month.ago..Time.current) }
  scope :unique_by_ip, -> {
    select(Arel.sql("DISTINCT ON (tracking_data->>'ip_address') *"))
    .order(Arel.sql("tracking_data->>'ip_address'"))
  }
  scope :not_bot, -> { where("(tracking_data->>'bot')::boolean = ? OR tracking_data->>'bot' IS NULL", false) }
  scope :privacy_compliant, -> { where(dnt_enabled: false) }
  scope :expired_for_retention, -> { where("retention_expires_at < ?", Time.current) }
  scope :with_tracking_data, -> { where.not(tracking_data: {}) }


  def self.top_referrers(limit = 5)
    where.not(attribution_data: {})
         .group("attribution_data->>'referrer'")
         .count
         .sort_by { |_, count| -count }
         .first(limit)
  end

  def self.device_breakdown
    group("tracking_data->>'device_type'").count
  end

  # Privacy helper methods
  def anonymized_ip
    tracking_data["ip_address"]
  end

  def has_location_data?
    tracking_data["country_code"].present?
  end

  def retention_expired?
    retention_expires_at.present? && retention_expires_at < Time.current
  end

  def days_until_expiry
    return nil unless retention_expires_at
    ((retention_expires_at - Time.current) / 1.day).ceil
  end

  def privacy_safe?
    # Returns true if this record is safe for analytics (respects privacy settings)
    !dnt_enabled && !retention_expired?
  end

  private

  def update_analytics_cache
    AnalyticsAggregationJob.perform_later(self)
  end

  def broadcast_real_time_update
    ActionCable.server.broadcast(
      "user_#{link.user_id}_analytics",
      {
        link_id: link_id,
        total_clicks: link.reload.link_clicks_count,
        timestamp: clicked_at.iso8601
      }
    )
  end
end
