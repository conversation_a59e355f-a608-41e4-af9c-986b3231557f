class Link < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :team, optional: true
  belongs_to :custom_domain, optional: true
  has_many :link_clicks, dependent: :destroy
  has_many :qr_code_analytics, dependent: :destroy

  # Validations
  validates :original_url, presence: true, format: URI::DEFAULT_PARSER.make_regexp(%w[http https])
  validates :short_code, presence: true, uniqueness: true,
            format: { with: /\A[a-zA-Z0-9\-_]+\z/, message: "only allows letters, numbers, hyphens and underscores" }

  # Callbacks
  before_validation :generate_short_code, unless: :short_code?
  after_create_commit :extract_metadata_later
  after_update_commit :broadcast_update
  after_update_commit :clear_qr_code_cache, if: :saved_change_to_short_code?

  # Scopes
  scope :recent, -> { order(created_at: :desc) }
  scope :popular, -> { left_joins(:link_clicks).group(:id).order("COUNT(link_clicks.id) DESC") }
  scope :by_team, ->(team_id) { where(team_id: team_id) }
  scope :active, -> { where(is_archived: false) }
  scope :archived, -> { where(is_archived: true) }
  scope :search, ->(query) {
    return all if query.blank?
    where("original_url ILIKE :query OR short_code ILIKE :query OR metadata->>'title' ILIKE :query", query: "%#{query}%")
  }

  # Store accessors for metadata
  store_accessor :metadata, :title, :description, :favicon_url, :og_image_url

  # Virtual attribute for custom short code
  attr_accessor :custom_short_code

  def click_rate
    return 0 if link_clicks_count.zero?
    (unique_visitors.to_f / link_clicks_count * 100).round(2)
  end

  def unique_visitors
    link_clicks.distinct.count("tracking_data->>'ip_address'")
  end

  def short_url(request = nil)
    if custom_domain && custom_domain.verified?
      "https://#{custom_domain.domain}/#{short_code}"
    else
      base_url = request ? request.base_url : Rails.application.config.default_host
      "#{base_url}/#{short_code}"
    end
  end

  def qr_code_svg(options = {})
    QrCodeService.new(self, options.merge(format: :svg)).generate_svg
  end

  def qr_code_png(options = {})
    QrCodeService.new(self, options.merge(format: :png)).generate_png
  end

  def qr_code_data_url(options = {})
    QrCodeService.new(self, options).to_data_url
  end

  def qr_code_pdf(options = {})
    QrCodeService.new(self, options.merge(format: :pdf)).generate_pdf
  end

  def qr_code_embed_code(options = {}, request = nil)
    base_url = request ? request.base_url : Rails.application.config.default_host
    qr_url = "#{base_url}/links/#{id}/qr_code.svg"

    # Add options as query parameters
    if options.any?
      params = options.map { |k, v| "#{k}=#{v}" }.join("&")
      qr_url += "?#{params}"
    end

    <<~HTML
      <div style="text-align: center;">
        <img src="#{qr_url}" alt="QR Code for #{short_url}" style="max-width: 300px; height: auto;" />
        <p style="margin-top: 10px; font-size: 14px; color: #666;">
          <a href="#{short_url}" target="_blank">#{short_url}</a>
        </p>
      </div>
    HTML
  end

  def qr_code_share_data
    {
      twitter_url: "https://twitter.com/intent/tweet?text=#{CGI.escape("Check out this QR code for #{short_url}")}&url=#{CGI.escape(short_url)}",
      linkedin_url: "https://www.linkedin.com/sharing/share-offsite/?url=#{CGI.escape(short_url)}",
      email_subject: "QR Code for #{short_url}",
      email_body: "Hi,\n\nI wanted to share this QR code with you:\n\n#{short_url}\n\nScan the QR code to visit: #{original_url}\n\nBest regards"
    }
  end

  def qr_code_generation_stats
    {
      total_generations: qr_code_analytics.count,
      by_format: qr_code_analytics.group(:format).count,
      recent_generations: qr_code_analytics.recent.limit(10),
      popular_format: qr_code_analytics.group(:format).count.max_by { |_, count| count }&.first
    }
  end

  private

  def generate_short_code
    loop do
      self.short_code = SecureRandom.alphanumeric(6)
      break unless Link.exists?(short_code: short_code)
    end
  end

  def extract_metadata_later
    MetadataExtractionJob.perform_later(id)
  end

  def broadcast_update
    # Skip broadcasting in test environment to avoid missing partial errors
    return if Rails.env.test?

    broadcast_replace_to(
      [ user, "links" ],
      target: "link_#{id}",
      partial: "links/link",
      locals: { link: self }
    )
  end

  def clear_qr_code_cache
    QrCodeService.clear_all_cache_for_link(self)
  end
end
