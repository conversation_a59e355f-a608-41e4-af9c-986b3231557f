require "ostruct"

class QrCodeAnalyticsJob < ApplicationJob
  queue_as :default

  def perform(link_id, format, options, ip_address, user_agent)
    link = Link.find_by(id: link_id)
    return unless link

    QrCodeAnalytic.track_generation(
      link,
      format,
      options,
      OpenStruct.new(remote_ip: ip_address, user_agent: user_agent)
    )
  rescue => e
    Rails.logger.error "QrCodeAnalyticsJob failed: #{e.message}"
    # Don't re-raise to avoid job retries for analytics
  end
end
