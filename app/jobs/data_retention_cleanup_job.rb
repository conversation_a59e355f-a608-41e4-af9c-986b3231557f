# frozen_string_literal: true

# Data Retention Cleanup Job
# Removes old analytics data in compliance with GDPR and data retention policies
class DataRetentionCleanupJob < ApplicationJob
  queue_as :low_priority

  # Run this job daily to clean up old data
  def perform(retention_period: nil)
    retention_period ||= default_retention_period
    cutoff_date = retention_period.ago

    Rails.logger.info "Starting data retention cleanup for data older than #{cutoff_date}"

    # Track what we're cleaning up
    stats = {
      link_clicks_deleted: 0,
      bulk_imports_deleted: 0,
      old_jobs_deleted: 0
    }

    # Clean up old link clicks (main privacy concern)
    stats[:link_clicks_deleted] = cleanup_old_link_clicks(cutoff_date)

    # Clean up old bulk import records and their associated data
    stats[:bulk_imports_deleted] = cleanup_old_bulk_imports(cutoff_date)

    # Clean up old background job records
    stats[:old_jobs_deleted] = cleanup_old_jobs(cutoff_date)

    # Log the cleanup results
    Rails.logger.info "Data retention cleanup completed: #{stats}"

    # Optionally notify administrators about cleanup
    notify_cleanup_completion(stats, cutoff_date) if should_notify?

    stats
  rescue => e
    Rails.logger.error "Data retention cleanup failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")

    # Re-raise to trigger job retry mechanism
    raise e
  end

  private

  def default_retention_period
    # Default to 13 months (1 year + 1 month buffer) for GDPR compliance
    # Organizations typically need to balance analytics value with privacy
    Rails.application.config.data_retention_period || 13.months
  end

  def cleanup_old_link_clicks(cutoff_date)
    # Delete link clicks older than retention period
    # This is the main privacy data we need to clean up
    deleted_count = 0

    # Process in batches to avoid locking the database for too long
    LinkClick.where("clicked_at < ?", cutoff_date).find_in_batches(batch_size: 1000) do |batch|
      batch_count = batch.size
      batch.each(&:destroy) # Use destroy to trigger callbacks if needed
      deleted_count += batch_count

      Rails.logger.debug "Deleted #{batch_count} link clicks (total: #{deleted_count})"

      # Small delay to reduce database load
      sleep(0.1) if batch_count == 1000
    end

    deleted_count
  end

  def cleanup_old_bulk_imports(cutoff_date)
    # Clean up old bulk import records
    deleted_count = 0

    # Only delete completed or failed imports older than cutoff
    BulkImport.where("created_at < ? AND status IN (?)", cutoff_date, %w[completed failed])
               .find_in_batches(batch_size: 100) do |batch|
      batch_count = batch.size

      # Delete associated files first if they exist
      batch.each do |import|
        cleanup_bulk_import_files(import)
      end

      # Then delete the records
      batch.each(&:destroy)
      deleted_count += batch_count

      Rails.logger.debug "Deleted #{batch_count} bulk imports (total: #{deleted_count})"
    end

    deleted_count
  end

  def cleanup_bulk_import_files(bulk_import)
    # Clean up any associated files (if stored)
    # This would depend on how files are stored (local, S3, etc.)
    if bulk_import.respond_to?(:file_path) && bulk_import.file_path.present?
      begin
        File.delete(bulk_import.file_path) if File.exist?(bulk_import.file_path)
      rescue => e
        Rails.logger.warn "Failed to delete bulk import file #{bulk_import.file_path}: #{e.message}"
      end
    end
  end

  def cleanup_old_jobs(cutoff_date)
    # Clean up old Solid Queue job records
    # Keep successful jobs for shorter period, failed jobs for debugging
    deleted_count = 0

    # Delete successful jobs older than 30 days
    if defined?(SolidQueue)
      successful_cutoff = 30.days.ago
      failed_cutoff = 90.days.ago # Keep failed jobs longer for debugging

      begin
        # This would depend on the exact SolidQueue schema
        # Adjust based on actual SolidQueue table structure
        deleted_count += SolidQueue::Job.where("finished_at < ? AND finished_at IS NOT NULL", successful_cutoff)
                                        .where.not(finished_at: nil)
                                        .delete_all

        deleted_count += SolidQueue::Job.where("finished_at < ? AND exception_executions_count > 0", failed_cutoff)
                                        .delete_all
      rescue => e
        Rails.logger.warn "Failed to cleanup old job records: #{e.message}"
      end
    end

    deleted_count
  end

  def should_notify?
    # Only notify in production or when explicitly configured
    Rails.env.production? || Rails.application.config.notify_data_cleanup
  end

  def notify_cleanup_completion(stats, cutoff_date)
    # Send notification to administrators about cleanup completion
    # This could be email, Slack, or other notification systems

    message = <<~MESSAGE
      Data Retention Cleanup Completed

      Cutoff Date: #{cutoff_date}

      Cleanup Summary:
      - Link Clicks Deleted: #{stats[:link_clicks_deleted]}
      - Bulk Imports Deleted: #{stats[:bulk_imports_deleted]}
      - Old Jobs Deleted: #{stats[:old_jobs_deleted]}

      Total Records Cleaned: #{stats.values.sum}
    MESSAGE

    Rails.logger.info message

    # TODO: Add actual notification mechanism
    # AdminMailer.data_cleanup_notification(stats, cutoff_date).deliver_now
  end
end
