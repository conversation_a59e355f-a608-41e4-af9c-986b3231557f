class BulkImportJob < ApplicationJob
  queue_as :default

  def perform(bulk_import)
    # Re-open the file from Active Storage or temp storage
    # For now, we'll assume the file path is stored temporarily
    # In production, you'd want to use Active Storage

    service = BulkImportService.new(bulk_import.user, nil, team: bulk_import.team)
    service.instance_variable_set(:@bulk_import, bulk_import)
    service.process_import
  rescue StandardError => e
    bulk_import.mark_as_failed!(e.message)
    raise # Re-raise to mark job as failed
  end
end
