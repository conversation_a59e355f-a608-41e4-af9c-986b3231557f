require 'rails_helper'

RSpec.describe QrCodeAnalyticsJob, type: :job do
  let(:link) { create(:link) }
  let(:format) { 'svg' }
  let(:options) { { module_size: 8, color: 'ff0000' } }
  let(:ip_address) { '127.0.0.1' }
  let(:user_agent) { 'Test Browser' }

  describe '#perform' do
    it 'calls track_generation with correct parameters' do
      expect(QrCodeAnalytic).to receive(:track_generation).with(
        link,
        format,
        options,
        instance_of(OpenStruct)
      )

      described_class.new.perform(link.id, format, options, ip_address, user_agent)
    end

    it 'creates a QR code analytic record' do
      # Debug: check if link exists
      expect(link).to be_persisted

      expect {
        described_class.new.perform(link.id, format, options, ip_address, user_agent)
      }.to change(QrCodeAnalytic, :count).by(1)
    end

    it 'handles missing link gracefully' do
      expect {
        described_class.new.perform(999999, format, options, ip_address, user_agent)
      }.not_to change(QrCodeAnalytic, :count)
    end

    it 'handles errors gracefully' do
      allow(QrCodeAnalytic).to receive(:track_generation).and_raise(StandardError.new('Test error'))

      expect {
        described_class.new.perform(link.id, format, options, ip_address, user_agent)
      }.not_to raise_error
    end
  end
end
