require 'rails_helper'

RSpec.describe BulkImportJob, type: :job do
  let(:user) { create(:user) }
  let(:team) { create(:team) }
  let(:bulk_import) { create(:bulk_import, user: user, team: team) }

  describe '#perform' do
    context 'with successful processing' do
      it 'processes the bulk import via service' do
        service_double = double('BulkImportService')
        expect(BulkImportService).to receive(:new).with(user, nil, team: team).and_return(service_double)
        expect(service_double).to receive(:instance_variable_set).with(:@bulk_import, bulk_import)
        expect(service_double).to receive(:process_import)

        described_class.perform_now(bulk_import)
      end
    end

    context 'when service raises an error' do
      it 'marks bulk import as failed and re-raises error' do
        service_double = double('BulkImportService')
        error_message = 'Processing failed'
        
        allow(BulkImportService).to receive(:new).and_return(service_double)
        allow(service_double).to receive(:instance_variable_set)
        allow(service_double).to receive(:process_import).and_raise(StandardError, error_message)
        
        expect(bulk_import).to receive(:mark_as_failed!).with(error_message)
        
        expect {
          described_class.perform_now(bulk_import)
        }.to raise_error(StandardError, error_message)
      end
    end
  end

  describe 'job queue' do
    it 'is queued on default queue' do
      expect(described_class.queue_name).to eq('default')
    end
  end
end