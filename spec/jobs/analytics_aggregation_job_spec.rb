require 'rails_helper'

RSpec.describe AnalyticsAggregationJob, type: :job do
  let(:link_click) { create(:link_click, :with_tracking) }

  describe '#perform' do
    it 'accepts a link_click parameter' do
      expect { described_class.perform_now(link_click) }.not_to raise_error
    end
  end

  describe 'job queue' do
    it 'is queued on default queue' do
      expect(described_class.queue_name).to eq('default')
    end
  end
end