# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DataRetentionCleanupJob, type: :job do
  include ActiveJob::TestHelper

  let(:user) { create(:user) }
  let(:link) { create(:link, user: user) }

  describe '#perform' do
    context 'with expired link clicks' do
      let!(:old_click) do
        create(:link_click,
          link: link,
          clicked_at: 14.months.ago,
          retention_expires_at: 1.month.ago
        )
      end

      let!(:recent_click) do
        create(:link_click,
          link: link,
          clicked_at: 1.month.ago,
          retention_expires_at: 12.months.from_now
        )
      end

      it 'deletes old link clicks based on retention period' do
        expect {
          described_class.new.perform(retention_period: 13.months)
        }.to change { LinkClick.count }.by(-1)

        expect(LinkClick.exists?(old_click.id)).to be false
        expect(LinkClick.exists?(recent_click.id)).to be true
      end

      it 'returns cleanup statistics' do
        stats = described_class.new.perform(retention_period: 13.months)

        expect(stats[:link_clicks_deleted]).to eq(1)
        expect(stats[:bulk_imports_deleted]).to eq(0)
      end
    end

    context 'with expired bulk imports' do
      let!(:old_completed_import) do
        create(:bulk_import,
          user: user,
          status: 'completed',
          created_at: 14.months.ago
        )
      end

      let!(:old_failed_import) do
        create(:bulk_import,
          user: user,
          status: 'failed',
          created_at: 14.months.ago
        )
      end

      let!(:recent_import) do
        create(:bulk_import,
          user: user,
          status: 'completed',
          created_at: 1.month.ago
        )
      end

      let!(:processing_import) do
        create(:bulk_import,
          user: user,
          status: 'processing',
          created_at: 14.months.ago
        )
      end

      it 'deletes old completed and failed bulk imports but not processing ones' do
        expect {
          described_class.new.perform(retention_period: 13.months)
        }.to change { BulkImport.count }.by(-2)

        expect(BulkImport.exists?(old_completed_import.id)).to be false
        expect(BulkImport.exists?(old_failed_import.id)).to be false
        expect(BulkImport.exists?(recent_import.id)).to be true
        expect(BulkImport.exists?(processing_import.id)).to be true
      end
    end

    context 'with custom retention period' do
      let!(:six_month_old_click) do
        create(:link_click,
          link: link,
          clicked_at: 6.months.ago
        )
      end

      it 'uses custom retention period when provided' do
        expect {
          described_class.new.perform(retention_period: 3.months)
        }.to change { LinkClick.count }.by(-1)

        expect(LinkClick.exists?(six_month_old_click.id)).to be false
      end

      it 'uses default retention period when not provided' do
        # Default is 13 months, so 6-month-old click should not be deleted
        expect {
          described_class.new.perform
        }.not_to change { LinkClick.count }

        expect(LinkClick.exists?(six_month_old_click.id)).to be true
      end
    end

    context 'error handling' do
      it 'logs and re-raises errors' do
        allow(LinkClick).to receive(:where).and_raise(StandardError.new('Database error'))
        allow(Rails.logger).to receive(:error)

        expect {
          described_class.new.perform
        }.to raise_error(StandardError, 'Database error')

        expect(Rails.logger).to have_received(:error).with('Data retention cleanup failed: Database error')
      end
    end

    context 'logging' do
      it 'logs cleanup start and completion' do
        allow(Rails.logger).to receive(:info)

        described_class.new.perform(retention_period: 13.months)

        expect(Rails.logger).to have_received(:info).with(/Starting data retention cleanup/)
        expect(Rails.logger).to have_received(:info).with(/Data retention cleanup completed/)
      end
    end

    context 'batch processing' do
      let!(:many_old_clicks) do
        15.times.map do |i|
          create(:link_click,
            link: link,
            clicked_at: 14.months.ago + i.days
          )
        end
      end

      it 'processes clicks in batches' do
        # Mock the batch size to test batching behavior
        allow(LinkClick).to receive(:find_in_batches).and_call_original

        described_class.new.perform(retention_period: 13.months)

        expect(LinkClick).to have_received(:find_in_batches).with(batch_size: 1000)
      end
    end
  end

  describe 'job queue and scheduling' do
    it 'is queued on low_priority queue' do
      expect(described_class.new.queue_name).to eq('low_priority')
    end

    it 'can be enqueued' do
      expect {
        described_class.perform_later
      }.to have_enqueued_job(described_class)
    end
  end

  describe 'configuration integration' do
    it 'uses Rails configuration for retention period' do
      allow(Rails.application.config).to receive(:data_retention_period).and_return(6.months)

      job = described_class.new
      expect(job.send(:default_retention_period)).to eq(6.months)
    end

    it 'falls back to default when config is not set' do
      allow(Rails.application.config).to receive(:data_retention_period).and_return(nil)

      job = described_class.new
      expect(job.send(:default_retention_period)).to eq(13.months)
    end
  end

  describe 'notification settings' do
    context 'when notifications are enabled' do
      before do
        allow(Rails.env).to receive(:production?).and_return(true)
      end

      it 'attempts to send notifications in production' do
        allow(described_class).to receive(:new).and_return(double.as_null_object)
        job = described_class.new

        expect(job.send(:should_notify?)).to be true
      end
    end

    context 'when notifications are disabled' do
      before do
        allow(Rails.env).to receive(:production?).and_return(false)
        allow(Rails.application.config).to receive(:notify_data_cleanup).and_return(false)
      end

      it 'does not send notifications in non-production' do
        job = described_class.new

        expect(job.send(:should_notify?)).to be false
      end
    end
  end
end
