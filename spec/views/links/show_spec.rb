require 'rails_helper'

RSpec.describe "links/show", type: :view do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user) }
  let!(:recent_clicks) { create_list(:link_click, 3, link: link) }

  before do
    assign(:link, link)
    assign(:recent_clicks, recent_clicks)
    allow(view).to receive(:current_user).and_return(user)
  end

  it "renders the link details" do
    render

    expect(rendered).to include(link.short_code)
    expect(rendered).to include(link.original_url)
    expect(rendered).to include("Link Details")
  end

  it "displays link statistics" do
    render

    expect(rendered).to include("TOTAL CLICKS")
    expect(rendered).to include("UNIQUE VISITORS")
    expect(rendered).to include(link.link_clicks_count.to_s)
  end

  it "includes QR code functionality" do
    render

    expect(rendered).to include("QR Code")
    expect(rendered).to include("qr-code-preview")
    expect(rendered).to include("Download Options")
  end

  it "includes recent activity when clicks exist" do
    render

    expect(rendered).to include("Recent Activity")
  end

  it "includes sharing functionality" do
    render

    expect(rendered).to include("Copy")
    expect(rendered).to include("Share")
  end

  it "includes proper navigation links" do
    render

    expect(rendered).to include("Edit Link")
    expect(rendered).to include("Back to Links")
  end

  context "with custom domain" do
    let(:custom_domain) { create(:custom_domain, user: user, verified: true) }
    let(:link) { create(:link, user: user, custom_domain: custom_domain) }

    it "displays custom domain information" do
      render

      expect(rendered).to include("Custom Domain")
      expect(rendered).to include(custom_domain.domain)
    end
  end
end
