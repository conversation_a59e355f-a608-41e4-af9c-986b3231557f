require 'rails_helper'

RSpec.describe Bot<PERSON><PERSON>roll<PERSON>, type: :controller do
  let(:user) { create(:user) }

  before { sign_in user }

  describe 'GET #show' do
    it 'returns success' do
      get :show
      expect(response).to have_http_status(:success)
    end

    it 'assigns recent messages' do
      get :show
      expect(assigns(:recent_messages)).to be_an(Array)
    end

    it 'renders dashboard layout' do
      get :show
      expect(response).to render_template(layout: 'dashboard')
    end
  end

  describe 'POST #chat' do
    let(:bot_service) { double('BotService') }

    before do
      allow(BotService).to receive(:new).and_return(bot_service)
    end

    context 'with valid message' do
      it 'processes message through bot service' do
        result_double = double('result', success?: true, data: { response: 'Hello!' })
        expect(bot_service).to receive(:process_message).with('Hello').and_return(result_double)

        post :chat, params: { message: 'Hello' }

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['response']).to eq('Hello!')
      end
    end

    context 'with blank message' do
      it 'returns error for empty message' do
        post :chat, params: { message: '' }

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Message cannot be empty')
      end

      it 'returns error for whitespace-only message' do
        post :chat, params: { message: '   ' }

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Message cannot be empty')
      end
    end

    context 'when bot service fails' do
      it 'returns error response' do
        result_double = double('result', 
          success?: false, 
          errors: ['Service unavailable'],
          data: { fallback_response: 'Sorry, I am temporarily unavailable' }
        )
        expect(bot_service).to receive(:process_message).and_return(result_double)

        post :chat, params: { message: 'Hello' }

        expect(response).to have_http_status(:internal_server_error)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Service unavailable')
        expect(json_response['fallback_response']).to eq('Sorry, I am temporarily unavailable')
      end
    end
  end

  describe 'authentication' do
    before { sign_out user }

    it 'redirects to login when not authenticated' do
      get :show
      expect(response).to redirect_to(new_user_session_path)
    end
  end
end