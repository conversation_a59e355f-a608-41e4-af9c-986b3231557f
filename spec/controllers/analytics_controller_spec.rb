require 'rails_helper'

RSpec.describe AnalyticsController, type: :controller do
  let(:user) { create(:user) }
  let!(:link) { create(:link, user: user) }
  let!(:clicks) { create_list(:link_click, 10, :with_tracking, link: link, clicked_at: Time.current) }

  before { sign_in user }

  describe 'GET #index' do
    it 'returns success' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'assigns overview stats' do
      get :index
      expect(assigns(:total_links)).to eq(1)
      expect(assigns(:total_clicks)).to eq(10)
    end

    it 'assigns date range' do
      get :index
      expect(assigns(:date_range)).to be_present
    end
  end

  describe 'GET #show' do
    it 'returns success' do
      get :show, params: { id: link.id }
      expect(response).to have_http_status(:success)
    end

    it 'assigns the link' do
      get :show, params: { id: link.id }
      expect(assigns(:link)).to eq(link)
    end

    it 'calculates link statistics' do
      get :show, params: { id: link.id }
      expect(assigns(:total_clicks)).to eq(10)
      expect(assigns(:clicks_by_date)).to be_present
      expect(assigns(:clicks_by_country)).to be_present
      expect(assigns(:clicks_by_device)).to be_present
    end

    it 'returns 404 for non-existent link' do
      get :show, params: { id: 'invalid' }
      expect(response).to have_http_status(:not_found)
    end

    it 'returns 404 for other user link' do
      other_link = create(:link)
      get :show, params: { id: other_link.id }
      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'date filtering' do
    it 'filters by date range' do
      old_click = create(:link_click, link: link, clicked_at: 2.months.ago)

      get :index, params: { date_range: '30' }

      # Should only count recent clicks
      expect(assigns(:total_clicks)).to eq(10)
    end

    it 'supports custom date ranges' do
      get :index, params: {
        start_date: 1.week.ago.to_date.to_s,
        end_date: Date.current.to_s
      }

      expect(response).to have_http_status(:success)
    end
  end

  describe 'export functionality' do
    it 'exports analytics data as CSV' do
      get :export, params: { format: :csv }

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('text/csv')
      expect(response.headers['Content-Disposition']).to include('analytics-export')
    end

    it 'includes link data in CSV' do
      get :export, params: { format: :csv }

      csv = CSV.parse(response.body)
      expect(csv.first).to include('Link', 'Total Clicks', 'Unique Visitors')
      expect(csv.size).to eq(2) # Header + 1 link
    end
  end
end
