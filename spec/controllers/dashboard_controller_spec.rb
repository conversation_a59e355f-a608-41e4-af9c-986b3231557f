require 'rails_helper'

RSpec.describe DashboardController, type: :controller do
  let(:user) { create(:user) }

  before do
    sign_in user
  end

  describe "GET #index" do
    it "returns a success response" do
      get :index
      expect(response).to be_successful
    end

    it "assigns the required instance variables" do
      create_list(:link, 3, user: user)

      get :index

      expect(assigns(:total_links)).to eq(3)
      expect(assigns(:recent_links)).to be_present
      expect(assigns(:top_links)).to be_present
      expect(assigns(:total_clicks)).to be_present
      expect(assigns(:clicks_today)).to be_present
      expect(assigns(:clicks_this_week)).to be_present
      expect(assigns(:active_links)).to be_present
    end

    it "renders the dashboard layout" do
      get :index
      expect(response).to render_template(layout: "dashboard")
    end

    context "with links and clicks" do
      let!(:link_with_clicks) { create(:link, user: user) }
      let!(:link_without_clicks) { create(:link, user: user) }

      before do
        create_list(:link_click, 5, link: link_with_clicks)
      end

      it "shows links ordered by creation date for recent links" do
        get :index
        recent_links = assigns(:recent_links)
        expect(recent_links.first.created_at).to be >= recent_links.last.created_at
      end

      it "calculates click statistics correctly" do
        get :index
        expect(assigns(:total_clicks)).to eq(5)
      end
    end
  end
end
