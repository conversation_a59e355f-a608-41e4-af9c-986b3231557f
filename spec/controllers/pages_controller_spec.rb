require 'rails_helper'

RSpec.describe PagesController, type: :controller do
  describe 'GET #about' do
    it 'returns success' do
      get :about
      expect(response).to have_http_status(:success)
    end

    it 'renders about template' do
      get :about
      expect(response).to render_template(:about)
    end
  end

  describe 'GET #contact' do
    it 'returns success' do
      get :contact
      expect(response).to have_http_status(:success)
    end

    it 'assigns contact form object' do
      get :contact
      expect(assigns(:contact)).to be_an(OpenStruct)
    end

    it 'renders contact template' do
      get :contact
      expect(response).to render_template(:contact)
    end
  end

  describe 'POST #create_contact' do
    let(:valid_contact_params) do
      {
        contact: {
          name: '<PERSON>',
          email: '<EMAIL>',
          subject: 'Test Subject',
          message: 'Test message'
        }
      }
    end

    context 'with valid parameters' do
      it 'redirects to contact page with success notice' do
        post :create_contact, params: valid_contact_params

        expect(response).to redirect_to(contact_path)
        expect(flash[:notice]).to eq("Thank you for your message! We'll get back to you soon.")
      end

      it 'assigns contact object with form data' do
        post :create_contact, params: valid_contact_params
        
        contact = assigns(:contact)
        expect(contact.name).to eq('<PERSON>')
        expect(contact.email).to eq('<EMAIL>')
        expect(contact.subject).to eq('Test Subject')
        expect(contact.message).to eq('Test message')
      end
    end

    context 'with invalid parameters' do
      it 'renders contact template with error for missing name' do
        params = valid_contact_params
        params[:contact][:name] = ''

        post :create_contact, params: params

        expect(response).to render_template(:contact)
        expect(flash[:alert]).to eq('Please fill in all required fields.')
      end

      it 'renders contact template with error for missing email' do
        params = valid_contact_params
        params[:contact][:email] = ''

        post :create_contact, params: params

        expect(response).to render_template(:contact)
        expect(flash[:alert]).to eq('Please fill in all required fields.')
      end

      it 'renders contact template with error for invalid email format' do
        params = valid_contact_params
        params[:contact][:email] = 'invalid-email'

        post :create_contact, params: params

        expect(response).to render_template(:contact)
        expect(flash[:alert]).to eq('Please fill in all required fields.')
      end

      it 'renders contact template with error for missing subject' do
        params = valid_contact_params
        params[:contact][:subject] = ''

        post :create_contact, params: params

        expect(response).to render_template(:contact)
        expect(flash[:alert]).to eq('Please fill in all required fields.')
      end

      it 'renders contact template with error for missing message' do
        params = valid_contact_params
        params[:contact][:message] = ''

        post :create_contact, params: params

        expect(response).to render_template(:contact)
        expect(flash[:alert]).to eq('Please fill in all required fields.')
      end
    end
  end

  describe 'GET #terms_of_service' do
    it 'returns success' do
      get :terms_of_service
      expect(response).to have_http_status(:success)
    end

    it 'renders terms_of_service template' do
      get :terms_of_service
      expect(response).to render_template(:terms_of_service)
    end
  end

  describe 'GET #privacy_policy' do
    it 'returns success' do
      get :privacy_policy
      expect(response).to have_http_status(:success)
    end

    it 'renders privacy_policy template' do
      get :privacy_policy
      expect(response).to render_template(:privacy_policy)
    end
  end

  describe 'private methods' do
    describe '#contact_form_valid?' do
      let(:controller) { described_class.new }

      it 'returns true for valid contact data' do
        controller.instance_variable_set(:@contact, OpenStruct.new(
          name: 'John Doe',
          email: '<EMAIL>',
          subject: 'Test',
          message: 'Message'
        ))

        expect(controller.send(:contact_form_valid?)).to be true
      end

      it 'returns false for invalid email format' do
        controller.instance_variable_set(:@contact, OpenStruct.new(
          name: 'John Doe',
          email: 'invalid-email',
          subject: 'Test',
          message: 'Message'
        ))

        expect(controller.send(:contact_form_valid?)).to be false
      end
    end
  end
end