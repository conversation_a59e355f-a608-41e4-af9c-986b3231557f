require 'rails_helper'

RSpec.describe BulkImportsController, type: :controller do
  let(:user) { create(:user) }

  before do
    sign_in user
  end

  describe 'GET #index' do
    let!(:bulk_import1) { create(:bulk_import, user: user, created_at: 1.day.ago) }
    let!(:bulk_import2) { create(:bulk_import, user: user, created_at: 2.days.ago) }
    let!(:other_user_import) { create(:bulk_import, created_at: 1.hour.ago) }

    it 'assigns user bulk imports ordered by created_at desc' do
      get :index

      expect(assigns(:bulk_imports)).to eq([ bulk_import1, bulk_import2 ])
    end

    it 'includes team associations' do
      get :index

      expect(assigns(:bulk_imports).first.association(:team)).to be_loaded
    end

    it 'limits to 20 records' do
      create_list(:bulk_import, 25, user: user)

      get :index

      expect(assigns(:bulk_imports).count).to eq(20)
    end

    it 'renders index template' do
      get :index
      expect(response).to render_template(:index)
    end
  end

  describe 'GET #new' do
    it 'builds new bulk import' do
      get :new

      expect(assigns(:bulk_import)).to be_a_new(BulkImport)
      expect(assigns(:bulk_import).user).to eq(user)
    end

    it 'renders new template' do
      get :new
      expect(response).to render_template(:new)
    end
  end

  describe 'POST #create' do
    let(:csv_content) do
      <<~CSV
        url,title
        https://example.com,Example
        https://github.com,GitHub
      CSV
    end

    let(:csv_file) do
      fixture_file_upload(
        Rails.root.join('spec', 'fixtures', 'files', 'test_import.csv'),
        'text/csv'
      )
    end

    before do
      # Create the test file
      FileUtils.mkdir_p(Rails.root.join('spec', 'fixtures', 'files'))
      File.write(Rails.root.join('spec', 'fixtures', 'files', 'test_import.csv'), csv_content)
    end

    after do
      # Clean up test file
      File.delete(Rails.root.join('spec', 'fixtures', 'files', 'test_import.csv')) if File.exist?(Rails.root.join('spec', 'fixtures', 'files', 'test_import.csv'))
    end

    context 'with valid file' do
      before do
        # Mock the service
        service_double = double('BulkImportService')
        result_double = double('result', success?: true, bulk_import: create(:bulk_import, user: user))

        allow(BulkImportService).to receive(:new).and_return(service_double)
        allow(service_double).to receive(:import).and_return(result_double)
      end

      it 'creates bulk import via service' do
        expect(BulkImportService).to receive(:new).with(user, anything, team: nil)

        post :create, params: { file: csv_file }
      end

      it 'redirects to show page on success' do
        bulk_import = create(:bulk_import, user: user)
        result_double = double('result', success?: true, bulk_import: bulk_import)
        allow_any_instance_of(BulkImportService).to receive(:import).and_return(result_double)

        post :create, params: { file: csv_file }

        expect(response).to redirect_to(bulk_import_path(bulk_import))
        expect(flash[:notice]).to eq('Import started successfully. Large imports will be processed in the background.')
      end
    end

    context 'with invalid file' do
      before do
        # Mock the service to return failure
        service_double = double('BulkImportService')
        result_double = double('result', success?: false, error: 'Invalid file format')

        allow(BulkImportService).to receive(:new).and_return(service_double)
        allow(service_double).to receive(:import).and_return(result_double)
      end

      it 'renders new template with error' do
        post :create, params: { file: csv_file }

        expect(response).to render_template(:new)
        expect(flash[:alert]).to eq('Invalid file format')
      end
    end

    context 'with team parameter' do
      let(:team) { create(:team) }

      before do
        # Create team membership
        create(:team_membership, user: user, team: team)
      end

      it 'passes team to service' do
        service_double = double('BulkImportService')
        result_double = double('result', success?: true, bulk_import: create(:bulk_import, user: user, team: team))
        
        expect(BulkImportService).to receive(:new).with(user, anything, team: team).and_return(service_double)
        expect(service_double).to receive(:import).and_return(result_double)

        post :create, params: { file: csv_file, team_id: team.id }
      end
    end
  end

  describe 'GET #show' do
    let!(:bulk_import) { create(:bulk_import, user: user) }
    let!(:recent_import1) { create(:bulk_import, user: user, created_at: 1.day.ago) }
    let!(:recent_import2) { create(:bulk_import, user: user, created_at: 2.days.ago) }

    it 'assigns the bulk import' do
      get :show, params: { id: bulk_import.id }

      expect(assigns(:bulk_import)).to eq(bulk_import)
    end

    it 'assigns recent imports excluding current' do
      get :show, params: { id: bulk_import.id }

      recent_imports = assigns(:recent_imports)
      expect(recent_imports).to include(recent_import1, recent_import2)
      expect(recent_imports).not_to include(bulk_import)
      expect(recent_imports.count).to be <= 5
    end

    it 'renders show template' do
      get :show, params: { id: bulk_import.id }
      expect(response).to render_template(:show)
    end

    context 'when bulk import belongs to other user' do
      let(:other_import) { create(:bulk_import) }

      it 'raises record not found' do
        expect {
          get :show, params: { id: other_import.id }
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe 'GET #download_errors' do
    let(:bulk_import) { create(:bulk_import, :with_errors, user: user) }

    it 'sends CSV data' do
      get :download_errors, params: { id: bulk_import.id }

      expect(response.headers['Content-Type']).to eq('text/csv')
      expect(response.headers['Content-Disposition']).to include("import_errors_#{bulk_import.id}.csv")
    end

    it 'includes error data in CSV' do
      get :download_errors, params: { id: bulk_import.id }

      csv_content = response.body
      expect(csv_content).to include('Row Number,Error Message')
      expect(csv_content).to include('1,Invalid URL format')
      expect(csv_content).to include('3,Missing required field')
    end

    context 'when no errors exist' do
      let(:bulk_import) { create(:bulk_import, user: user) }

      it 'redirects with alert' do
        get :download_errors, params: { id: bulk_import.id }

        expect(response).to redirect_to(bulk_import_path(bulk_import))
        expect(flash[:alert]).to eq('No errors to download')
      end
    end
  end

  describe 'GET #download_template' do
    it 'sends CSV template' do
      get :download_template

      expect(response.headers['Content-Type']).to eq('text/csv')
      expect(response.headers['Content-Disposition']).to include('linkmaster_import_template.csv')
    end

    it 'includes sample data' do
      get :download_template

      csv_content = response.body
      expect(csv_content).to include('url,title,tags,expires_at')
      expect(csv_content).to include('https://example.com')
      expect(csv_content).to include('https://github.com')
    end
  end
end
