require 'rails_helper'

RSpec.describe GeolocationService do
  describe '.lookup' do
    it 'delegates to instance method' do
      service_instance = instance_double(GeolocationService)
      allow(GeolocationService).to receive(:new).and_return(service_instance)
      allow(service_instance).to receive(:lookup).with('*******').and_return({ country_code: 'US' })

      result = GeolocationService.lookup('*******')
      expect(result).to eq({ country_code: 'US' })
    end
  end

  describe '#lookup' do
    let(:service) { GeolocationService.new }

    context 'with valid public IP addresses' do
      context 'in development environment' do
        before { allow(Rails.env).to receive(:development?).and_return(true) }

        it 'returns mock data for Google DNS (*******)' do
          result = service.lookup('*******')
          
          expect(result).to eq({
            country_code: 'US',
            city: 'Mountain View',
            region: 'California'
          })
        end

        it 'returns mock data for Cloudflare DNS (*******)' do
          result = service.lookup('*******')
          
          expect(result).to eq({
            country_code: 'US',
            city: 'San Francisco',
            region: 'California'
          })
        end

        it 'returns default mock data for unknown public IPs' do
          result = service.lookup('***********')
          
          expect(result).to eq({
            country_code: 'XX',
            city: 'Unknown',
            region: 'Unknown'
          })
        end
      end

      context 'in test environment' do
        before { allow(Rails.env).to receive(:test?).and_return(true) }

        it 'returns mock data for known IPs' do
          result = service.lookup('*******')
          
          expect(result).to eq({
            country_code: 'US',
            city: 'Mountain View',
            region: 'California'
          })
        end
      end

      context 'in production environment' do
        before do
          allow(Rails.env).to receive(:development?).and_return(false)
          allow(Rails.env).to receive(:test?).and_return(false)
        end

        it 'returns nil (no external service integration)' do
          result = service.lookup('*******')
          expect(result).to be_nil
        end
      end
    end

    context 'with private IP addresses' do
      private_ips = [
        '********',      # Private Class A
        '**********',    # Private Class B
        '***********',   # Private Class C
        '127.0.0.1',     # Loopback
        '::1',           # IPv6 loopback
        'fc00::1'        # IPv6 private
      ]

      private_ips.each do |ip|
        it "returns nil for private IP #{ip}" do
          result = service.lookup(ip)
          expect(result).to be_nil
        end
      end
    end

    context 'with edge cases' do
      it 'returns nil for blank IP address' do
        expect(service.lookup('')).to be_nil
        expect(service.lookup(nil)).to be_nil
      end

      it 'returns nil for whitespace-only IP address' do
        expect(service.lookup('   ')).to be_nil
      end

      it 'returns default mock data for invalid IP address format' do
        # Invalid IPs don't raise errors in private_ip? (rescued), so they get mock data
        expect(service.lookup('invalid-ip')).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
        expect(service.lookup('999.999.999.999')).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
        expect(service.lookup('not-an-ip')).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
      end

      it 'handles malformed IP addresses gracefully with mock data' do
        # Malformed IPs are treated as non-private and get default mock data
        expect(service.lookup('192.168.1')).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
        expect(service.lookup('***********.1')).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
        expect(service.lookup('192.168..1')).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
      end
    end

    context 'IPv6 addresses' do
      before { allow(Rails.env).to receive(:development?).and_return(true) }

      it 'handles valid public IPv6 addresses' do
        result = service.lookup('2001:4860:4860::8888')  # Google IPv6 DNS
        expect(result).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
      end

      it 'rejects IPv6 loopback' do
        result = service.lookup('::1')
        expect(result).to be_nil
      end

      it 'rejects IPv6 private addresses' do
        result = service.lookup('fc00::1')
        expect(result).to be_nil
      end
    end
  end

  describe '#private_ip?' do
    let(:service) { GeolocationService.new }

    context 'with private IPv4 addresses' do
      private_ipv4_ranges = [
        '********',      # Class A private
        '**************', # Class A private boundary
        '**********',    # Class B private
        '**************', # Class B private boundary
        '***********',   # Class C private
        '***************', # Class C private boundary
        '127.0.0.1',     # Loopback
        '***************' # Loopback boundary
      ]

      private_ipv4_ranges.each do |ip|
        it "identifies #{ip} as private" do
          expect(service.send(:private_ip?, ip)).to be true
        end
      end
    end

    context 'with public IPv4 addresses' do
      public_ipv4_addresses = [
        '*******',       # Google DNS
        '*******',       # Cloudflare DNS
        '**************', # OpenDNS
        '*******',       # Quad9 DNS
        '***********'    # Documentation range (but not private)
      ]

      public_ipv4_addresses.each do |ip|
        it "identifies #{ip} as public" do
          expect(service.send(:private_ip?, ip)).to be false
        end
      end
    end

    context 'with private IPv6 addresses' do
      private_ipv6_addresses = [
        '::1',           # Loopback
        'fc00::1',       # Private
        'fd00::1'        # Private
      ]

      private_ipv6_addresses.each do |ip|
        it "identifies #{ip} as private" do
          expect(service.send(:private_ip?, ip)).to be true
        end
      end
    end

    context 'with public IPv6 addresses' do
      public_ipv6_addresses = [
        '2001:4860:4860::8888', # Google IPv6 DNS
        '2606:4700:4700::1111'  # Cloudflare IPv6 DNS
      ]

      public_ipv6_addresses.each do |ip|
        it "identifies #{ip} as public" do
          expect(service.send(:private_ip?, ip)).to be false
        end
      end
    end

    context 'with invalid IP addresses' do
      invalid_ips = [
        'invalid',
        '999.999.999.999',
        '192.168.1',
        '***********.1',
        '',
        nil
      ]

      invalid_ips.each do |ip|
        it "handles invalid IP '#{ip}' gracefully" do
          expect(service.send(:private_ip?, ip)).to be false
        end
      end
    end
  end

  describe '#mock_geolocation_data' do
    let(:service) { GeolocationService.new }

    it 'returns correct data for Google DNS' do
      result = service.send(:mock_geolocation_data, '*******')
      expect(result).to eq({
        country_code: 'US',
        city: 'Mountain View',
        region: 'California'
      })
    end

    it 'returns correct data for Cloudflare DNS' do
      result = service.send(:mock_geolocation_data, '*******')
      expect(result).to eq({
        country_code: 'US',
        city: 'San Francisco',
        region: 'California'
      })
    end

    it 'returns default data for unknown IPs' do
      result = service.send(:mock_geolocation_data, '***********')
      expect(result).to eq({
        country_code: 'XX',
        city: 'Unknown',
        region: 'Unknown'
      })
    end

    it 'returns consistent default data for any unknown IP' do
      unknown_ips = ['************', '*************', '2001:db8::1']
      
      unknown_ips.each do |ip|
        result = service.send(:mock_geolocation_data, ip)
        expect(result).to eq({
          country_code: 'XX',
          city: 'Unknown',
          region: 'Unknown'
        })
      end
    end
  end

  describe 'integration scenarios' do
    let(:service) { GeolocationService.new }

    context 'realistic usage patterns' do
      before { allow(Rails.env).to receive(:development?).and_return(true) }

      it 'handles typical web request scenario' do
        # Simulate typical user IP addresses
        user_ips = ['*******', '*******', '***********']
        
        results = user_ips.map { |ip| service.lookup(ip) }
        
        expect(results).to all(be_a(Hash))
        expect(results).to all(have_key(:country_code))
        expect(results).to all(have_key(:city))
        expect(results).to all(have_key(:region))
      end

      it 'properly filters out private IPs in mixed request batch' do
        mixed_ips = ['*******', '***********', '*******', '********', '***********']
        
        results = mixed_ips.map { |ip| service.lookup(ip) }
        
        # Should have 3 valid results (public IPs) and 2 nil results (private IPs)
        valid_results = results.compact
        expect(valid_results.length).to eq(3)
        expect(results.count(&:nil?)).to eq(2)
      end
    end
  end
end