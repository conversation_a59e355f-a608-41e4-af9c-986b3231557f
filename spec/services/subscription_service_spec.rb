require 'rails_helper'

RSpec.describe SubscriptionService, type: :service do
  let(:user) { create(:user, subscription_plan: :free) }
  let(:service) { described_class.new(user) }

  describe 'PLANS constant' do
    it 'defines all required plans' do
      expect(described_class::PLANS.keys).to match_array([ :free, :professional, :business, :enterprise ])
    end

    it 'includes all required fields for each plan' do
      described_class::PLANS.each do |plan_id, config|
        expect(config).to include(
          :name, :price, :links_limit, :monthly_links_limit,
          :custom_domains_limit, :team_members_limit, :api_access,
          :bulk_import, :custom_branding, :priority_support,
          :analytics_retention_days, :features
        )
      end
    end
  end

  describe '#can_create_link?' do
    context 'with free plan' do
      it 'returns true when under limit' do
        expect(service.can_create_link?).to be true
      end

      it 'returns false when at limit' do
        # Stub the monthly link count to simulate being at limit
        allow(service).to receive(:monthly_link_count).and_return(1000)
        expect(service.can_create_link?).to be false
      end
    end

    context 'with enterprise plan' do
      let(:user) { create(:user, subscription_plan: :enterprise) }

      it 'always returns true' do
        allow(service).to receive(:monthly_link_count).and_return(999_999)
        expect(service.can_create_link?).to be true
      end
    end
  end

  describe '#can_access_feature?' do
    context 'with free plan' do
      it 'denies premium features' do
        expect(service.can_access_feature?(:api_access)).to be false
        expect(service.can_access_feature?(:bulk_import)).to be false
        expect(service.can_access_feature?(:custom_branding)).to be false
        expect(service.can_access_feature?(:priority_support)).to be false
        expect(service.can_access_feature?(:team_collaboration)).to be false
        expect(service.can_access_feature?(:custom_domains)).to be false
      end
    end

    context 'with professional plan' do
      let(:user) { create(:user, subscription_plan: :professional) }

      it 'allows professional features' do
        expect(service.can_access_feature?(:bulk_import)).to be true
        expect(service.can_access_feature?(:priority_support)).to be true
        expect(service.can_access_feature?(:custom_domains)).to be true
      end

      it 'denies business+ features' do
        expect(service.can_access_feature?(:api_access)).to be false
        expect(service.can_access_feature?(:team_collaboration)).to be false
      end
    end

    context 'with business plan' do
      let(:user) { create(:user, subscription_plan: :business) }

      it 'allows business features' do
        expect(service.can_access_feature?(:api_access)).to be true
        expect(service.can_access_feature?(:team_collaboration)).to be true
        expect(service.can_access_feature?(:custom_branding)).to be true
      end
    end

    context 'with enterprise plan' do
      let(:user) { create(:user, subscription_plan: :enterprise) }

      it 'allows all features' do
        expect(service.can_access_feature?(:api_access)).to be true
        expect(service.can_access_feature?(:bulk_import)).to be true
        expect(service.can_access_feature?(:custom_branding)).to be true
        expect(service.can_access_feature?(:priority_support)).to be true
        expect(service.can_access_feature?(:team_collaboration)).to be true
        expect(service.can_access_feature?(:custom_domains)).to be true
      end
    end
  end

  describe '#can_add_custom_domain?' do
    context 'with free plan' do
      it 'returns false' do
        expect(service.can_add_custom_domain?).to be false
      end
    end

    context 'with professional plan' do
      let(:user) { create(:user, subscription_plan: :professional) }

      it 'returns true when under limit' do
        expect(service.can_add_custom_domain?).to be true
      end

      it 'returns false when at limit' do
        create_list(:custom_domain, 3, user: user)
        expect(service.can_add_custom_domain?).to be false
      end
    end

    context 'with enterprise plan' do
      let(:user) { create(:user, subscription_plan: :enterprise) }

      it 'always returns true' do
        create_list(:custom_domain, 100, user: user)
        expect(service.can_add_custom_domain?).to be true
      end
    end
  end

  describe '#usage_stats' do
    before do
      # Create some links for this month
      create_list(:link, 5, user: user, created_at: 2.weeks.ago)
      create_list(:link, 3, user: user, created_at: 2.months.ago)
    end

    it 'returns comprehensive usage statistics' do
      stats = service.usage_stats

      expect(stats).to include(
        :links_created_this_month,
        :links_limit,
        :links_remaining,
        :usage_percentage,
        :custom_domains_count,
        :custom_domains_limit,
        :team_members_count,
        :team_members_limit,
        :total_clicks_this_month
      )
    end

    it 'calculates usage percentage correctly' do
      allow(service).to receive(:monthly_link_count).and_return(500)
      stats = service.usage_stats

      expect(stats[:usage_percentage]).to eq(50.0)
    end

    it 'calculates remaining links correctly' do
      allow(service).to receive(:monthly_link_count).and_return(750)
      stats = service.usage_stats

      expect(stats[:links_remaining]).to eq(250)
    end
  end

  describe '#upgrade_to_plan' do
    it 'successfully upgrades to a valid plan' do
      result = service.upgrade_to_plan('professional')

      expect(result).to be_success
      expect(result.data[:plan]).to eq(:professional)
      expect(user.reload.subscription_plan).to eq('professional')
    end

    it 'fails with invalid plan' do
      result = service.upgrade_to_plan('invalid_plan')

      expect(result).to be_failure
      expect(result.errors).to include('Invalid subscription plan')
    end
  end

  describe '#limits_warnings' do
    it 'returns warnings when approaching limits' do
      allow(service).to receive(:usage_percentage).and_return(95)
      warnings = service.limits_warnings

      expect(warnings).not_to be_empty
      expect(warnings.first[:type]).to eq('links_limit')
      expect(warnings.first[:severity]).to eq('critical')
    end

    it 'returns no warnings when usage is low' do
      allow(service).to receive(:usage_percentage).and_return(50)
      warnings = service.limits_warnings

      expect(warnings).to be_empty
    end
  end

  describe '.plan_comparison' do
    it 'returns formatted plan data for UI' do
      plans = described_class.plan_comparison

      expect(plans).to be_an(Array)
      expect(plans.length).to eq(4)

      plans.each do |plan|
        expect(plan).to include(
          :id, :name, :price, :features, :monthly_links,
          :custom_domains, :team_members, :api_access, :analytics_retention
        )
      end
    end

    it 'formats unlimited values correctly' do
      enterprise_plan = described_class.plan_comparison.find { |p| p[:id] == 'enterprise' }

      expect(enterprise_plan[:monthly_links]).to eq('Unlimited')
      expect(enterprise_plan[:custom_domains]).to eq('Unlimited')
      expect(enterprise_plan[:analytics_retention]).to eq('Forever')
    end
  end

  describe 'class methods' do
    describe '.can_create_link?' do
      it 'delegates to instance method' do
        expect(described_class.can_create_link?(user)).to be true
      end
    end

    describe '.can_access_feature?' do
      it 'delegates to instance method' do
        expect(described_class.can_access_feature?(user, :api_access)).to be false
      end
    end

    describe '.usage_stats_for' do
      it 'delegates to instance method' do
        stats = described_class.usage_stats_for(user)
        expect(stats).to be_a(Hash)
      end
    end

    describe '.limits_warnings_for' do
      it 'delegates to instance method' do
        warnings = described_class.limits_warnings_for(user)
        expect(warnings).to be_an(Array)
      end
    end
  end
end
