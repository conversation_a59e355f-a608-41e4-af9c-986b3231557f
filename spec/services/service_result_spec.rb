require 'rails_helper'

RSpec.describe ServiceResult do
  describe '#initialize' do
    it 'creates a successful result with data' do
      result = ServiceResult.new(success: true, data: { id: 1, name: 'Test' })
      
      expect(result.success?).to be true
      expect(result.failure?).to be false
      expect(result.data).to eq({ id: 1, name: 'Test' })
      expect(result.errors).to eq([])
    end

    it 'creates a failed result with errors' do
      result = ServiceResult.new(success: false, errors: ['Something went wrong'])
      
      expect(result.success?).to be false
      expect(result.failure?).to be true
      expect(result.data).to be_nil
      expect(result.errors).to eq(['Something went wrong'])
    end

    it 'handles extra attributes dynamically' do
      result = ServiceResult.new(
        success: true, 
        data: { id: 1 },
        message: 'Operation completed',
        status_code: 200,
        metadata: { source: 'api' }
      )
      
      expect(result.success?).to be true
      expect(result.data).to eq({ id: 1 })
      expect(result.message).to eq('Operation completed')
      expect(result.status_code).to eq(200)
      expect(result.metadata).to eq({ source: 'api' })
    end

    it 'normalizes different error formats' do
      # String errors
      result1 = ServiceResult.new(success: false, errors: 'Single error')
      expect(result1.errors).to eq(['Single error'])

      # Array errors  
      result2 = ServiceResult.new(success: false, errors: ['Error 1', 'Error 2'])
      expect(result2.errors).to eq(['Error 1', 'Error 2'])

      # Hash errors
      result3 = ServiceResult.new(success: false, errors: { name: ['is required'] })
      expect(result3.errors).to eq({ name: ['is required'] })

      # Nil errors
      result4 = ServiceResult.new(success: false, errors: nil)
      expect(result4.errors).to eq([])
    end

    it 'normalizes ActiveModel::Errors' do
      # Create a named class with ActiveModel::Validations
      test_class = Class.new do
        include ActiveModel::Validations
        attr_accessor :name

        validates :name, presence: true
        
        def self.name
          'TestClass'
        end
      end

      instance = test_class.new
      instance.valid? # This will populate errors

      result = ServiceResult.new(success: false, errors: instance.errors)
      expect(result.errors).to be_a(Hash)
      expect(result.errors[:name]).to include("can't be blank")
    end

    it 'normalizes other object types to string' do
      result = ServiceResult.new(success: false, errors: 12345)
      expect(result.errors).to eq(['12345'])
    end
  end

  describe '.success' do
    it 'creates a successful result with data' do
      result = ServiceResult.success(data: { id: 1, name: 'Test' })
      
      expect(result.success?).to be true
      expect(result.failure?).to be false
      expect(result.data).to eq({ id: 1, name: 'Test' })
      expect(result.errors).to eq([])
    end

    it 'creates a successful result without data' do
      result = ServiceResult.success
      
      expect(result.success?).to be true
      expect(result.data).to be_nil
      expect(result.errors).to eq([])
    end

    it 'handles extra attributes' do
      result = ServiceResult.success(
        data: { id: 1 },
        message: 'Created successfully',
        created_id: 42
      )
      
      expect(result.success?).to be true
      expect(result.data).to eq({ id: 1 })
      expect(result.message).to eq('Created successfully')
      expect(result.created_id).to eq(42)
    end
  end

  describe '.failure' do
    it 'creates a failed result with errors' do
      result = ServiceResult.failure(errors: ['Something went wrong'])
      
      expect(result.success?).to be false
      expect(result.failure?).to be true
      expect(result.data).to be_nil
      expect(result.errors).to eq(['Something went wrong'])
    end

    it 'creates a failed result with data and errors' do
      result = ServiceResult.failure(
        data: { partial_results: [] },
        errors: ['Validation failed']
      )
      
      expect(result.failure?).to be true
      expect(result.data).to eq({ partial_results: [] })
      expect(result.errors).to eq(['Validation failed'])
    end

    it 'creates a failed result without errors' do
      result = ServiceResult.failure
      
      expect(result.failure?).to be true
      expect(result.errors).to eq([])
    end

    it 'handles extra attributes' do
      result = ServiceResult.failure(
        errors: ['Invalid input'],
        error_code: 422,
        retry_after: 30
      )
      
      expect(result.failure?).to be true
      expect(result.errors).to eq(['Invalid input'])
      expect(result.error_code).to eq(422)
      expect(result.retry_after).to eq(30)
    end
  end

  describe 'dynamic attribute access' do
    let(:result) do
      ServiceResult.new(
        success: true,
        data: { test: 'data' },
        custom_field: 'custom_value',
        nested_object: { key: 'value' }
      )
    end

    it 'provides access to custom attributes' do
      expect(result.custom_field).to eq('custom_value')
      expect(result.nested_object).to eq({ key: 'value' })
    end

    it 'responds to custom attributes' do
      expect(result.respond_to?(:custom_field)).to be true
      expect(result.respond_to?(:nested_object)).to be true
      expect(result.respond_to?(:nonexistent_field)).to be false
    end

    it 'raises NoMethodError for undefined attributes' do
      expect { result.nonexistent_method }.to raise_error(NoMethodError)
    end

    it 'handles method calls with arguments properly' do
      # The dynamic method doesn't accept arguments, so it should raise ArgumentError
      expect { result.custom_field('arg') }.to raise_error(ArgumentError, /wrong number of arguments/)
    end
  end

  describe 'backward compatibility scenarios' do
    it 'supports legacy service patterns' do
      # Simulate a service that used to return specific attributes
      result = ServiceResult.success(
        data: { user_id: 1 },
        user: { id: 1, name: 'John' },
        token: 'abc123',
        expires_at: Time.current + 1.hour
      )

      expect(result.success?).to be true
      expect(result.user).to eq({ id: 1, name: 'John' })
      expect(result.token).to eq('abc123')
      expect(result.expires_at).to be_a(Time)
    end

    it 'works with different error handling patterns' do
      # String error
      result1 = ServiceResult.failure(errors: 'Authentication failed')
      expect(result1.errors).to eq(['Authentication failed'])

      # Multiple errors
      result2 = ServiceResult.failure(errors: ['Name is required', 'Email is invalid'])
      expect(result2.errors).to eq(['Name is required', 'Email is invalid'])

      # Validation errors (hash format)
      result3 = ServiceResult.failure(errors: { 
        name: ['is required', 'is too short'], 
        email: ['is invalid'] 
      })
      expect(result3.errors).to eq({
        name: ['is required', 'is too short'],
        email: ['is invalid']
      })
    end
  end

  describe 'real-world usage patterns' do
    context 'service layer integration' do
      it 'handles typical service success scenario' do
        # Simulate a user creation service
        result = ServiceResult.success(
          data: { id: 1, email: '<EMAIL>' },
          message: 'User created successfully',
          created_at: Time.current
        )

        expect(result.success?).to be true
        expect(result.data[:email]).to eq('<EMAIL>')
        expect(result.message).to eq('User created successfully')
        expect(result.created_at).to be_a(Time)
      end

      it 'handles typical service failure scenario' do
        # Simulate a validation failure
        result = ServiceResult.failure(
          errors: { email: ['has already been taken'], name: ['is too short'] },
          error_code: 'VALIDATION_FAILED',
          suggested_action: 'Please correct the errors and try again'
        )

        expect(result.failure?).to be true
        expect(result.errors[:email]).to include('has already been taken')
        expect(result.error_code).to eq('VALIDATION_FAILED')
        expect(result.suggested_action).to eq('Please correct the errors and try again')
      end
    end

    context 'controller integration patterns' do
      it 'supports controller response patterns' do
        # Success response
        success_result = ServiceResult.success(
          data: { users: [] },
          pagination: { page: 1, per_page: 20, total: 0 },
          meta: { request_id: 'req_123' }
        )

        expect(success_result.success?).to be true
        expect(success_result.data[:users]).to eq([])
        expect(success_result.pagination[:page]).to eq(1)
        expect(success_result.meta[:request_id]).to eq('req_123')

        # Error response
        error_result = ServiceResult.failure(
          errors: ['Resource not found'],
          status_code: 404,
          error_type: 'NOT_FOUND'
        )

        expect(error_result.failure?).to be true
        expect(error_result.status_code).to eq(404)
        expect(error_result.error_type).to eq('NOT_FOUND')
      end
    end

    context 'chaining and composition' do
      it 'supports result chaining patterns' do
        # First operation
        result1 = ServiceResult.success(data: { step1: 'completed' })
        
        # Chain with second operation (simulate)
        if result1.success?
          result2 = ServiceResult.success(
            data: result1.data.merge({ step2: 'completed' }),
            final: true
          )
          
          expect(result2.success?).to be true
          expect(result2.data).to eq({ step1: 'completed', step2: 'completed' })
          expect(result2.final).to be true
        end
      end

      it 'supports early failure in chains' do
        # First operation fails
        result1 = ServiceResult.failure(errors: ['Step 1 failed'])
        
        # Should short-circuit
        unless result1.success?
          expect(result1.failure?).to be true
          expect(result1.errors).to eq(['Step 1 failed'])
        end
      end
    end
  end

  describe 'edge cases and error handling' do
    it 'handles nil and empty values gracefully' do
      result = ServiceResult.new(
        success: true,
        data: nil,
        errors: nil,
        empty_string: '',
        empty_array: [],
        empty_hash: {}
      )

      expect(result.data).to be_nil
      expect(result.errors).to eq([])
      expect(result.empty_string).to eq('')
      expect(result.empty_array).to eq([])
      expect(result.empty_hash).to eq({})
    end

    it 'handles boolean values correctly' do
      result = ServiceResult.new(
        success: true,
        flag1: true,
        flag2: false,
        flag3: nil
      )

      expect(result.flag1).to be true
      expect(result.flag2).to be false
      expect(result.flag3).to be_nil
    end

    it 'preserves object types' do
      time_obj = Time.current
      result = ServiceResult.new(
        success: true,
        timestamp: time_obj,
        number: 42,
        string: 'test',
        symbol: :test
      )

      expect(result.timestamp).to eq(time_obj)
      expect(result.timestamp).to be_a(Time)
      expect(result.number).to eq(42)
      expect(result.number).to be_a(Integer)
      expect(result.string).to eq('test')
      expect(result.string).to be_a(String)
      expect(result.symbol).to eq(:test)
      expect(result.symbol).to be_a(Symbol)
    end
  end
end