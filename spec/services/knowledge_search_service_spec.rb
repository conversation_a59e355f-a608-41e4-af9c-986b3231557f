require 'rails_helper'

RSpec.describe KnowledgeSearchService do
  let(:category) { create(:knowledge_category, :with_articles, active: true) }
  let!(:article1) do
    create(:knowledge_article, :published,
           knowledge_category: category,
           title: 'How to create links',
           content: 'This guide explains link creation',
           keywords: 'links, create, tutorial')
  end
  let!(:article2) do
    create(:knowledge_article, :published,
           knowledge_category: category,
           title: 'Understanding analytics',
           content: 'Analytics help you track performance',
           keywords: 'analytics, tracking, metrics')
  end
  let!(:featured_article) do
    create(:knowledge_article, :published, :featured,
           knowledge_category: category,
           title: 'Getting started guide')
  end

  describe '#search' do
    context 'with valid query' do
      it 'finds articles by title' do
        service = KnowledgeSearchService.new('create links')
        result = service.search

        expect(result).to be_success
        expect(result.data).to include(article1)
      end

      it 'finds articles by content' do
        service = KnowledgeSearchService.new('analytics help')
        result = service.search

        expect(result).to be_success
        expect(result.data).to include(article2)
      end

      it 'finds articles by keywords' do
        service = KnowledgeSearchService.new('tutorial')
        result = service.search

        expect(result).to be_success
        expect(result.data).to include(article1)
      end

      it 'limits results' do
        service = KnowledgeSearchService.new('guide')
        result = service.search(limit: 1)

        expect(result).to be_success
        expect(result.data.size).to eq(1)
      end

      it 'orders by relevance' do
        # Create article with exact title match
        exact_match = create(:knowledge_article, :published,
                            knowledge_category: category,
                            title: 'create tutorial',
                            keywords: 'create')

        service = KnowledgeSearchService.new('create')
        result = service.search

        expect(result).to be_success
        expect(result.data.first).to eq(exact_match)
      end
    end

    context 'with blank query' do
      it 'returns empty results' do
        service = KnowledgeSearchService.new('')
        result = service.search

        expect(result).to be_success
        expect(result.data).to be_empty
      end
    end

    context 'when error occurs' do
      it 'handles database errors gracefully' do
        service = KnowledgeSearchService.new('test')
        allow(KnowledgeArticle).to receive(:published).and_raise(StandardError.new('DB Error'))

        result = service.search

        expect(result).to be_failure
        expect(result.errors).to include('Unable to search knowledge base')
      end
    end
  end

  describe '#find_by_category' do
    context 'with valid category slug' do
      it 'finds articles in category' do
        service = KnowledgeSearchService.new('')
        result = service.find_by_category(category.slug)

        expect(result).to be_success
        expect(result.data).to include(article1, article2, featured_article)
      end

      it 'limits results' do
        service = KnowledgeSearchService.new('')
        result = service.find_by_category(category.slug, limit: 2)

        expect(result).to be_success
        expect(result.data.size).to eq(2)
      end
    end

    context 'with invalid category slug' do
      it 'returns empty results' do
        service = KnowledgeSearchService.new('')
        result = service.find_by_category('nonexistent')

        expect(result).to be_success
        expect(result.data).to be_empty
      end
    end

    context 'with blank slug' do
      it 'returns empty results' do
        service = KnowledgeSearchService.new('')
        result = service.find_by_category('')

        expect(result).to be_success
        expect(result.data).to be_empty
      end
    end

    context 'when error occurs' do
      it 'handles errors gracefully' do
        service = KnowledgeSearchService.new('')
        allow(KnowledgeCategory).to receive(:active).and_raise(StandardError.new('DB Error'))

        result = service.find_by_category(category.slug)

        expect(result).to be_failure
        expect(result.errors).to include('Unable to find articles in category')
      end
    end
  end

  describe '#find_featured' do
    it 'returns featured articles' do
      service = KnowledgeSearchService.new('')
      result = service.find_featured

      expect(result).to be_success
      expect(result.data).to include(featured_article)
    end

    it 'limits results' do
      create(:knowledge_article, :published, :featured, knowledge_category: category)

      service = KnowledgeSearchService.new('')
      result = service.find_featured(limit: 1)

      expect(result).to be_success
      expect(result.data.size).to eq(1)
    end

    context 'when error occurs' do
      it 'handles errors gracefully' do
        service = KnowledgeSearchService.new('')
        allow(KnowledgeArticle).to receive(:published).and_raise(StandardError.new('DB Error'))

        result = service.find_featured

        expect(result).to be_failure
        expect(result.errors).to include('Unable to find featured articles')
      end
    end
  end

  describe '#find_related' do
    context 'with valid article' do
      it 'finds articles in same category' do
        service = KnowledgeSearchService.new('')
        result = service.find_related(article1)

        expect(result).to be_success
        expect(result.data).to include(article2, featured_article)
        expect(result.data).not_to include(article1)
      end

      it 'finds articles with similar keywords' do
        different_category = create(:knowledge_category, active: true)
        similar_article = create(:knowledge_article, :published,
                                knowledge_category: different_category,
                                keywords: 'links, create')

        service = KnowledgeSearchService.new('')
        result = service.find_related(article1)

        expect(result).to be_success
        expect(result.data).to include(similar_article)
      end

      it 'limits results' do
        service = KnowledgeSearchService.new('')
        result = service.find_related(article1, limit: 1)

        expect(result).to be_success
        expect(result.data.size).to eq(1)
      end
    end

    context 'with invalid article' do
      it 'returns empty results for non-article object' do
        service = KnowledgeSearchService.new('')
        result = service.find_related('not an article')

        expect(result).to be_success
        expect(result.data).to be_empty
      end
    end

    context 'when error occurs' do
      it 'handles errors gracefully' do
        service = KnowledgeSearchService.new('')
        allow(KnowledgeArticle).to receive(:published).and_raise(StandardError.new('DB Error'))

        result = service.find_related(article1)

        expect(result).to be_failure
        expect(result.errors).to include('Unable to find related articles')
      end
    end
  end

  describe '#suggest_topics' do
    it 'returns categories and popular articles' do
      service = KnowledgeSearchService.new('')
      result = service.suggest_topics

      expect(result).to be_success
      expect(result.data[:categories]).to include(category)
      expect(result.data[:popular_articles]).to include(article1, article2, featured_article)
    end

    context 'when error occurs' do
      it 'handles errors gracefully' do
        service = KnowledgeSearchService.new('')
        allow(KnowledgeCategory).to receive(:active).and_raise(StandardError.new('DB Error'))

        result = service.suggest_topics

        expect(result).to be_failure
        expect(result.errors).to include('Unable to suggest topics')
      end
    end
  end

  describe 'private methods' do
    describe '#extract_keywords' do
      let(:service) { KnowledgeSearchService.new('') }

      it 'extracts meaningful keywords' do
        keywords = service.send(:extract_keywords, 'How to create custom links and manage them')

        expect(keywords).to include('create', 'custom', 'links', 'manage')
        expect(keywords).not_to include('how', 'and', 'them') # stop words
      end

      it 'removes short words' do
        keywords = service.send(:extract_keywords, 'a to is it')

        expect(keywords).to be_empty
      end

      it 'limits keyword count' do
        long_text = (1..20).map { |i| "keyword#{i}" }.join(' ')
        keywords = service.send(:extract_keywords, long_text)

        expect(keywords.size).to be <= 10
      end

      it 'handles empty text' do
        keywords = service.send(:extract_keywords, '')

        expect(keywords).to be_empty
      end
    end
  end
end
