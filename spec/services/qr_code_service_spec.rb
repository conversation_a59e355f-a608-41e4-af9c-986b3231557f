require 'rails_helper'

RSpec.describe QrCodeService do
  let(:link) { create(:link, short_code: 'abc123') }
  let(:service) { described_class.new(link) }

  describe '#initialize' do
    it 'sets the link and default options' do
      expect(service.link).to eq(link)
      expect(service.options).to include(
        module_size: 6,
        module_px_size: 10,
        png_size: 400,
        border_modules: 4,
        color: "000000",
        background_color: "ffffff",
        error_correction: :m,
        mode: nil,
        format: :svg
      )
    end

    it 'merges custom options with defaults' do
      custom_service = described_class.new(link, module_size: 10, color: 'fff')
      expect(custom_service.options[:module_size]).to eq(10)
      expect(custom_service.options[:color]).to eq('fff')
      expect(custom_service.options[:png_size]).to eq(400) # default preserved
    end
  end

  describe '#generate_svg' do
    it 'generates an SVG QR code' do
      svg = service.generate_svg
      expect(svg).to be_a(String)
      expect(svg).to include('<svg')
      expect(svg).to include('class="qr-code-svg"')
      expect(svg).to include('viewBox=')
    end

    it 'uses custom color option' do
      custom_service = described_class.new(link, color: 'ff0000')
      svg = custom_service.generate_svg
      expect(svg).to include('fill="#ff0000"')
    end

    it 'uses custom module size option' do
      custom_service = described_class.new(link, module_size: 10)
      svg = custom_service.generate_svg
      expect(svg).to be_a(String)
      expect(svg).to include('<svg')
    end

    it 'includes proper viewBox dimensions' do
      svg = service.generate_svg
      expect(svg).to match(/viewBox="0 0 \d+ \d+"/)
    end
  end

  describe '#generate_png' do
    it 'generates a PNG QR code' do
      png = service.generate_png
      expect(png).to be_a(ChunkyPNG::Image)
      expect(png.width).to eq(400) # default png_size
      expect(png.height).to eq(400)
    end

    it 'uses custom size option' do
      custom_service = described_class.new(link, png_size: 500)
      png = custom_service.generate_png
      expect(png.width).to eq(500)
      expect(png.height).to eq(500)
    end
  end

  describe '#generate_pdf' do
    it 'generates a PDF document' do
      pdf_data = service.generate_pdf
      expect(pdf_data).to be_a(String)
      expect(pdf_data).to start_with('%PDF')
    end

    it 'includes link information in PDF' do
      allow(link).to receive(:short_url).and_return('http://test.host/abc123')
      allow(link).to receive(:original_url).and_return('https://example.com')

      pdf_data = service.generate_pdf
      # PDF content is binary, so we'll just check it's a valid PDF
      expect(pdf_data).to be_a(String)
      expect(pdf_data.length).to be > 1000
    end
  end

  describe '#to_data_url' do
    it 'generates a data URL for the PNG' do
      data_url = service.to_data_url
      expect(data_url).to start_with('data:image/png;base64,')
      expect(data_url.length).to be > 100 # Should have actual data
    end
  end

  describe 'QR code content' do
    it 'encodes the short URL' do
      allow(link).to receive(:short_url).and_return('http://test.host/abc123')

      # Create a new instance to test the QR code content
      qr_service = described_class.new(link)
      qr_code = qr_service.send(:qr_code)

      expect(qr_code).to be_a(RQRCode::QRCode)
      # The QR code should contain the short URL
      expect(qr_code.qrcode.modules.size).to be > 0
    end
  end

  describe 'error correction levels' do
    %i[l m q h].each do |level|
      it "supports error correction level :#{level}" do
        service = described_class.new(link, error_correction: level)
        expect { service.generate_svg }.not_to raise_error
      end
    end
  end

  describe 'caching' do
    it 'caches SVG generation' do
      expect(Rails.cache).to receive(:fetch).with("#{service.cache_key}:svg", expires_in: 1.hour).and_call_original
      service.generate_svg
    end

    it 'caches PNG generation' do
      expect(Rails.cache).to receive(:fetch).with("#{service.cache_key}:png", expires_in: 1.hour).and_call_original
      service.generate_png
    end

    it 'caches PDF generation' do
      expect(Rails.cache).to receive(:fetch).with("#{service.cache_key}:pdf", expires_in: 1.hour).and_call_original
      service.generate_pdf
    end

    it 'clears cache for specific service instance' do
      expect(Rails.cache).to receive(:delete).with("#{service.cache_key}:svg")
      expect(Rails.cache).to receive(:delete).with("#{service.cache_key}:png")
      expect(Rails.cache).to receive(:delete).with("#{service.cache_key}:pdf")
      service.clear_cache
    end

    it 'clears all cache for a link' do
      expect(Rails.cache).to receive(:delete_matched).with("qr_code:#{link.id}:*")
      described_class.clear_all_cache_for_link(link)
    end
  end
end
