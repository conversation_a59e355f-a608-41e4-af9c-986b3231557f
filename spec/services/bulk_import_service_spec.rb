require 'rails_helper'

RSpec.describe BulkImportService, type: :service do
  let(:user) { create(:user) }
  let(:team) { create(:team) }

  describe '#import' do
    context 'with valid CSV file' do
      let(:csv_content) do
        <<~CSV
          url,title,tags,expires_at
          https://example.com,Example Site,example,2024-12-31
          https://github.com,GitHub,development,
          https://google.com,Google,search,2025-01-01
        CSV
      end

      let(:csv_file) do
        file = Tempfile.new([ 'test', '.csv' ])
        file.write(csv_content)
        file.rewind

        # Mock uploaded file
        uploaded_file = double('uploaded_file')
        allow(uploaded_file).to receive(:path).and_return(file.path)
        allow(uploaded_file).to receive(:size).and_return(csv_content.bytesize)
        allow(uploaded_file).to receive(:content_type).and_return('text/csv')
        allow(uploaded_file).to receive(:original_filename).and_return('test.csv')
        allow(uploaded_file).to receive(:present?).and_return(true)
        allow(uploaded_file).to receive(:respond_to?).with(:content_type).and_return(true)

        uploaded_file
      ensure
        file&.close
        file&.unlink
      end

      let(:service) { described_class.new(user, csv_file) }

      it 'creates a bulk import record' do
        expect { service.import }.to change(BulkImport, :count).by(1)
      end

      it 'returns success result' do
        result = service.import
        expect(result.success?).to be true
        expect(result.bulk_import).to be_a(BulkImport)
      end

      it 'sets correct file details' do
        result = service.import
        bulk_import = result.bulk_import

        expect(bulk_import.file_name).to eq('test.csv')
        expect(bulk_import.total_rows).to eq(3)
        expect(bulk_import.user).to eq(user)
      end

      context 'with team' do
        let(:service) { described_class.new(user, csv_file, team: team) }

        it 'associates with team' do
          result = service.import
          expect(result.bulk_import.team).to eq(team)
        end
      end
    end

    context 'with invalid file' do
      context 'when no file provided' do
        let(:service) { described_class.new(user, nil) }

        it 'returns failure result' do
          result = service.import
          expect(result.success?).to be false
          expect(result.error).to eq('No file provided')
        end
      end

      context 'when file too large' do
        let(:large_file) do
          file = double('large_file')
          allow(file).to receive(:present?).and_return(true)
          allow(file).to receive(:size).and_return(15.megabytes)
          file
        end

        let(:service) { described_class.new(user, large_file) }

        it 'returns failure result' do
          result = service.import
          expect(result.success?).to be false
          expect(result.error).to eq('File too large (max 10MB)')
        end
      end

      context 'when invalid file type' do
        let(:txt_file) do
          file = double('txt_file')
          allow(file).to receive(:present?).and_return(true)
          allow(file).to receive(:size).and_return(1000)
          allow(file).to receive(:content_type).and_return('text/plain')
          allow(file).to receive(:original_filename).and_return('test.txt')
          allow(file).to receive(:respond_to?).with(:content_type).and_return(true)
          file
        end

        let(:service) { described_class.new(user, txt_file) }

        it 'returns failure result' do
          result = service.import
          expect(result.success?).to be false
          expect(result.error).to eq('Invalid file type. Please upload a CSV file')
        end
      end

      context 'when CSV has no URL column' do
        let(:invalid_csv_content) do
          <<~CSV
            title,description
            Example Site,A test site
            GitHub,Code repository
          CSV
        end

        let(:invalid_csv_file) do
          file = Tempfile.new([ 'invalid', '.csv' ])
          file.write(invalid_csv_content)
          file.rewind

          uploaded_file = double('uploaded_file')
          allow(uploaded_file).to receive(:path).and_return(file.path)
          allow(uploaded_file).to receive(:size).and_return(invalid_csv_content.bytesize)
          allow(uploaded_file).to receive(:content_type).and_return('text/csv')
          allow(uploaded_file).to receive(:original_filename).and_return('invalid.csv')
          allow(uploaded_file).to receive(:present?).and_return(true)
          allow(uploaded_file).to receive(:respond_to?).with(:content_type).and_return(true)

          uploaded_file
        ensure
          file&.close
          file&.unlink
        end

        let(:service) { described_class.new(user, invalid_csv_file) }

        it 'returns failure result' do
          result = service.import
          expect(result.success?).to be false
          expect(result.error).to eq('CSV must contain a URL column (url, original_url, long_url, or destination)')
        end
      end
    end
  end

  describe '#process_import' do
    let(:csv_content) do
      <<~CSV
        url,title,tags
        https://example.com,Example Site,example
        invalid-url,Bad URL,test
        https://github.com,GitHub,development
      CSV
    end

    let(:csv_file) do
      file = Tempfile.new([ 'process_test', '.csv' ])
      file.write(csv_content)
      file.rewind
      file
    end

    let(:bulk_import) { create(:bulk_import, user: user, total_rows: 3) }
    let(:service) { described_class.new(user, nil) }

    before do
      service.instance_variable_set(:@bulk_import, bulk_import)
      service.instance_variable_set(:@file, double(path: csv_file.path))

      # Mock LinkShorteningService
      allow_any_instance_of(LinkShorteningService).to receive(:create_link).and_return(
        double(success?: true)
      )
    end

    after do
      csv_file.close
      csv_file.unlink
    end

    it 'processes all rows' do
      service.process_import

      bulk_import.reload
      expect(bulk_import.processed_rows).to eq(3)
      expect(bulk_import.status).to eq('completed')
    end

    it 'marks import as processing first' do
      expect(bulk_import).to receive(:mark_as_processing!).once
      service.process_import
    end

    it 'marks import as completed when done' do
      expect(bulk_import).to receive(:mark_as_completed!).once
      service.process_import
    end

    context 'when link creation fails' do
      before do
        allow_any_instance_of(LinkShorteningService).to receive(:create_link).and_return(
          double(success?: false, errors: { original_url: [ 'Invalid URL format' ] })
        )
      end

      it 'records errors' do
        expect(bulk_import).to receive(:add_error).at_least(:once)
        service.process_import
      end

      it 'updates failed rows count' do
        service.process_import

        bulk_import.reload
        expect(bulk_import.failed_rows).to be > 0
      end
    end
  end
end
