require 'rails_helper'

RSpec.describe CustomDomainService do
  let(:user) { create(:user, subscription_plan: 'professional') }
  let(:service) { described_class.new(user) }

  describe '#add_domain' do
    context 'with valid domain' do
      it 'creates a custom domain' do
        result = service.add_domain('example.com')

        expect(result.success?).to be true
        expect(result.custom_domain).to be_persisted
        expect(result.custom_domain.domain).to eq('example.com')
        expect(result.custom_domain.verified).to be false
        expect(result.custom_domain.verification_token).to be_present
        expect(result.verification_instructions).to be_present
      end

      it 'cleans the domain input' do
        result = service.add_domain('https://EXAMPLE.COM/')

        expect(result.success?).to be true
        expect(result.custom_domain.domain).to eq('example.com')
      end
    end

    context 'with duplicate domain' do
      before { create(:custom_domain, domain: 'example.com') }

      it 'returns an error' do
        result = service.add_domain('example.com')

        expect(result.success?).to be false
        expect(result.errors[:domain]).to include('has already been taken')
      end
    end

    context 'with domain limit reached' do
      before do
        # Professional plan has limit of 3
        3.times { |i| create(:custom_domain, user: user, domain: "example#{i}.com") }
      end

      it 'returns an error' do
        result = service.add_domain('example4.com')

        expect(result.success?).to be false
        expect(result.errors[:base]).to include('You have reached your custom domain limit for your subscription plan')
      end
    end

    context 'with free plan' do
      let(:user) { create(:user, subscription_plan: 'free') }

      it 'returns an error' do
        result = service.add_domain('example.com')

        expect(result.success?).to be false
        expect(result.errors[:base]).to include('You have reached your custom domain limit for your subscription plan')
      end
    end
  end

  describe '#verify_domain' do
    let(:custom_domain) { create(:custom_domain, user: user, verified: false) }

    context 'in development' do
      before { allow(Rails.env).to receive(:development?).and_return(true) }

      it 'simulates verification' do
        # May succeed or fail randomly in development
        result = service.verify_domain(custom_domain)

        if result.success?
          expect(custom_domain.reload.verified?).to be true
          expect(custom_domain.verified_at).to be_present
        else
          expect(result.errors[:base]).to include('DNS records not found or invalid')
          expect(result.dns_status).to be_present
        end
      end
    end
  end

  describe '#remove_domain' do
    let(:custom_domain) { create(:custom_domain, user: user) }

    it 'deletes the custom domain' do
      result = service.remove_domain(custom_domain)

      expect(result.success?).to be true
      expect(result.message).to eq('Domain removed successfully')
      expect(CustomDomain.exists?(custom_domain.id)).to be false
    end
  end

  describe '#set_primary_domain' do
    let!(:domain1) { create(:custom_domain, user: user, verified: true, is_primary: true) }
    let!(:domain2) { create(:custom_domain, user: user, verified: true, is_primary: false) }

    context 'with verified domain' do
      it 'sets the domain as primary' do
        result = service.set_primary_domain(domain2)

        expect(result.success?).to be true
        expect(domain2.reload.is_primary?).to be true
        expect(domain1.reload.is_primary?).to be false
      end
    end

    context 'with unverified domain' do
      let(:unverified_domain) { create(:custom_domain, user: user, verified: false) }

      it 'returns an error' do
        result = service.set_primary_domain(unverified_domain)

        expect(result.success?).to be false
        expect(result.errors[:base]).to include('Domain must be verified before setting as primary')
      end
    end
  end
end
