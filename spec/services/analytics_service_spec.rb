require 'rails_helper'

RSpec.describe AnalyticsService, type: :service do
  let(:user) { create(:user) }
  let(:date_range) { 7.days.ago..Time.current }
  let(:service) { described_class.new(user: user, date_range: date_range) }

  describe '#dashboard_analytics' do
    context 'with no data' do
      it 'returns zero values for all metrics' do
        result = service.dashboard_analytics

        expect(result.success?).to be true
        expect(result.data[:total_links]).to eq(0)
        expect(result.data[:active_links]).to eq(0)
        expect(result.data[:total_clicks]).to eq(0)
        expect(result.data[:unique_visitors]).to eq(0)
      end
    end

    context 'with analytics data' do
      let!(:team) { create(:team) }
      let!(:link1) { create(:link, user: user, team: team, created_at: 3.days.ago) }
      let!(:link2) { create(:link, user: user, is_archived: true, created_at: 5.days.ago) }
      let!(:link3) { create(:link, user: user, created_at: 2.days.ago) }

      let!(:click1) { create(:link_click, link: link1, clicked_at: 2.days.ago, tracking_data: { ip_address: '***********', device_type: 'mobile', country_code: 'US', browser: 'Chrome', os: 'iOS', city: 'New York' }) }
      let!(:click2) { create(:link_click, link: link1, clicked_at: 1.day.ago, tracking_data: { ip_address: '***********', device_type: 'desktop', country_code: 'US', browser: 'Firefox', os: 'Windows', city: 'Los Angeles' }) }
      let!(:click3) { create(:link_click, link: link3, clicked_at: 1.day.ago, tracking_data: { ip_address: '***********', device_type: 'mobile', country_code: 'CA', browser: 'Safari', os: 'macOS', city: 'Toronto' }) }

      it 'calculates dashboard metrics correctly' do
        result = service.dashboard_analytics

        expect(result.success?).to be true

        data = result.data

        expect(data[:total_links]).to eq(3)
        expect(data[:active_links]).to eq(2) # link2 is archived
        expect(data[:total_clicks]).to eq(3)
        expect(data[:unique_visitors]).to eq(2) # Two unique IP addresses

        expect(data[:clicks_by_device]).to include('mobile' => 2, 'desktop' => 1)
        expect(data[:clicks_by_country]).to include('US' => 2, 'CA' => 1)
        expect(data[:clicks_by_browser]).to include('Chrome' => 1, 'Firefox' => 1, 'Safari' => 1)
        expect(data[:clicks_by_os]).to include('iOS' => 1, 'Windows' => 1, 'macOS' => 1)
        expect(data[:clicks_by_city]).to include('New York' => 1, 'Los Angeles' => 1, 'Toronto' => 1)

        expect(data[:new_links_count]).to eq(3) # All links created within date range
        expect(data[:return_visitor_rate]).to eq(50.0) # 1 returning visitor out of 2 unique
        expect(data[:avg_clicks_per_visitor]).to eq(1.5) # 3 clicks / 2 unique visitors
      end

      it 'includes top performing links' do
        result = service.dashboard_analytics

        expect(result.success?).to be true
        expect(result.data[:top_links]).to be_an(Array)
        expect(result.data[:top_links].length).to eq(2) # Only links with clicks in period
      end

      it 'includes time-based analytics' do
        result = service.dashboard_analytics

        expect(result.success?).to be true

        data = result.data
        expect(data[:clicks_by_date]).to be_a(Hash)
        expect(data[:clicks_by_hour]).to be_a(Hash)
        expect(data[:clicks_by_day_of_week]).to be_a(Hash)
        expect(data[:clicks_by_day_of_week].keys).to match_array([ 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday' ])
      end
    end

    context 'when service encounters an error' do
      before do
        allow(service).to receive(:calculate_base_stats).and_raise(StandardError, 'Database error')
      end

      it 'returns failure result with error message' do
        result = service.dashboard_analytics

        expect(result.success?).to be false
        expect(result.errors).to include('Database error')
        expect(result.data).to eq({})
      end
    end
  end

  describe '#link_analytics' do
    let!(:link) { create(:link, user: user, created_at: 10.days.ago) }

    context 'with no clicks' do
      it 'returns zero values for link metrics' do
        result = service.link_analytics(link)

        expect(result.success?).to be true
        expect(result.data[:total_clicks]).to eq(0)
        expect(result.data[:unique_visitors]).to eq(0)
        expect(result.data[:recent_clicks]).to be_empty
      end
    end

    context 'with link clicks' do
      let!(:click1) { create(:link_click, link: link, clicked_at: 2.days.ago,
                            tracking_data: { ip_address: '***********', device_type: 'mobile', country_code: 'US', browser: 'Chrome', os: 'iOS' },
                            attribution_data: { referrer: 'https://google.com', utm_campaign: 'summer_sale', utm_source: 'google' }) }
      let!(:click2) { create(:link_click, link: link, clicked_at: 1.day.ago,
                            tracking_data: { ip_address: '***********', device_type: 'desktop', country_code: 'CA', browser: 'Firefox', os: 'Windows' },
                            attribution_data: { referrer: 'https://facebook.com', utm_campaign: 'winter_promo', utm_source: 'facebook' }) }

      it 'calculates link metrics correctly' do
        result = service.link_analytics(link)

        expect(result.success?).to be true

        data = result.data
        expect(data[:total_clicks]).to eq(2)
        expect(data[:unique_visitors]).to eq(2)

        expect(data[:clicks_by_device]).to include('mobile' => 1, 'desktop' => 1)
        expect(data[:clicks_by_country]).to include('US' => 1, 'CA' => 1)
        expect(data[:clicks_by_browser]).to include('Chrome' => 1, 'Firefox' => 1)
        expect(data[:clicks_by_os]).to include('iOS' => 1, 'Windows' => 1)

        expect(data[:top_referrers]).to include('https://google.com' => 1, 'https://facebook.com' => 1)
        expect(data[:utm_campaigns]).to include('summer_sale' => 1, 'winter_promo' => 1)
        expect(data[:utm_sources]).to include('google' => 1, 'facebook' => 1)

        expect(data[:daily_average_clicks]).to be > 0
        expect(data[:peak_hour]).to be_a(String)
      end

      it 'includes recent clicks' do
        result = service.link_analytics(link)

        expect(result.success?).to be true
        expect(result.data[:recent_clicks]).to be_an(ActiveRecord::Relation)
        expect(result.data[:recent_clicks].count).to eq(2)
      end
    end
  end

  describe '#export_data' do
    context 'with links and clicks' do
      let!(:link1) { create(:link, user: user, short_code: 'abc123', original_url: 'https://example.com/1') }
      let!(:link2) { create(:link, user: user, short_code: 'def456', original_url: 'https://example.com/2') }

      let!(:click1) { create(:link_click, link: link1, clicked_at: 2.days.ago,
                            tracking_data: { ip_address: '***********', country_code: 'US' },
                            attribution_data: { referrer: 'https://google.com' }) }
      let!(:click2) { create(:link_click, link: link2, clicked_at: 1.day.ago,
                            tracking_data: { ip_address: '***********', country_code: 'CA' },
                            attribution_data: { referrer: 'https://facebook.com' }) }

      it 'returns structured export data' do
        result = service.export_data

        expect(result.success?).to be true
        expect(result.data).to be_an(Array)
        expect(result.data.length).to eq(2)

        first_link_data = result.data.first
        expect(first_link_data).to include(
          :link,
          :original_url,
          :total_clicks,
          :clicks_in_period,
          :unique_visitors,
          :unique_in_period,
          :created_at,
          :last_clicked,
          :top_country,
          :top_referrer
        )

        expect(first_link_data[:original_url]).to be_in([ 'https://example.com/1', 'https://example.com/2' ])
      end
    end

    context 'with no data' do
      it 'returns empty array' do
        result = service.export_data

        expect(result.success?).to be true
        expect(result.data).to eq([])
      end
    end
  end

  describe 'error handling' do
    context 'when database is unavailable' do
      before do
        allow(user).to receive(:links).and_raise(ActiveRecord::ConnectionNotEstablished)
      end

      it 'handles database errors gracefully' do
        result = service.dashboard_analytics

        expect(result.success?).to be false
        expect(result.errors).not_to be_empty
        expect(result.data).to eq({})
      end
    end
  end

  describe 'performance optimization' do
    let!(:links) { create_list(:link, 10, user: user) }
    let!(:clicks) do
      links.flat_map do |link|
        create_list(:link_click, 5, link: link, clicked_at: 2.days.ago,
                   tracking_data: { ip_address: '***********', device_type: 'mobile', country_code: 'US' })
      end
    end

    it 'minimizes database queries' do
      # Remove query limit test for now since it requires additional gems
      result = service.dashboard_analytics
      expect(result.success?).to be true
    end
  end
end
