# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AttributionTrackingService, 'Privacy and GDPR Compliance' do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user) }
  let(:request) { double('request') }
  let(:service) { described_class.new(link: link, request: request) }

  before do
    allow(request).to receive(:remote_ip).and_return('*************')
    allow(request).to receive(:user_agent).and_return('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)')
    allow(request).to receive(:referrer).and_return('https://example.com')
    allow(request).to receive(:headers).and_return({})

    # Mock GeolocationService to avoid external dependencies
    allow(GeolocationService).to receive(:lookup).and_return({
      country_code: 'US',
      city: 'San Francisco',
      region: 'California'
    })
  end

  describe 'IP Anonymization' do
    context 'IPv4 addresses' do
      it 'anonymizes IPv4 addresses by masking last octet' do
        allow(request).to receive(:remote_ip).and_return('*************')

        result = service.track_click
        click = result.click

        expect(click.anonymized_ip).to eq('***********')
        expect(click.anonymized_ip).not_to eq('*************')
      end

      it 'handles different IPv4 ranges correctly' do
        test_cases = {
          '********' => '10.0.0.0',
          '************' => '************',
          '*******' => '*******',
          '*******' => '*******'
        }

        test_cases.each do |original, expected|
          allow(request).to receive(:remote_ip).and_return(original)

          result = service.track_click
          click = result.click

          expect(click.anonymized_ip).to eq(expected),
            "Expected #{original} to be anonymized to #{expected}, got #{click.anonymized_ip}"
        end
      end
    end

    context 'IPv6 addresses' do
      it 'anonymizes IPv6 addresses by keeping only first 48 bits' do
        allow(request).to receive(:remote_ip).and_return('2001:0db8:85a3:0000:0000:8a2e:0370:7334')

        result = service.track_click
        click = result.click

        # Should keep only the first 48 bits (first 3 groups)
        expect(click.anonymized_ip).to eq('2001:db8:85a3::')
      end

      it 'handles compressed IPv6 addresses' do
        allow(request).to receive(:remote_ip).and_return('2001:db8::1')

        result = service.track_click
        click = result.click

        expect(click.anonymized_ip).to eq('2001:db8::')
      end
    end

    context 'invalid IP addresses' do
      it 'handles invalid IP addresses gracefully' do
        allow(request).to receive(:remote_ip).and_return('invalid-ip')
        allow(Rails.logger).to receive(:warn)

        result = service.track_click
        click = result.click

        expect(click.anonymized_ip).to be_nil
        expect(Rails.logger).to have_received(:warn).with(/Invalid IP address/)
      end

      it 'handles blank IP addresses' do
        allow(request).to receive(:remote_ip).and_return('')

        result = service.track_click
        click = result.click

        expect(click.anonymized_ip).to be_nil
      end
    end
  end

  describe 'Do Not Track (DNT) Compliance' do
    context 'when DNT header is present' do
      before do
        allow(request).to receive(:headers).and_return({ 'DNT' => '1' })
      end

      it 'respects DNT and does not collect tracking data' do
        result = service.track_click
        click = result.click

        expect(click.dnt_enabled).to be true
        expect(click.tracking_data).to be_empty
        expect(click.anonymized_ip).to be_nil
      end

      it 'still allows basic attribution data' do
        utm_params = { utm_source: 'newsletter', utm_campaign: 'spring2024' }

        result = service.track_click(utm_params)
        click = result.click

        expect(click.dnt_enabled).to be true
        expect(click.attribution_data['utm_source']).to eq('newsletter')
        expect(click.attribution_data['utm_campaign']).to eq('spring2024')
      end
    end

    context 'when DNT header is not present' do
      it 'collects normal tracking data' do
        result = service.track_click
        click = result.click

        expect(click.dnt_enabled).to be false
        expect(click.tracking_data).not_to be_empty
        expect(click.anonymized_ip).to be_present
      end
    end
  end

  describe 'Data Retention' do
    it 'sets retention expiry date for new clicks' do
      # Mock the retention period
      allow(Rails.application.config).to receive(:data_retention_period).and_return(6.months)

      travel_to Time.parse('2024-01-01 12:00:00 UTC') do
        result = service.track_click
        click = result.click

        expected_expiry = Time.parse('2024-07-01 12:00:00 UTC')
        expect(click.retention_expires_at).to be_within(1.second).of(expected_expiry)
      end
    end

    it 'uses default retention period when not configured' do
      travel_to Time.parse('2024-01-01 12:00:00 UTC') do
        result = service.track_click
        click = result.click

        # Default is 13 months
        expected_expiry = Time.parse('2025-02-01 12:00:00 UTC')
        expect(click.retention_expires_at).to be_within(1.second).of(expected_expiry)
      end
    end
  end

  describe 'Geolocation Privacy' do
    it 'uses original IP for geolocation but stores anonymized IP' do
      original_ip = '*******'
      allow(request).to receive(:remote_ip).and_return(original_ip)

      # Geolocation service should receive the original IP
      expect(GeolocationService).to receive(:lookup).with(original_ip).and_return({
        country_code: 'US',
        city: 'Mountain View'
      })

      result = service.track_click
      click = result.click

      # But the stored IP should be anonymized
      expect(click.anonymized_ip).to eq('*******')
      expect(click.tracking_data['country_code']).to eq('US')
      expect(click.tracking_data['city']).to eq('Mountain View')
    end

    it 'handles geolocation service failures gracefully' do
      allow(GeolocationService).to receive(:lookup).and_raise(StandardError.new('API error'))
      allow(Rails.logger).to receive(:debug)

      result = service.track_click
      click = result.click

      expect(result.success?).to be true
      expect(click.tracking_data['country_code']).to be_nil
      expect(Rails.logger).to have_received(:debug).with(/Geolocation lookup failed/)
    end
  end

  describe 'User Agent Handling' do
    it 'parses user agent data correctly' do
      user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)'
      allow(request).to receive(:user_agent).and_return(user_agent)

      result = service.track_click
      click = result.click

      expect(click.tracking_data['device_type']).to eq('mobile')
      expect(click.tracking_data['os']).to eq('iOS')
      expect(click.tracking_data['browser']).to be_present
    end

    it 'handles missing user agent gracefully' do
      allow(request).to receive(:user_agent).and_return(nil)

      result = service.track_click
      click = result.click

      expect(result.success?).to be true
      expect(click.tracking_data['device_type']).to be_nil
      expect(click.tracking_data['browser']).to be_nil
    end

    it 'detects bots correctly' do
      bot_user_agents = [
        'Googlebot/2.1',
        'Mozilla/5.0 (compatible; bingbot/2.0)',
        'facebookexternalhit/1.1'
      ]

      bot_user_agents.each do |bot_ua|
        allow(request).to receive(:user_agent).and_return(bot_ua)

        result = service.track_click
        click = result.click

        expect(click.tracking_data['bot']).to be true,
          "Expected #{bot_ua} to be detected as bot"
      end
    end
  end

  describe 'Privacy Helper Methods in LinkClick' do
    let(:link_click) do
      create(:link_click,
        link: link,
        dnt_enabled: false,
        retention_expires_at: 30.days.from_now,
        tracking_data: {
          'ip_address' => '***********',
          'country_code' => 'US'
        }
      )
    end

    it 'provides privacy safety check' do
      expect(link_click.privacy_safe?).to be true

      # DNT enabled should not be privacy safe for analytics
      link_click.update!(dnt_enabled: true)
      expect(link_click.privacy_safe?).to be false

      # Expired retention should not be privacy safe
      link_click.update!(dnt_enabled: false, retention_expires_at: 1.day.ago)
      expect(link_click.privacy_safe?).to be false
    end

    it 'calculates days until expiry correctly' do
      travel_to Time.parse('2024-01-01') do
        link_click.update!(retention_expires_at: Time.parse('2024-01-31'))
        expect(link_click.days_until_expiry).to eq(30)
      end
    end

    it 'detects retention expiry' do
      link_click.update!(retention_expires_at: 1.day.ago)
      expect(link_click.retention_expired?).to be true

      link_click.update!(retention_expires_at: 1.day.from_now)
      expect(link_click.retention_expired?).to be false
    end
  end

  describe 'Scopes for Privacy Compliance' do
    let!(:normal_click) do
      create(:link_click,
        link: link,
        dnt_enabled: false,
        retention_expires_at: 30.days.from_now,
        tracking_data: { 'ip_address' => '***********' }
      )
    end

    let!(:dnt_click) do
      create(:link_click,
        link: link,
        dnt_enabled: true,
        retention_expires_at: 30.days.from_now,
        tracking_data: {}
      )
    end

    let!(:expired_click) do
      create(:link_click,
        link: link,
        dnt_enabled: false,
        retention_expires_at: 1.day.ago,
        tracking_data: { 'ip_address' => '10.0.0.0' }
      )
    end

    it 'filters privacy compliant clicks' do
      privacy_compliant = LinkClick.privacy_compliant
      expect(privacy_compliant).to include(normal_click, expired_click)
      expect(privacy_compliant).not_to include(dnt_click)
    end

    it 'finds expired clicks for retention cleanup' do
      expired = LinkClick.expired_for_retention
      expect(expired).to include(expired_click)
      expect(expired).not_to include(normal_click, dnt_click)
    end

    it 'finds clicks with tracking data' do
      with_data = LinkClick.with_tracking_data
      expect(with_data).to include(normal_click, expired_click)
      expect(with_data).not_to include(dnt_click)
    end
  end
end
