require 'rails_helper'

RSpec.describe BlogHelper, type: :helper do
  describe '#media_type_badge_classes' do
    it 'returns red classes for video' do
      result = helper.media_type_badge_classes("video")
      expect(result).to eq("bg-red-100 text-red-800")
    end

    it 'returns green classes for podcast' do
      result = helper.media_type_badge_classes("podcast")
      expect(result).to eq("bg-green-100 text-green-800")
    end

    it 'returns orange classes for pdf' do
      result = helper.media_type_badge_classes("pdf")
      expect(result).to eq("bg-orange-100 text-orange-800")
    end

    it 'returns blue classes for infographic' do
      result = helper.media_type_badge_classes("infographic")
      expect(result).to eq("bg-blue-100 text-blue-800")
    end

    it 'returns blue classes for image' do
      result = helper.media_type_badge_classes("image")
      expect(result).to eq("bg-blue-100 text-blue-800")
    end

    it 'returns indigo classes for interactive' do
      result = helper.media_type_badge_classes("interactive")
      expect(result).to eq("bg-indigo-100 text-indigo-800")
    end

    it 'returns gray classes for unknown media type' do
      result = helper.media_type_badge_classes("unknown")
      expect(result).to eq("bg-gray-100 text-gray-800")
    end

    it 'returns gray classes for nil media type' do
      result = helper.media_type_badge_classes(nil)
      expect(result).to eq("bg-gray-100 text-gray-800")
    end

    it 'returns gray classes for empty string' do
      result = helper.media_type_badge_classes("")
      expect(result).to eq("bg-gray-100 text-gray-800")
    end

    it 'handles case sensitivity' do
      expect(helper.media_type_badge_classes("VIDEO")).to eq("bg-gray-100 text-gray-800")
      expect(helper.media_type_badge_classes("Video")).to eq("bg-gray-100 text-gray-800")
      expect(helper.media_type_badge_classes("video")).to eq("bg-red-100 text-red-800")
    end
  end

  describe '#media_type_icon' do
    it 'returns play icon for video' do
      result = helper.media_type_icon("video")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('fill="currentColor"')
      expect(result).to include('d="M8 5v14l11-7z"')
      expect(result).to be_html_safe
    end

    it 'returns podcast icon for podcast' do
      result = helper.media_type_icon("podcast")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('d="M12 3a9 9 0 000 18 9 9 0 000-18zm-2 8l6-3-6-3v6z"')
      expect(result).to be_html_safe
    end

    it 'returns document icon for pdf' do
      result = helper.media_type_icon("pdf")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"')
      expect(result).to be_html_safe
    end

    it 'returns image icon for infographic' do
      result = helper.media_type_icon("infographic")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"')
      expect(result).to be_html_safe
    end

    it 'returns image icon for image' do
      result = helper.media_type_icon("image")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"')
      expect(result).to be_html_safe
    end

    it 'returns checkmark icon for interactive' do
      result = helper.media_type_icon("interactive")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"')
      expect(result).to be_html_safe
    end

    it 'returns document icon for unknown media type' do
      result = helper.media_type_icon("unknown")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to include('d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"')
      expect(result).to be_html_safe
    end

    it 'returns document icon for nil media type' do
      result = helper.media_type_icon(nil)
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to be_html_safe
    end

    it 'returns document icon for empty string' do
      result = helper.media_type_icon("")
      
      expect(result).to include('<svg')
      expect(result).to include('class="w-3 h-3 mr-1"')
      expect(result).to be_html_safe
    end

    it 'generates valid SVG markup' do
      result = helper.media_type_icon("video")
      
      expect(result).to match(/<svg[^>]*>.*<path[^>]*><\/path>.*<\/svg>/m)
      expect(result).to include('viewBox="0 0 24 24"')
    end

    it 'handles case sensitivity' do
      video_lower = helper.media_type_icon("video")
      video_upper = helper.media_type_icon("VIDEO")
      video_mixed = helper.media_type_icon("Video")
      
      # Only lowercase should match
      expect(video_lower).to include('d="M8 5v14l11-7z"')
      expect(video_upper).not_to include('d="M8 5v14l11-7z"')
      expect(video_mixed).not_to include('d="M8 5v14l11-7z"')
    end
  end

  describe '#media_type_label' do
    it 'returns "Video" for video' do
      result = helper.media_type_label("video")
      expect(result).to eq("Video")
    end

    it 'returns "Podcast" for podcast' do
      result = helper.media_type_label("podcast")
      expect(result).to eq("Podcast")
    end

    it 'returns "PDF Report" for pdf' do
      result = helper.media_type_label("pdf")
      expect(result).to eq("PDF Report")
    end

    it 'returns "Infographic" for infographic' do
      result = helper.media_type_label("infographic")
      expect(result).to eq("Infographic")
    end

    it 'returns "Image" for image' do
      result = helper.media_type_label("image")
      expect(result).to eq("Image")
    end

    it 'returns "Interactive" for interactive' do
      result = helper.media_type_label("interactive")
      expect(result).to eq("Interactive")
    end

    it 'returns "Article" for unknown media type' do
      result = helper.media_type_label("unknown")
      expect(result).to eq("Article")
    end

    it 'returns "Article" for nil media type' do
      result = helper.media_type_label(nil)
      expect(result).to eq("Article")
    end

    it 'returns "Article" for empty string' do
      result = helper.media_type_label("")
      expect(result).to eq("Article")
    end

    it 'handles case sensitivity' do
      expect(helper.media_type_label("video")).to eq("Video")
      expect(helper.media_type_label("VIDEO")).to eq("Article")
      expect(helper.media_type_label("Video")).to eq("Article")
    end
  end

  describe 'edge cases and integration' do
    it 'handles all supported media types consistently' do
      media_types = ["video", "podcast", "pdf", "infographic", "image", "interactive"]
      
      media_types.each do |type|
        badge_classes = helper.media_type_badge_classes(type)
        icon = helper.media_type_icon(type)
        label = helper.media_type_label(type)
        
        # All should return valid strings
        expect(badge_classes).to be_a(String)
        expect(badge_classes).to match(/^bg-\w+-\d+ text-\w+-\d+$/)
        
        expect(icon).to be_html_safe
        expect(icon).to include('<svg')
        
        expect(label).to be_a(String)
        expect(label).to be_present
      end
    end

    it 'handles special characters in media type' do
      special_types = ["video@hd", "podcast-episode", "pdf_document", "image/jpeg"]
      
      special_types.each do |type|
        # Should fallback to default/unknown behavior
        expect(helper.media_type_badge_classes(type)).to eq("bg-gray-100 text-gray-800")
        expect(helper.media_type_icon(type)).to include('<svg')
        expect(helper.media_type_label(type)).to eq("Article")
      end
    end

    it 'generates consistent output for same input' do
      # Call methods multiple times with same input
      type = "video"
      
      badge1 = helper.media_type_badge_classes(type)
      badge2 = helper.media_type_badge_classes(type)
      
      icon1 = helper.media_type_icon(type)
      icon2 = helper.media_type_icon(type)
      
      label1 = helper.media_type_label(type)
      label2 = helper.media_type_label(type)
      
      expect(badge1).to eq(badge2)
      expect(icon1).to eq(icon2)
      expect(label1).to eq(label2)
    end

    it 'supports blog post display workflow' do
      media_type = "podcast"
      
      # Simulate blog post display
      badge_classes = helper.media_type_badge_classes(media_type)
      icon = helper.media_type_icon(media_type)
      label = helper.media_type_label(media_type)
      
      # Should be suitable for HTML rendering
      expect(badge_classes).to eq("bg-green-100 text-green-800")
      expect(icon).to be_html_safe
      expect(icon).to include('class="w-3 h-3 mr-1"')
      expect(label).to eq("Podcast")
    end

    it 'handles concurrent method calls' do
      types = ["video", "podcast", "pdf", "image"]
      
      results = types.map do |type|
        Thread.new do
          {
            type: type,
            badge: helper.media_type_badge_classes(type),
            icon: helper.media_type_icon(type),
            label: helper.media_type_label(type)
          }
        end
      end.map(&:value)
      
      # Verify all results are correct
      video_result = results.find { |r| r[:type] == "video" }
      expect(video_result[:badge]).to eq("bg-red-100 text-red-800")
      expect(video_result[:label]).to eq("Video")
      
      podcast_result = results.find { |r| r[:type] == "podcast" }
      expect(podcast_result[:badge]).to eq("bg-green-100 text-green-800")
      expect(podcast_result[:label]).to eq("Podcast")
    end

    it 'generates valid TailwindCSS classes' do
      # Test that all badge classes follow TailwindCSS patterns
      media_types = ["video", "podcast", "pdf", "infographic", "image", "interactive", "unknown"]
      
      media_types.each do |type|
        classes = helper.media_type_badge_classes(type)
        
        # Should match pattern: bg-{color}-{shade} text-{color}-{shade}
        expect(classes).to match(/^bg-\w+-\d{3} text-\w+-\d{3}$/)
        
        # Should use consistent color shades (100 for background, 800 for text)
        expect(classes).to include("-100 ")
        expect(classes).to include("-800")
      end
    end

    it 'provides appropriate semantic SVG attributes' do
      icon = helper.media_type_icon("video")
      
      expect(icon).to include('fill="currentColor"')
      expect(icon).to include('viewBox="0 0 24 24"')
      expect(icon).to include('class="w-3 h-3 mr-1"')
    end

    it 'handles whitespace in media types' do
      expect(helper.media_type_badge_classes(" video ")).to eq("bg-gray-100 text-gray-800")
      expect(helper.media_type_label(" video ")).to eq("Article")
    end
  end

  describe 'accessibility and usability' do
    it 'provides consistent visual hierarchy through colors' do
      # Video (red) - most attention-grabbing
      video_classes = helper.media_type_badge_classes("video")
      expect(video_classes).to include("bg-red-100 text-red-800")
      
      # Podcast (green) - friendly and approachable
      podcast_classes = helper.media_type_badge_classes("podcast")
      expect(podcast_classes).to include("bg-green-100 text-green-800")
      
      # PDF (orange) - warning/informational
      pdf_classes = helper.media_type_badge_classes("pdf")
      expect(pdf_classes).to include("bg-orange-100 text-orange-800")
      
      # Unknown (gray) - neutral fallback
      unknown_classes = helper.media_type_badge_classes("unknown")
      expect(unknown_classes).to include("bg-gray-100 text-gray-800")
    end

    it 'provides semantic SVG icons with proper sizing' do
      media_types = ["video", "podcast", "pdf", "infographic", "interactive"]
      
      media_types.each do |type|
        icon = helper.media_type_icon(type)
        
        # Consistent sizing for layout
        expect(icon).to include('class="w-3 h-3 mr-1"')
        
        # Proper color inheritance
        expect(icon).to include('fill="currentColor"')
        
        # Standard viewBox for scalability
        expect(icon).to include('viewBox="0 0 24 24"')
      end
    end

    it 'provides clear, descriptive labels' do
      expect(helper.media_type_label("pdf")).to eq("PDF Report") # More descriptive than just "PDF"
      expect(helper.media_type_label("interactive")).to eq("Interactive") # Clear and specific
      expect(helper.media_type_label("infographic")).to eq("Infographic") # Unambiguous
    end
  end
end