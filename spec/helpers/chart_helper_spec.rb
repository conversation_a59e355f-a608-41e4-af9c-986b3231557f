require 'rails_helper'

RSpec.describe ChartHelper, type: :helper do
  describe 'color constants' do
    it 'defines chart colors' do
      expect(ChartHelper::CHART_COLORS).to be_a(Hash)
      expect(ChartHelper::CHART_COLORS[:primary]).to eq("99, 102, 241")
      expect(ChartHelper::CHART_COLORS[:success]).to eq("34, 197, 94")
      expect(ChartHelper::CHART_COLORS[:danger]).to eq("239, 68, 68")
    end

    it 'defines color schemes' do
      expect(ChartHelper::CHART_COLOR_SCHEMES).to be_a(Hash)
      expect(ChartHelper::CHART_COLOR_SCHEMES[:single]).to eq([:primary])
      expect(ChartHelper::CHART_COLOR_SCHEMES[:dual]).to eq([:primary, :success])
      expect(ChartHelper::CHART_COLOR_SCHEMES[:multi]).to eq([:primary, :success, :warning, :danger, :info])
    end

    it 'freezes constants to prevent modification' do
      expect(ChartHelper::CHART_COLORS).to be_frozen
      expect(ChartHelper::CHART_COLOR_SCHEMES).to be_frozen
    end
  end

  describe '#chart_colors' do
    it 'returns colors for single scheme' do
      colors = helper.chart_colors(:single)
      expect(colors).to eq(["99, 102, 241"])
    end

    it 'returns colors for dual scheme' do
      colors = helper.chart_colors(:dual)
      expect(colors).to eq(["99, 102, 241", "34, 197, 94"])
    end

    it 'returns colors for multi scheme' do
      colors = helper.chart_colors(:multi)
      expected = ["99, 102, 241", "34, 197, 94", "251, 146, 60", "239, 68, 68", "14, 165, 233"]
      expect(colors).to eq(expected)
    end

    it 'returns colors for rainbow scheme' do
      colors = helper.chart_colors(:rainbow)
      expect(colors.length).to eq(8)
      expect(colors).to include("99, 102, 241", "34, 197, 94", "168, 85, 247")
    end

    it 'defaults to multi scheme for invalid scheme' do
      colors = helper.chart_colors(:invalid)
      multi_colors = helper.chart_colors(:multi)
      expect(colors).to eq(multi_colors)
    end

    it 'defaults to multi scheme when no argument provided' do
      colors = helper.chart_colors
      multi_colors = helper.chart_colors(:multi)
      expect(colors).to eq(multi_colors)
    end
  end

  describe '#chart_container' do
    let(:sample_data) { { "Jan" => 10, "Feb" => 20, "Mar" => 15 } }

    it 'creates a chart container with basic attributes' do
      result = helper.chart_container(type: "line", data: sample_data)
      
      expect(result).to include('class="chart-container"')
      expect(result).to include('data-controller="chart"')
      expect(result).to include('data-chart-type-value="line"')
      expect(result).to include('style="height: 300px"')
      expect(result).to include('<canvas></canvas>')
    end

    it 'handles custom label' do
      result = helper.chart_container(type: "bar", data: sample_data, label: "Monthly Sales")
      expect(result).to include('data-chart-label-value="Monthly Sales"')
    end

    it 'handles custom height' do
      result = helper.chart_container(type: "pie", data: sample_data, height: "400px")
      expect(result).to include('style="height: 400px"')
    end

    it 'handles custom colors' do
      custom_colors = ["255, 0, 0", "0, 255, 0"]
      result = helper.chart_container(type: "doughnut", data: sample_data, colors: custom_colors)
      # Check for HTML-escaped JSON in data attribute
      expect(result).to include('data-chart-colors-value=')
      expect(result).to include('255, 0, 0')
      expect(result).to include('0, 255, 0')
    end

    it 'handles custom options' do
      options = { responsive: true, maintainAspectRatio: false }
      result = helper.chart_container(type: "bar", data: sample_data, options: options)
      # Check for HTML-escaped JSON in data attribute
      expect(result).to include('data-chart-options-value=')
      expect(result).to include('responsive')
      expect(result).to include('maintainAspectRatio')
    end

    it 'chooses appropriate color scheme based on data size' do
      # Small data (2 items) should use dual colors
      small_data = { "A" => 1, "B" => 2 }
      result = helper.chart_container(type: "pie", data: small_data)
      expect(result).to include('data-chart-colors-value=')
      # Should contain dual color scheme elements
      expect(result).to include('99, 102, 241') # primary
      expect(result).to include('34, 197, 94')  # success

      # Large data (3+ items) should use multi colors
      large_data = { "A" => 1, "B" => 2, "C" => 3 }
      result = helper.chart_container(type: "pie", data: large_data)
      expect(result).to include('data-chart-colors-value=')
      # Should contain multi color scheme elements
      expect(result).to include('99, 102, 241') # primary
      expect(result).to include('34, 197, 94')  # success
      expect(result).to include('251, 146, 60') # warning
    end

    describe 'data format handling' do
      it 'handles hash data format' do
        hash_data = { "US" => 38, "JP" => 36, "GB" => 25 }
        result = helper.chart_container(type: "bar", data: hash_data)
        expect(result).to include('data-chart-data-value=')
        expect(result).to include('US')
        expect(result).to include('38')
      end

      it 'handles array of arrays data format' do
        array_data = [["US", 38], ["JP", 36], ["GB", 25]]
        result = helper.chart_container(type: "bar", data: array_data)
        expect(result).to include('data-chart-data-value=')
        expect(result).to include('US')
        expect(result).to include('38')
      end

      it 'handles simple array data format' do
        array_data = [10, 20, 30]
        result = helper.chart_container(type: "line", data: array_data)
        expect(result).to include('data-chart-data-value=')
        expect(result).to include('10')
        expect(result).to include('20')
        expect(result).to include('30')
      end

      it 'handles empty data' do
        result = helper.chart_container(type: "pie", data: nil)
        expect(result).to include('{}')
      end

      it 'handles invalid data types' do
        result = helper.chart_container(type: "bar", data: "invalid")
        expect(result).to include('{}')
      end
    end

    it 'supports block content' do
      result = helper.chart_container(type: "line", data: sample_data) do
        "<p>Chart loading...</p>".html_safe
      end
      expect(result).to include('<canvas></canvas><p>Chart loading...</p>')
    end
  end

  describe '#line_chart' do
    let(:data) { { "Jan" => 100, "Feb" => 150, "Mar" => 120 } }

    it 'creates a line chart with default options' do
      result = helper.line_chart(data)
      
      expect(result).to include('data-chart-type-value="line"')
      expect(result).to include('data-chart-label-value="Data"')
      expect(result).to include('style="height: 300px"')
      expect(result).to include(ChartHelper::CHART_COLORS[:primary])
    end

    it 'accepts custom label' do
      result = helper.line_chart(data, label: "Revenue")
      expect(result).to include('data-chart-label-value="Revenue"')
    end

    it 'accepts custom height' do
      result = helper.line_chart(data, height: "250px")
      expect(result).to include('style="height: 250px"')
    end

    it 'accepts additional options' do
      result = helper.line_chart(data, tension: 0.4, fill: false)
      expect(result).to include('tension')
      expect(result).to include('fill')
    end
  end

  describe '#bar_chart' do
    let(:data) { { "Q1" => 500, "Q2" => 750, "Q3" => 600, "Q4" => 900 } }

    it 'creates a bar chart with default options' do
      result = helper.bar_chart(data)
      
      expect(result).to include('data-chart-type-value="bar"')
      expect(result).to include('data-chart-label-value="Data"')
      expect(result).to include('style="height: 300px"')
      expect(result).to include(ChartHelper::CHART_COLORS[:success])
    end

    it 'accepts custom label' do
      result = helper.bar_chart(data, label: "Quarterly Sales")
      expect(result).to include('data-chart-label-value="Quarterly Sales"')
    end

    it 'accepts custom height' do
      result = helper.bar_chart(data, height: "350px")
      expect(result).to include('style="height: 350px"')
    end

    it 'accepts additional options' do
      result = helper.bar_chart(data, indexAxis: 'y')
      expect(result).to include('indexAxis')
      expect(result).to include('y')
    end
  end

  describe '#pie_chart' do
    let(:data) { { "Desktop" => 45, "Mobile" => 35, "Tablet" => 20 } }

    it 'creates a pie chart with default options' do
      result = helper.pie_chart(data)
      
      expect(result).to include('data-chart-type-value="pie"')
      expect(result).to include('style="height: 300px"')
      # Should use multi colors for 3 data points
      expect(result).to include('99, 102, 241') # primary
      expect(result).to include('34, 197, 94')  # success
      expect(result).to include('251, 146, 60') # warning
    end

    it 'accepts custom height' do
      result = helper.pie_chart(data, height: "400px")
      expect(result).to include('style="height: 400px"')
    end

    it 'accepts additional options' do
      result = helper.pie_chart(data, cutout: '50%')
      expect(result).to include('cutout')
      expect(result).to include('50%')
    end
  end

  describe '#doughnut_chart' do
    let(:data) { { "Chrome" => 65, "Firefox" => 15, "Safari" => 12, "Edge" => 8 } }

    it 'creates a doughnut chart with default options' do
      result = helper.doughnut_chart(data)
      
      expect(result).to include('data-chart-type-value="doughnut"')
      expect(result).to include('style="height: 300px"')
      # Should use multi colors for 4 data points
      expect(result).to include('99, 102, 241') # primary
      expect(result).to include('34, 197, 94')  # success
      expect(result).to include('251, 146, 60') # warning
    end

    it 'accepts custom height' do
      result = helper.doughnut_chart(data, height: "320px")
      expect(result).to include('style="height: 320px"')
    end

    it 'accepts additional options' do
      result = helper.doughnut_chart(data, cutout: '70%', borderWidth: 2)
      expect(result).to include('cutout')
      expect(result).to include('70%')
      expect(result).to include('borderWidth')
      expect(result).to include('2')
    end
  end

  describe 'edge cases and real-world scenarios' do
    it 'handles very large datasets' do
      large_data = (1..100).to_h { |i| ["Item #{i}", rand(1..1000)] }
      result = helper.bar_chart(large_data)
      
      expect(result).to include('data-chart-type-value="bar"')
      expect(result).to include('data-chart-data-value=')
    end

    it 'handles zero values' do
      data = { "A" => 0, "B" => 10, "C" => 0 }
      result = helper.pie_chart(data)
      expect(result).to include('data-chart-data-value=')
      expect(result).to include('10')
    end

    it 'handles negative values' do
      data = { "Profit" => 100, "Loss" => -50, "Break-even" => 0 }
      result = helper.bar_chart(data)
      expect(result).to include('data-chart-data-value=')
      expect(result).to include('100')
      expect(result).to include('-50')
    end

    it 'handles special characters in labels' do
      data = { "Café & Restaurant" => 45, "Hotels < 5 stars" => 30, "B&B's" => 25 }
      result = helper.pie_chart(data)
      expect(result).to be_present
    end

    it 'handles unicode characters in labels' do
      data = { "Español" => 40, "Français" => 35, "Deutsch" => 25 }
      result = helper.doughnut_chart(data)
      expect(result).to be_present
    end

    it 'handles very long labels' do
      long_label = "A" * 100
      data = { long_label => 50, "Short" => 30, "Medium length label" => 20 }
      result = helper.bar_chart(data)
      expect(result).to include('data-chart-data-value=')
      expect(result).to include('50')
    end

    it 'generates valid HTML structure' do
      result = helper.line_chart({ "A" => 1, "B" => 2 })
      
      # Should be valid HTML
      expect(result).to match(/<div[^>]*>.*<canvas[^>]*><\/canvas>.*<\/div>/m)
      expect(result).to include('data-controller="chart"')
    end

    it 'handles concurrent chart creation' do
      data1 = { "Series 1" => [1, 2, 3] }
      data2 = { "Series 2" => [4, 5, 6] }
      
      result1 = helper.line_chart(data1, label: "Chart 1")
      result2 = helper.bar_chart(data2, label: "Chart 2")
      
      expect(result1).to include('data-chart-label-value="Chart 1"')
      expect(result2).to include('data-chart-label-value="Chart 2"')
      expect(result1).not_to eq(result2)
    end
  end

  describe 'integration with Stimulus chart controller' do
    it 'generates correct data attributes for Stimulus' do
      data = { "Jan" => 100, "Feb" => 200 }
      options = { responsive: true }
      
      result = helper.chart_container(
        type: "line", 
        data: data, 
        label: "Monthly Data",
        options: options
      )
      
      expect(result).to include('data-controller="chart"')
      expect(result).to include('data-chart-type-value="line"')
      expect(result).to include('data-chart-data-value=')
      expect(result).to include('data-chart-label-value="Monthly Data"')
      expect(result).to include('data-chart-colors-value=')
      expect(result).to include('data-chart-options-value=')
    end

    it 'properly escapes JSON data for HTML attributes' do
      data = { "Key with \"quotes\"" => 42, "Key & ampersand" => 24 }
      result = helper.line_chart(data)
      
      # Should contain escaped JSON
      expect(result).to be_present
      expect(result).to be_an_instance_of(ActiveSupport::SafeBuffer)
    end
  end
end