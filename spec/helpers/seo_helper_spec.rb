require 'rails_helper'

RSpec.describe <PERSON><PERSON><PERSON><PERSON><PERSON>, type: :helper do
  before do
    # Mock request for canonical URL generation
    allow(helper).to receive(:request).and_return(
      double(original_url: 'https://linklysis.com/test')
    )
    allow(helper).to receive(:asset_url).and_return('https://linklysis.com/assets/test.png')
    allow(helper).to receive(:content_for).and_return(nil)
  end

  describe 'constants' do
    it 'defines default SEO values' do
      expect(SeoHelper::DEFAULT_TITLE).to include("Linklysis")
      expect(SeoHelper::DEFAULT_DESCRIPTION).to include("Transform your links")
      expect(SeoHelper::DEFAULT_KEYWORDS).to be_an(Array)
      expect(SeoHelper::SITE_NAME).to eq("Linklysis")
      expect(SeoHelper::TWITTER_HANDLE).to eq("@linklysis")
      expect(SeoHelper::DOMAIN).to eq("linklysis.com")
    end

    it 'freezes keyword array' do
      expect(SeoHelper::DEFAULT_KEYWORDS).to be_frozen
    end

    it 'includes relevant keywords' do
      keywords = SeoHelper::DEFAULT_KEYWORDS
      expect(keywords).to include("link")
      expect(keywords).to include("shortener")
      expect(keywords).to include("analytics")
      expect(keywords).to include("custom")
      expect(keywords).to include("domains")
    end
  end

  describe '#seo_meta_tags' do
    it 'generates basic meta tags with defaults' do
      result = helper.seo_meta_tags
      
      expect(result).to include('name="description"')
      expect(result).to include('name="keywords"')
      expect(result).to include('name="author" content="Linklysis"')
      expect(result).to include('name="robots" content="index, follow"')
    end

    it 'generates Open Graph tags' do
      result = helper.seo_meta_tags
      
      expect(result).to include('property="og:title"')
      expect(result).to include('property="og:description"')
      expect(result).to include('property="og:image"')
      expect(result).to include('property="og:url"')
      expect(result).to include('property="og:type" content="website"')
      expect(result).to include('property="og:site_name" content="Linklysis"')
      expect(result).to include('property="og:locale" content="en_US"')
    end

    it 'generates Twitter Card tags' do
      result = helper.seo_meta_tags
      
      expect(result).to include('name="twitter:card" content="summary_large_image"')
      expect(result).to include('name="twitter:site" content="@linklysis"')
      expect(result).to include('name="twitter:creator" content="@linklysis"')
      expect(result).to include('name="twitter:title"')
      expect(result).to include('name="twitter:description"')
      expect(result).to include('name="twitter:image"')
    end

    it 'includes brand color theme tags' do
      result = helper.seo_meta_tags
      
      expect(result).to include('name="theme-color" content="#7c3aed"')
      expect(result).to include('name="msapplication-TileColor" content="#7c3aed"')
    end

    it 'accepts custom options' do
      options = {
        title: "Custom Title",
        description: "Custom description",
        keywords: ["custom", "keywords"],
        canonical_url: "https://example.com/custom",
        type: "article",
        robots: "noindex, nofollow",
        twitter_card: "summary"
      }
      
      result = helper.seo_meta_tags(options)
      
      expect(result).to include('content="Custom Title"')
      expect(result).to include('content="Custom description"')
      expect(result).to include('content="custom, keywords"')
      expect(result).to include('href="https://example.com/custom"')
      expect(result).to include('content="article"')
      expect(result).to include('content="noindex, nofollow"')
      expect(result).to include('content="summary"')
    end

    it 'includes canonical URL when provided' do
      result = helper.seo_meta_tags(canonical_url: "https://example.com/canonical")
      expect(result).to include('rel="canonical" href="https://example.com/canonical"')
    end

    it 'returns HTML safe string' do
      result = helper.seo_meta_tags
      expect(result).to be_html_safe
    end
  end

  describe '#structured_data' do
    it 'generates basic structured data' do
      result = helper.structured_data("Organization")
      
      expect(result).to include('<script type="application/ld+json">')
      expect(result).to include('"@context":"https://schema.org"')
      expect(result).to include('"@type":"Organization"')
      expect(result).to be_html_safe
    end

    it 'generates Organization schema' do
      result = helper.structured_data("Organization")
      
      expect(result).to include('"name":"Linklysis"')
      expect(result).to include('"url":"https://linklysis.com"')
      expect(result).to include('"foundingDate":"2024"')
      expect(result).to include('"sameAs"')
    end

    it 'generates WebSite schema' do
      result = helper.structured_data("WebSite")
      
      expect(result).to include('"@type":"WebSite"')
      expect(result).to include('"potentialAction"')
      expect(result).to include('"SearchAction"')
    end

    it 'generates SoftwareApplication schema' do
      result = helper.structured_data("SoftwareApplication")
      
      expect(result).to include('"@type":"SoftwareApplication"')
      expect(result).to include('"applicationCategory":"BusinessApplication"')
      expect(result).to include('"offers"')
      expect(result).to include('"Free Plan"')
      expect(result).to include('"Professional Plan"')
      expect(result).to include('"Business Plan"')
    end

    it 'generates Article schema' do
      data = {
        title: "Test Article",
        description: "Test description",
        image: "https://example.com/image.jpg",
        published_at: Time.parse("2024-01-01"),
        updated_at: Time.parse("2024-01-02")
      }
      
      result = helper.structured_data("Article", data)
      
      expect(result).to include('"@type":"Article"')
      expect(result).to include('"headline":"Test Article"')
      expect(result).to include('"datePublished":"2024-01-01T00:00:00')
      expect(result).to include('"dateModified":"2024-01-02T00:00:00')
    end

    it 'generates BreadcrumbList schema' do
      data = {
        items: [
          { "@type" => "ListItem", "position" => 1, "name" => "Home" },
          { "@type" => "ListItem", "position" => 2, "name" => "Blog" }
        ]
      }
      
      result = helper.structured_data("BreadcrumbList", data)
      
      expect(result).to include('"@type":"BreadcrumbList"')
      expect(result).to include('"itemListElement"')
      expect(result).to include('"ListItem"')
    end

    it 'handles custom schema types' do
      custom_data = { "customProperty" => "customValue" }
      result = helper.structured_data("CustomType", custom_data)
      
      expect(result).to include('"@type":"CustomType"')
      expect(result).to include('"customProperty":"customValue"')
    end

    it 'merges custom data with schema defaults' do
      custom_data = { "customField" => "customValue" }
      result = helper.structured_data("Organization", custom_data)
      
      expect(result).to include('"name":"Linklysis"') # Default
      expect(result).to include('"customField":"customValue"') # Custom
    end
  end

  describe '#page_title' do
    it 'sets and returns title with site name' do
      allow(helper).to receive(:content_for)
      expect(helper).to receive(:content_for).with(:title, "Test Page | Linklysis")
      
      result = helper.page_title("Test Page")
      expect(result).to eq("Test Page")
    end

    it 'uses default title when no title provided' do
      allow(helper).to receive(:content_for)
      expect(helper).to receive(:content_for).with(:title, SeoHelper::DEFAULT_TITLE)
      
      result = helper.page_title
      expect(result).to eq(SeoHelper::DEFAULT_TITLE)
    end

    it 'handles blank title' do
      allow(helper).to receive(:content_for)
      expect(helper).to receive(:content_for).with(:title, SeoHelper::DEFAULT_TITLE)
      
      result = helper.page_title("")
      expect(result).to eq(SeoHelper::DEFAULT_TITLE)
    end
  end

  describe '#page_seo_config' do
    it 'returns home page config' do
      config = helper.page_seo_config("home")
      
      expect(config[:title]).to include("Professional Link Shortening")
      expect(config[:description]).to include("Transform your links")
      expect(config[:keywords]).to include("link")
      expect(config[:keywords]).to include("shortener")
    end

    it 'returns pricing page config' do
      config = helper.page_seo_config("pricing")
      
      expect(config[:title]).to include("Pricing Plans")
      expect(config[:description]).to include("Choose the perfect plan")
      expect(config[:keywords]).to include("pricing")
      expect(config[:keywords]).to include("plans")
    end

    it 'returns features page config' do
      config = helper.page_seo_config("features")
      
      expect(config[:title]).to include("Features")
      expect(config[:description]).to include("Discover powerful features")
      expect(config[:keywords]).to include("features")
    end

    it 'returns about page config' do
      config = helper.page_seo_config("about")
      
      expect(config[:title]).to include("About Us")
      expect(config[:description]).to include("Learn about Linklysis")
      expect(config[:keywords]).to include("about")
    end

    it 'returns contact page config' do
      config = helper.page_seo_config("contact")
      
      expect(config[:title]).to include("Contact Us")
      expect(config[:description]).to include("Get in touch")
      expect(config[:keywords]).to include("contact")
    end

    it 'returns blog page config' do
      config = helper.page_seo_config("blog")
      
      expect(config[:title]).to include("Blog")
      expect(config[:description]).to include("tips, strategies")
      expect(config[:keywords]).to include("blog")
    end

    it 'returns default config for unknown pages' do
      config = helper.page_seo_config("unknown")
      
      expect(config[:title]).to eq(SeoHelper::DEFAULT_TITLE)
      expect(config[:description]).to eq(SeoHelper::DEFAULT_DESCRIPTION)
      expect(config[:keywords]).to eq(SeoHelper::DEFAULT_KEYWORDS)
    end

    it 'handles symbol input' do
      config = helper.page_seo_config(:home)
      expect(config[:title]).to include("Professional Link Shortening")
    end
  end

  describe '#breadcrumbs' do
    it 'returns nothing for empty crumbs' do
      result = helper.breadcrumbs
      expect(result).to be_nil
    end

    it 'generates breadcrumb structured data' do
      result = helper.breadcrumbs(
        { name: "Home", url: "/" },
        { name: "Blog", url: "/blog" },
        { name: "Article", url: nil }
      )
      
      expect(result).to include('"@type":"BreadcrumbList"')
      expect(result).to include('"position":1')
      expect(result).to include('"name":"Home"')
      expect(result).to include('"item":"https://linklysis.com/"')
      expect(result).to include('"position":2')
      expect(result).to include('"name":"Blog"')
    end

    it 'handles crumbs without URLs' do
      result = helper.breadcrumbs(
        { name: "Home", url: "/" },
        { name: "Current Page" }
      )
      
      expect(result).to include('"name":"Current Page"')
      expect(result).not_to include('"item":"https://linklysis.com/undefined"')
    end
  end

  describe '#hreflang_tags' do
    it 'generates hreflang tags for multiple locales' do
      locales = {
        "en" => "https://linklysis.com/",
        "es" => "https://linklysis.com/es/",
        "fr" => "https://linklysis.com/fr/"
      }
      
      result = helper.hreflang_tags(locales)
      
      expect(result).to include('rel="alternate" hreflang="en"')
      expect(result).to include('rel="alternate" hreflang="es"')
      expect(result).to include('rel="alternate" hreflang="fr"')
      expect(result).to be_html_safe
    end

    it 'returns empty string for no locales' do
      result = helper.hreflang_tags({})
      expect(result).to eq("")
    end
  end

  describe '#social_share_tags' do
    it 'generates social share meta tags' do
      result = helper.social_share_tags(
        "https://example.com/article",
        "Article Title",
        "Article description",
        "https://example.com/image.jpg"
      )
      
      # Facebook/Open Graph
      expect(result).to include('property="og:url" content="https://example.com/article"')
      expect(result).to include('property="og:title" content="Article Title"')
      expect(result).to include('property="og:description" content="Article description"')
      expect(result).to include('property="og:image" content="https://example.com/image.jpg"')
      
      # Twitter
      expect(result).to include('name="twitter:url" content="https://example.com/article"')
      expect(result).to include('name="twitter:title" content="Article Title"')
      expect(result).to include('name="twitter:description" content="Article description"')
      expect(result).to include('name="twitter:image" content="https://example.com/image.jpg"')
      
      expect(result).to be_html_safe
    end

    it 'handles missing image gracefully' do
      result = helper.social_share_tags(
        "https://example.com/article",
        "Article Title",
        "Article description"
      )
      
      expect(result).to include('property="og:url"')
      expect(result).to include('name="twitter:url"')
      expect(result).not_to include('og:image')
      expect(result).not_to include('twitter:image')
    end
  end

  describe 'edge cases and real-world scenarios' do
    it 'handles very long titles and descriptions' do
      long_title = "A" * 200
      long_description = "B" * 500
      
      result = helper.seo_meta_tags(title: long_title, description: long_description)
      
      expect(result).to include(long_title)
      expect(result).to include(long_description)
    end

    it 'handles special characters in content' do
      title = 'Title with "quotes" & ampersands'
      description = "Description with <tags> and 'single quotes'"
      
      result = helper.seo_meta_tags(title: title, description: description)
      
      expect(result).to be_present
      expect(result).to be_html_safe
    end

    it 'handles unicode characters' do
      title = "Título en Español with émojis 🚀"
      description = "Description with unicode: café, naïve, résumé"
      
      result = helper.seo_meta_tags(title: title, description: description)
      
      expect(result).to include(title)
      expect(result).to include(description)
    end

    it 'handles empty arrays and nil values gracefully' do
      result = helper.seo_meta_tags(keywords: [], canonical_url: nil)
      expect(result).to be_present
      expect(result).to be_html_safe
    end

    it 'generates valid JSON-LD for structured data' do
      result = helper.structured_data("Organization")
      
      # Extract JSON from script tag
      json_match = result.match(/<script[^>]*>(.*?)<\/script>/m)
      expect(json_match).not_to be_nil
      
      json_content = json_match[1]
      expect { JSON.parse(json_content) }.not_to raise_error
    end

    it 'handles concurrent meta tag generation' do
      result1 = helper.seo_meta_tags(title: "Page 1")
      result2 = helper.seo_meta_tags(title: "Page 2")
      
      expect(result1).to include("Page 1")
      expect(result2).to include("Page 2")
      expect(result1).not_to eq(result2)
    end

    it 'supports all SoftwareApplication pricing tiers' do
      result = helper.structured_data("SoftwareApplication")
      
      expect(result).to include('"name":"Free Plan"')
      expect(result).to include('"price":"0"')
      expect(result).to include('"name":"Professional Plan"')
      expect(result).to include('"price":"29"')
      expect(result).to include('"name":"Business Plan"')
      expect(result).to include('"price":"99"')
    end
  end

  describe 'SEO best practices compliance' do
    it 'includes essential meta tags' do
      result = helper.seo_meta_tags
      
      # Essential tags
      expect(result).to include('name="description"')
      expect(result).to include('name="keywords"')
      expect(result).to include('name="robots"')
      expect(result).to include('property="og:title"')
      expect(result).to include('property="og:description"')
      expect(result).to include('name="twitter:card"')
    end

    it 'uses appropriate default robots directive' do
      result = helper.seo_meta_tags
      expect(result).to include('content="index, follow"')
    end

    it 'includes theme colors for PWA compatibility' do
      result = helper.seo_meta_tags
      expect(result).to include('name="theme-color"')
      expect(result).to include('name="msapplication-TileColor"')
    end

    it 'uses summary_large_image Twitter card by default' do
      result = helper.seo_meta_tags
      expect(result).to include('content="summary_large_image"')
    end
  end
end