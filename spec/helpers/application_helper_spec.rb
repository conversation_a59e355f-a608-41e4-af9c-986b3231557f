require 'rails_helper'

RSpec.describe ApplicationHelper, type: :helper do
  describe '#render_markdown' do
    context 'with valid markdown' do
      it 'renders basic markdown to HTML with Tailwind classes' do
        markdown = "# Hello World\n\nThis is a **bold** text."
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<h1 class="text-3xl font-bold text-gray-900 mt-8 mb-4">Hello World</h1>')
        expect(result).to include('<p class="text-gray-600 mb-4 leading-relaxed">This is a <strong class="font-bold text-gray-900">bold</strong> text.</p>')
        expect(result).to be_html_safe
      end

      it 'renders different heading levels with appropriate classes' do
        markdown = "# H1\n## H2\n### H3\n#### H4"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<h1 class="text-3xl font-bold text-gray-900 mt-8 mb-4">')
        expect(result).to include('<h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4">')
        expect(result).to include('<h3 class="text-xl font-bold text-gray-900 mt-6 mb-3">')
        expect(result).to include('<h4 class="text-lg font-bold text-gray-900 mt-4 mb-2">')
      end

      it 'renders lists with proper Tailwind classes' do
        markdown = "- Item 1\n- Item 2\n\n1. First\n2. Second"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<ul class="list-disc list-inside space-y-2 mb-4 text-gray-600">')
        expect(result).to include('<ol class="list-decimal list-inside space-y-2 mb-4 text-gray-600">')
        expect(result).to include('<li class="ml-4">')
      end

      it 'renders blockquotes with styling' do
        markdown = "> This is a quote"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<blockquote class="border-l-4 border-purple-500 pl-4 my-4 italic text-gray-700">')
      end

      it 'renders code blocks and inline code with styling' do
        markdown = "Here is `inline code` and:\n\n```\ncode block\n```"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">')
        expect(result).to include('<pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto my-4">')
      end

      it 'renders tables with Tailwind classes' do
        markdown = "| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<table class="min-w-full divide-y divide-gray-200 my-4">')
        expect(result).to include('<thead class="bg-gray-50">')
        expect(result).to include('<tbody class="bg-white divide-y divide-gray-200">')
        expect(result).to include('<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">')
        expect(result).to include('<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">')
      end

      it 'adds target and rel attributes to links' do
        markdown = "[Example](https://example.com)"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('target="_blank"')
        expect(result).to include('rel="noopener"')
        expect(result).to include('class="text-purple-600 hover:text-purple-700 underline"')
      end

      it 'handles autolink feature' do
        markdown = "Visit https://example.com for more info"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<a')
        expect(result).to include('https://example.com')
      end

      it 'renders emphasis with styling' do
        markdown = "This is *italic* text"
        result = helper.render_markdown(markdown)
        
        expect(result).to include('<em class="italic">italic</em>')
      end
    end

    context 'with edge cases' do
      it 'returns empty string for nil input' do
        expect(helper.render_markdown(nil)).to eq("")
      end

      it 'returns empty string for blank input' do
        expect(helper.render_markdown("")).to eq("")
        expect(helper.render_markdown("   ")).to eq("")
      end

      it 'handles empty markdown gracefully' do
        result = helper.render_markdown("\n\n")
        # Empty content after markdown processing should return empty string
        expect(result).to eq("")
      end
    end
  end

  describe '#country_flag' do
    it 'returns correct flag emoji for valid country codes' do
      expect(helper.country_flag('US')).to eq('🇺🇸')
      expect(helper.country_flag('GB')).to eq('🇬🇧')
      expect(helper.country_flag('FR')).to eq('🇫🇷')
      expect(helper.country_flag('DE')).to eq('🇩🇪')
      expect(helper.country_flag('JP')).to eq('🇯🇵')
    end

    it 'handles lowercase country codes' do
      expect(helper.country_flag('us')).to eq('🇺🇸')
      expect(helper.country_flag('gb')).to eq('🇬🇧')
    end

    it 'returns world emoji for blank input' do
      expect(helper.country_flag(nil)).to eq('🌍')
      expect(helper.country_flag('')).to eq('🌍')
      expect(helper.country_flag('   ')).to eq('🌍')
    end

    it 'handles invalid country codes gracefully' do
      # Should still attempt to convert, but may not be a valid flag
      result = helper.country_flag('XX')
      expect(result).to be_a(String)
      expect(result.length).to eq(2) # Regional indicator symbols are 2 characters
    end

    it 'rescues encoding errors' do
      # Test the rescue block by potentially causing an encoding error
      allow(helper).to receive(:country_flag).and_call_original
      
      # This should still work
      expect(helper.country_flag('US')).to eq('🇺🇸')
    end
  end

  describe '#safe_qr_code_svg' do
    let(:link) { double('link') }
    let(:svg_content) { '<svg>QR Code SVG</svg>' }

    before do
      allow(link).to receive(:qr_code_svg).and_return(svg_content)
    end

    it 'generates QR code SVG through link service' do
      expect(link).to receive(:qr_code_svg).with({ module_size: 8 })
      helper.safe_qr_code_svg(link, module_size: 8)
    end

    it 'returns HTML safe content' do
      result = helper.safe_qr_code_svg(link)
      expect(result).to be_html_safe
      expect(result).to eq(svg_content)
    end

    it 'passes options to QR code service' do
      options = { module_size: 10, color: 'red' }
      expect(link).to receive(:qr_code_svg).with(options)
      helper.safe_qr_code_svg(link, options)
    end
  end

  describe '#country_name' do
    it 'returns correct country names for valid codes' do
      expect(helper.country_name('US')).to eq('United States')
      expect(helper.country_name('GB')).to eq('United Kingdom')
      expect(helper.country_name('FR')).to eq('France')
      expect(helper.country_name('DE')).to eq('Germany')
      expect(helper.country_name('JP')).to eq('Japan')
    end

    it 'handles lowercase country codes' do
      expect(helper.country_name('us')).to eq('United States')
      expect(helper.country_name('gb')).to eq('United Kingdom')
    end

    it 'returns "Unknown" for blank input' do
      expect(helper.country_name(nil)).to eq('Unknown')
      expect(helper.country_name('')).to eq('Unknown')
      expect(helper.country_name('   ')).to eq('Unknown')
    end

    it 'returns the country code itself for unknown codes' do
      expect(helper.country_name('XX')).to eq('XX')
      expect(helper.country_name('ZZ')).to eq('ZZ')
    end

    it 'includes countries from all continents' do
      # Test a sampling from different continents
      expect(helper.country_name('NG')).to eq('Nigeria')      # Africa
      expect(helper.country_name('BR')).to eq('Brazil')       # South America
      expect(helper.country_name('IN')).to eq('India')        # Asia
      expect(helper.country_name('AU')).to eq('Australia')    # Oceania
      expect(helper.country_name('NO')).to eq('Norway')       # Europe
      expect(helper.country_name('CA')).to eq('Canada')       # North America
    end

    it 'handles special territories and regions' do
      expect(helper.country_name('PR')).to eq('Puerto Rico')
      expect(helper.country_name('HK')).to eq('Hong Kong')  
      expect(helper.country_name('AQ')).to eq('Antarctica')
    end
  end
end