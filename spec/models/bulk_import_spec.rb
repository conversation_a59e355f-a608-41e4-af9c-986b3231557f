require 'rails_helper'

RSpec.describe BulkImport, type: :model do
  let(:user) { create(:user) }
  let(:bulk_import) { create(:bulk_import, user: user) }

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:team).optional }
  end

  describe 'validations' do
    it { should validate_inclusion_of(:status).in_array(BulkImport::STATUSES) }
  end

  describe 'scopes' do
    let!(:recent_import) { create(:bulk_import, user: user, created_at: 1.day.ago) }
    let!(:old_import) { create(:bulk_import, user: user, created_at: 1.week.ago) }
    let!(:completed_import) { create(:bulk_import, user: user, status: 'completed') }
    let!(:processing_import) { create(:bulk_import, user: user, status: 'processing') }

    describe '.recent' do
      it 'orders by created_at desc' do
        result = BulkImport.recent.to_a
        expect(result.first).to eq(bulk_import)
        expect(result.map(&:created_at)).to eq(result.map(&:created_at).sort.reverse)
      end
    end

    describe '.by_status' do
      it 'filters by status' do
        expect(BulkImport.by_status('completed')).to contain_exactly(completed_import)
        expect(BulkImport.by_status('processing')).to contain_exactly(processing_import)
      end
    end
  end

  describe 'status predicates' do
    it 'returns correct status predicates' do
      expect(build(:bulk_import, status: 'pending')).to be_pending
      expect(build(:bulk_import, status: 'processing')).to be_processing
      expect(build(:bulk_import, status: 'completed')).to be_completed
      expect(build(:bulk_import, status: 'failed')).to be_failed
    end
  end

  describe '#success_rate' do
    context 'when no rows processed' do
      it 'returns 0' do
        import = build(:bulk_import, processed_rows: 0)
        expect(import.success_rate).to eq(0)
      end
    end

    context 'when rows processed' do
      it 'calculates success rate correctly' do
        import = build(:bulk_import, processed_rows: 10, successful_rows: 8)
        expect(import.success_rate).to eq(80.0)
      end

      it 'handles partial success rates' do
        import = build(:bulk_import, processed_rows: 3, successful_rows: 2)
        expect(import.success_rate).to eq(66.67)
      end
    end
  end

  describe '#mark_as_processing!' do
    it 'updates status and started_at' do
      expect { bulk_import.mark_as_processing! }.to change { bulk_import.reload.status }.to('processing')
      expect(bulk_import.started_at).to be_within(1.second).of(Time.current)
    end
  end

  describe '#mark_as_completed!' do
    it 'updates status and completed_at' do
      expect { bulk_import.mark_as_completed! }.to change { bulk_import.reload.status }.to('completed')
      expect(bulk_import.completed_at).to be_within(1.second).of(Time.current)
    end
  end

  describe '#mark_as_failed!' do
    context 'without error message' do
      it 'updates status and completed_at' do
        expect { bulk_import.mark_as_failed! }.to change { bulk_import.reload.status }.to('failed')
        expect(bulk_import.completed_at).to be_within(1.second).of(Time.current)
      end
    end

    context 'with error message' do
      it 'updates status, completed_at, and error_details' do
        error_message = 'Import failed due to invalid file'
        expect { bulk_import.mark_as_failed!(error_message) }.to change { bulk_import.reload.status }.to('failed')
        expect(bulk_import.completed_at).to be_within(1.second).of(Time.current)
        expect(bulk_import.error_details['general_error']).to eq(error_message)
      end
    end
  end

  describe '#add_error' do
    it 'adds error for specific row' do
      bulk_import.add_error(5, 'Invalid URL format')

      expect(bulk_import.reload.error_details['rows']['5']).to eq('Invalid URL format')
    end

    it 'adds multiple errors for different rows' do
      bulk_import.add_error(1, 'Missing URL')
      bulk_import.add_error(3, 'Invalid date format')

      bulk_import.reload
      expect(bulk_import.error_details['rows']['1']).to eq('Missing URL')
      expect(bulk_import.error_details['rows']['3']).to eq('Invalid date format')
    end
  end

  describe '#duration' do
    context 'when not started' do
      it 'returns nil' do
        expect(bulk_import.duration).to be_nil
      end
    end

    context 'when started but not completed' do
      it 'returns duration from start to now' do
        start_time = 5.minutes.ago
        bulk_import.update!(started_at: start_time)
        expect(bulk_import.duration).to be_within(5).of(300) # approximately 5 minutes
      end
    end

    context 'when completed' do
      it 'returns duration from start to completion' do
        bulk_import.update!(started_at: 10.minutes.ago, completed_at: 2.minutes.ago)
        expect(bulk_import.duration).to be_within(1).of(480) # approximately 8 minutes in seconds
      end
    end
  end

  describe '#formatted_duration' do
    context 'when no duration' do
      it 'returns nil' do
        expect(bulk_import.formatted_duration).to be_nil
      end
    end

    context 'with seconds only' do
      it 'formats correctly' do
        bulk_import.update!(started_at: 30.seconds.ago, completed_at: Time.current)
        expect(bulk_import.formatted_duration).to eq('30s')
      end
    end

    context 'with minutes and seconds' do
      it 'formats correctly' do
        bulk_import.update!(started_at: 90.seconds.ago, completed_at: Time.current)
        expect(bulk_import.formatted_duration).to eq('1m 30s')
      end
    end

    context 'with hours, minutes' do
      it 'formats correctly' do
        bulk_import.update!(started_at: 3900.seconds.ago, completed_at: Time.current) # 1h 5m
        expect(bulk_import.formatted_duration).to eq('1h 5m')
      end
    end
  end
end
