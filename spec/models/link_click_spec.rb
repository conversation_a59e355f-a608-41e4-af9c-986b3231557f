require 'rails_helper'

RSpec.describe LinkClick, type: :model do
  describe 'associations' do
    it { should belong_to(:link).counter_cache(true) }
  end

  describe 'validations' do
    it { should validate_presence_of(:link) }
    it { should validate_presence_of(:clicked_at) }
  end

  describe 'store_accessors' do
    let(:click) { create(:link_click) }

    it 'provides attribution data accessors' do
      click.utm_source = 'google'
      click.utm_medium = 'cpc'
      click.save!

      click.reload
      expect(click.utm_source).to eq('google')
      expect(click.utm_medium).to eq('cpc')
    end

    it 'provides tracking data accessors' do
      click.ip_address = '*******'
      click.country_code = 'US'
      click.device_type = 'mobile'
      click.save!

      click.reload
      expect(click.ip_address).to eq('*******')
      expect(click.country_code).to eq('US')
      expect(click.device_type).to eq('mobile')
    end
  end

  describe 'callbacks' do
    let(:click) { build(:link_click) }

    it 'enqueues analytics aggregation job' do
      expect {
        click.save!
      }.to have_enqueued_job(AnalyticsAggregationJob).with(click)
    end

    it 'broadcasts real-time update' do
      allow(ActionCable.server).to receive(:broadcast)

      click.save!

      expect(ActionCable.server).to have_received(:broadcast).with(
        "user_#{click.link.user_id}_analytics",
        hash_including(
          link_id: click.link_id,
          total_clicks: kind_of(Integer),
          timestamp: kind_of(String)
        )
      )
    end
  end

  describe 'scopes' do
    let!(:today_click) { create(:link_click, clicked_at: Time.current) }
    let!(:yesterday_click) { create(:link_click, clicked_at: 1.day.ago) }
    let!(:bot_click) { create(:link_click, tracking_data: { bot: true }) }

    describe '.today' do
      it 'returns clicks from today' do
        expect(LinkClick.today).to include(today_click)
        expect(LinkClick.today).not_to include(yesterday_click)
      end
    end

    describe '.not_bot' do
      it 'excludes bot traffic' do
        expect(LinkClick.not_bot).not_to include(bot_click)
      end
    end

    describe '.unique_by_ip' do
      before do
        create(:link_click, tracking_data: { ip_address: '*******' })
        create(:link_click, tracking_data: { ip_address: '*******' })
        create(:link_click, tracking_data: { ip_address: '*******' })
      end

      it 'returns one record per unique IP address' do
        unique_clicks = LinkClick.unique_by_ip
        unique_ips = unique_clicks.map(&:ip_address).compact
        expect(unique_ips).to match_array([ '*******', '*******' ])
      end
    end
  end
end
