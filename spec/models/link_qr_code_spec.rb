require 'rails_helper'

RSpec.describe Link, 'QR code methods' do
  let(:link) { create(:link, short_code: 'test123') }

  describe '#qr_code_svg' do
    it 'generates an SVG QR code' do
      svg = link.qr_code_svg
      expect(svg).to be_a(String)
      expect(svg).to include('<svg')
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(format: :svg, size: 10)).and_call_original
      link.qr_code_svg(size: 10)
    end
  end

  describe '#qr_code_png' do
    it 'generates a PNG QR code' do
      png = link.qr_code_png
      expect(png).to be_a(ChunkyPNG::Image)
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(format: :png, png_size: 500)).and_call_original
      link.qr_code_png(png_size: 500)
    end
  end

  describe '#qr_code_data_url' do
    it 'generates a data URL' do
      data_url = link.qr_code_data_url
      expect(data_url).to start_with('data:image/png;base64,')
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(color: 'ff0000')).and_call_original
      link.qr_code_data_url(color: 'ff0000')
    end
  end

  describe '#qr_code_pdf' do
    it 'generates a PDF' do
      pdf_data = link.qr_code_pdf
      expect(pdf_data).to be_a(String)
      expect(pdf_data).to start_with('%PDF')
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(format: :pdf, color: 'ff0000')).and_call_original
      link.qr_code_pdf(color: 'ff0000')
    end
  end

  describe 'sharing and embedding' do
    describe '#qr_code_embed_code' do
      it 'generates HTML embed code' do
        embed_code = link.qr_code_embed_code

        expect(embed_code).to include('<div style="text-align: center;">')
        expect(embed_code).to include('<img src=')
        expect(embed_code).to include('QR Code for')
        expect(embed_code).to include(link.short_url)
      end

      it 'includes options as query parameters' do
        options = { module_size: 10, color: 'ff0000' }
        embed_code = link.qr_code_embed_code(options)

        expect(embed_code).to include('module_size=10')
        expect(embed_code).to include('color=ff0000')
      end

      it 'uses request base URL when provided' do
        request = double('request', base_url: 'https://custom.example.com')
        embed_code = link.qr_code_embed_code({}, request)

        expect(embed_code).to include('https://custom.example.com')
      end
    end

    describe '#qr_code_share_data' do
      it 'generates sharing URLs and data' do
        share_data = link.qr_code_share_data

        expect(share_data[:twitter_url]).to include('twitter.com/intent/tweet')
        expect(share_data[:twitter_url]).to include(CGI.escape(link.short_url))
        expect(share_data[:linkedin_url]).to include('linkedin.com/sharing')
        expect(share_data[:email_subject]).to include(link.short_url)
        expect(share_data[:email_body]).to include(link.original_url)
      end
    end

    describe '#qr_code_generation_stats' do
      it 'returns stats when no analytics exist' do
        stats = link.qr_code_generation_stats

        expect(stats[:total_generations]).to eq(0)
        expect(stats[:by_format]).to eq({})
        expect(stats[:recent_generations]).to eq([])
        expect(stats[:popular_format]).to be_nil
      end

      it 'returns stats when analytics exist' do
        create(:qr_code_analytic, link: link, format: 'svg')
        create(:qr_code_analytic, link: link, format: 'png')
        create(:qr_code_analytic, link: link, format: 'svg')

        stats = link.qr_code_generation_stats

        expect(stats[:total_generations]).to eq(3)
        expect(stats[:by_format]).to eq({ 'svg' => 2, 'png' => 1 })
        expect(stats[:recent_generations].count).to eq(3)
        expect(stats[:popular_format]).to eq('svg')
      end
    end
  end
end
