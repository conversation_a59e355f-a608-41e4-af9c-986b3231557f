require 'rails_helper'

RSpec.describe QrCodeAnalytic, type: :model do
  let(:link) { create(:link) }

  describe 'associations' do
    it { should belong_to(:link) }
  end

  describe 'validations' do
    it { should validate_presence_of(:format) }
    it { should validate_presence_of(:generated_at) }
    it { should validate_inclusion_of(:format).in_array(%w[svg png pdf]) }
  end

  describe 'scopes' do
    let!(:svg_analytic) { create(:qr_code_analytic, link: link, format: 'svg', generated_at: 1.day.ago) }
    let!(:png_analytic) { create(:qr_code_analytic, link: link, format: 'png', generated_at: 2.days.ago) }
    let!(:pdf_analytic) { create(:qr_code_analytic, link: link, format: 'pdf', generated_at: 3.days.ago) }

    describe '.recent' do
      it 'orders by generated_at desc' do
        expect(described_class.recent).to eq([ svg_analytic, png_analytic, pdf_analytic ])
      end
    end

    describe '.by_format' do
      it 'filters by format' do
        expect(described_class.by_format('svg')).to eq([ svg_analytic ])
        expect(described_class.by_format('png')).to eq([ png_analytic ])
      end
    end

    describe '.today' do
      let!(:today_analytic) { create(:qr_code_analytic, link: link, generated_at: Time.current) }

      it 'returns analytics from today' do
        expect(described_class.today).to include(today_analytic)
        expect(described_class.today).not_to include(svg_analytic)
      end
    end

    describe '.this_week' do
      it 'returns analytics from this week' do
        week_analytics = described_class.this_week
        expect(week_analytics).to include(svg_analytic)
        expect(week_analytics).to include(png_analytic)
        # pdf_analytic is 3 days ago, which should be within a week
        expect(week_analytics).to include(pdf_analytic)
      end
    end
  end

  describe '.track_generation' do
    let(:options) { { module_size: 8, color: 'ff0000' } }
    let(:request) { double('request', remote_ip: '127.0.0.1', user_agent: 'Test Browser') }

    it 'creates a new analytic record' do
      expect {
        described_class.track_generation(link, 'svg', options, request)
      }.to change(described_class, :count).by(1)
    end

    it 'stores the correct data' do
      analytic = described_class.track_generation(link, 'svg', options, request)

      expect(analytic.link).to eq(link)
      expect(analytic.format).to eq('svg')
      expect(analytic.options).to eq(options.stringify_keys)
      expect(analytic.ip_address).to eq('127.0.0.1')
      expect(analytic.user_agent).to eq('Test Browser')
      expect(analytic.generated_at).to be_within(1.second).of(Time.current)
    end

    it 'works without request object' do
      expect {
        described_class.track_generation(link, 'png', options, nil)
      }.to change(described_class, :count).by(1)
    end
  end

  describe '.popular_formats' do
    before do
      create_list(:qr_code_analytic, 3, link: link, format: 'svg')
      create_list(:qr_code_analytic, 2, link: link, format: 'png')
      create(:qr_code_analytic, link: link, format: 'pdf')
    end

    it 'returns formats ordered by popularity' do
      result = described_class.popular_formats
      expect(result).to eq([ [ 'svg', 3 ], [ 'png', 2 ], [ 'pdf', 1 ] ])
    end
  end

  describe '.format_breakdown' do
    before do
      create_list(:qr_code_analytic, 2, link: link, format: 'svg')
      create(:qr_code_analytic, link: link, format: 'png')
    end

    it 'returns count by format' do
      result = described_class.format_breakdown
      expect(result).to eq({ 'svg' => 2, 'png' => 1 })
    end
  end

  describe 'store accessors' do
    let(:analytic) { create(:qr_code_analytic, link: link, options: { module_size: 8, color: 'ff0000' }) }

    it 'provides access to options fields' do
      expect(analytic.module_size).to eq(8)
      expect(analytic.color).to eq('ff0000')
    end
  end
end
