require 'rails_helper'

RSpec.describe BlogPost, type: :model do
  describe 'validations' do
    subject { build(:blog_post, slug: 'test-slug') }

    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:content) }
    it { should validate_uniqueness_of(:slug) }

    it 'creates a valid blog post with required attributes' do
      post = build(:blog_post, title: 'Test Post', slug: 'test-post', content: 'Test content')
      expect(post).to be_valid
    end

    it 'is invalid without a title' do
      post = build(:blog_post, title: nil)
      expect(post).not_to be_valid
      expect(post.errors[:title]).to include("can't be blank")
    end

    it 'is invalid without content' do
      post = build(:blog_post, content: nil)
      expect(post).not_to be_valid
      expect(post.errors[:content]).to include("can't be blank")
    end

    it 'requires unique slug' do
      existing_post = create(:blog_post, slug: 'unique-slug')
      duplicate_post = build(:blog_post, slug: 'unique-slug')
      
      expect(duplicate_post).not_to be_valid
      expect(duplicate_post.errors[:slug]).to include('has already been taken')
    end
  end

  describe 'callbacks' do
    describe '#generate_slug' do
      context 'on create' do
        it 'generates slug from title when slug is blank' do
          post = BlogPost.new(title: 'My Awesome Blog Post', content: 'Content')
          post.valid? # Trigger validations/callbacks
          
          expect(post.slug).to eq('my-awesome-blog-post')
        end

        it 'handles special characters in title' do
          post = BlogPost.new(title: 'Blog Post with Special Characters! & Symbols @#$', content: 'Content')
          post.valid?
          
          expect(post.slug).to eq('blog-post-with-special-characters-symbols')
        end

        it 'handles titles with numbers' do
          post = BlogPost.new(title: 'Rails 7.1 New Features', content: 'Content')
          post.valid?
          
          expect(post.slug).to eq('rails-7-1-new-features')
        end

        it 'handles titles with Unicode characters' do
          post = BlogPost.new(title: 'Développement Rails Français', content: 'Content')
          post.valid?
          
          expect(post.slug).to eq('developpement-rails-francais')
        end

        it 'does not overwrite existing slug' do
          post = BlogPost.new(title: 'Test Post', slug: 'custom-slug', content: 'Content')
          post.valid?
          
          expect(post.slug).to eq('custom-slug')
        end

        it 'does not generate slug when title is blank' do
          post = BlogPost.new(title: nil, content: 'Content')
          post.valid?
          
          expect(post.slug).to be_nil
        end
      end
    end
  end

  describe 'scopes' do
    let!(:published_post) { create(:blog_post, :published, published_at: 2.days.ago) }
    let!(:recent_published_post) { create(:blog_post, :published, published_at: 1.day.ago) }
    let!(:unpublished_post) { create(:blog_post, :unpublished) }
    let!(:future_post) { create(:blog_post, :future, published_at: 1.day.from_now) }
    let!(:featured_post) { create(:blog_post, :featured, :published) }
    let!(:announcement_post) { create(:blog_post, :published, category: 'announcements') }
    let!(:tutorial_post) { create(:blog_post, :published, category: 'tutorials') }

    describe '.published' do
      it 'returns only published posts in reverse chronological order' do
        published_posts = BlogPost.published
        
        expect(published_posts).to include(published_post, recent_published_post, featured_post, announcement_post, tutorial_post)
        expect(published_posts).not_to include(unpublished_post, future_post)
        
        # Check order (most recent first)
        expect(published_posts.first.published_at).to be > published_posts.last.published_at
      end

      it 'excludes future posts' do
        published_posts = BlogPost.published
        expect(published_posts).not_to include(future_post)
      end
    end

    describe '.featured' do
      it 'returns only featured posts' do
        featured_posts = BlogPost.featured
        
        expect(featured_posts).to include(featured_post)
        expect(featured_posts).not_to include(published_post, unpublished_post, future_post)
      end
    end

    describe '.by_category' do
      it 'returns posts by specific category' do
        announcement_posts = BlogPost.by_category('announcements')
        tutorial_posts = BlogPost.by_category('tutorials')
        
        expect(announcement_posts).to include(announcement_post)
        expect(announcement_posts).not_to include(tutorial_post)
        
        expect(tutorial_posts).to include(tutorial_post)
        expect(tutorial_posts).not_to include(announcement_post)
      end

      it 'returns empty collection for non-existent category' do
        posts = BlogPost.by_category('non-existent')
        expect(posts).to be_empty
      end
    end
  end

  describe '#to_param' do
    it 'returns the slug for URL generation' do
      post = build(:blog_post, slug: 'my-blog-post-slug')
      expect(post.to_param).to eq('my-blog-post-slug')
    end

    it 'handles nil slug gracefully' do
      post = build(:blog_post, slug: nil)
      expect(post.to_param).to be_nil
    end
  end

  describe '#published?' do
    it 'returns true for published posts with past publish date' do
      post = build(:blog_post, published_at: 1.day.ago)
      expect(post.published?).to be true
    end

    it 'returns false for unpublished posts' do
      post = build(:blog_post, published_at: nil)
      expect(post.published?).to be false
    end

    it 'returns false for future posts' do
      post = build(:blog_post, published_at: 1.day.from_now)
      expect(post.published?).to be false
    end

    it 'returns true for posts published exactly now' do
      now = Time.current
      post = build(:blog_post, published_at: now)
      
      # Allow small time differences due to test execution time
      expect(post.published?).to be true
    end
  end

  describe '#excerpt' do
    it 'returns truncated content without markdown headers' do
      content = "## Introduction\n\nThis is a blog post about Rails development. It covers many topics including controllers, models, and views."
      post = build(:blog_post, content: content)
      
      excerpt = post.excerpt(50)
      expect(excerpt).not_to include('## Introduction')
      expect(excerpt.length).to be <= 50
      expect(excerpt).to include('This is a blog post')
    end

    it 'handles content without headers' do
      content = "This is regular content without any headers. It should be truncated normally."
      post = build(:blog_post, content: content)
      
      excerpt = post.excerpt(30)
      expect(excerpt.length).to be <= 30
      expect(excerpt).to include('This is regular')
    end

    it 'returns empty string for blank content' do
      post = build(:blog_post, content: nil)
      expect(post.excerpt).to eq('')
      
      post = build(:blog_post, content: '')
      expect(post.excerpt).to eq('')
    end

    it 'uses default length of 150 characters' do
      long_content = 'A' * 200
      post = build(:blog_post, content: long_content)
      
      excerpt = post.excerpt
      expect(excerpt.length).to be <= 150
    end

    it 'handles custom length parameter' do
      content = 'A' * 100
      post = build(:blog_post, content: content)
      
      excerpt = post.excerpt(25)
      expect(excerpt.length).to be <= 25
    end

    it 'removes multiple header levels' do
      content = "# Main Header\n## Subheader\n### Sub-subheader\n\nActual content here."
      post = build(:blog_post, content: content)
      
      excerpt = post.excerpt
      expect(excerpt).not_to include('# Main Header')
      expect(excerpt).not_to include('## Subheader')
      expect(excerpt).not_to include('### Sub-subheader')
      expect(excerpt).to include('Actual content')
    end
  end

  describe 'edge cases and real-world scenarios' do
    it 'handles slug generation conflicts with database uniqueness' do
      create(:blog_post, title: 'Test Post', slug: 'test-post')
      
      # This would generate the same slug, but validation should catch it
      post2 = build(:blog_post, title: 'Test Post', slug: nil)  # Will auto-generate 'test-post'
      post2.valid? # Trigger the callback
      
      expect(post2).not_to be_valid
      expect(post2.errors[:slug]).to include('has already been taken')
    end

    it 'supports blog post creation workflow' do
      post = BlogPost.new(
        title: 'Getting Started with Rails 8',
        content: 'Rails 8 introduces many new features...',
        category: 'tutorials',
        author: 'Jane Developer'
      )
      
      expect(post).to be_valid
      expect(post.save).to be true
      expect(post.slug).to eq('getting-started-with-rails-8')
      expect(post).to be_persisted
    end

    it 'handles publishing workflow' do
      # Draft post
      post = create(:blog_post, :unpublished)
      expect(post.published?).to be false
      
      # Publish post
      post.update!(published_at: Time.current)
      expect(post.published?).to be true
      expect(BlogPost.published).to include(post)
    end

    it 'handles media content types' do
      video_post = create(:blog_post, :with_media)
      expect(video_post.media_type).to eq('video')
      expect(video_post.media_url).to be_present
      expect(video_post.video_duration).to be_present
      
      podcast_post = create(:blog_post, :podcast)
      expect(podcast_post.media_type).to eq('podcast')
      expect(podcast_post.episode_number).to be_present
    end

    it 'maintains data integrity during bulk operations' do
      posts_data = [
        { title: 'Post 1', slug: 'post-1', content: 'Content 1' },
        { title: 'Post 2', slug: 'post-2', content: 'Content 2' },
        { title: 'Post 3', slug: 'post-3', content: 'Content 3' }
      ]
      
      posts = posts_data.map { |data| BlogPost.create!(data) }
      
      expect(posts.size).to eq(3)
      expect(posts.all?(&:persisted?)).to be true
      expect(BlogPost.where(slug: posts_data.map { |d| d[:slug] }).count).to eq(3)
    end

    it 'handles SEO and categorization features' do
      post = create(:blog_post, 
        category: 'tutorials',
        tags: ['rails', 'authentication', 'security'],
        featured: true
      )
      
      expect(BlogPost.by_category('tutorials')).to include(post)
      expect(BlogPost.featured).to include(post)
      expect(post.tags).to include('rails', 'authentication', 'security')
    end
  end

  describe 'factory and database constraints' do
    it 'creates valid blog post with factory' do
      post = create(:blog_post)
      expect(post).to be_persisted
      expect(post).to be_valid
      expect(post.title).to be_present
      expect(post.slug).to be_present
      expect(post.content).to be_present
    end

    it 'creates blog post with traits' do
      featured_post = create(:blog_post, :featured)
      expect(featured_post.featured?).to be true
      
      unpublished_post = create(:blog_post, :unpublished)
      expect(unpublished_post.published?).to be false
      
      future_post = create(:blog_post, :future)
      expect(future_post.published?).to be false
      expect(future_post.published_at).to be > Time.current
    end

    it 'handles database-level constraints' do
      post1 = create(:blog_post, slug: 'unique-slug')
      
      # Database should enforce uniqueness
      expect {
        BlogPost.create!(title: 'Different Title', slug: 'unique-slug', content: 'Content')
      }.to raise_error(ActiveRecord::RecordInvalid)
    end
  end
end