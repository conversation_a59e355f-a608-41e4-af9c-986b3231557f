require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'associations' do
    it { should have_many(:links).dependent(:destroy) }
    it { should have_many(:teams).through(:team_memberships) }
    it { should have_many(:team_memberships).dependent(:destroy) }
    it { should have_many(:api_tokens).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:email) }
    it { should validate_uniqueness_of(:email).case_insensitive }
  end

  describe 'enums' do
    it { should define_enum_for(:role).with_values(user: 0, admin: 1, enterprise: 2) }
    it { should define_enum_for(:subscription_plan).with_values(free: 0, professional: 1, business: 2, enterprise: 3) }
  end

  describe '#total_clicks' do
    let(:user) { create(:user) }
    let!(:link1) { create(:link, user: user) }
    let!(:link2) { create(:link, user: user) }

    before do
      create_list(:link_click, 3, link: link1)
      create_list(:link_click, 2, link: link2)
    end

    it 'returns total clicks across all links' do
      expect(user.total_clicks).to eq(5)
    end

    it 'caches the result' do
      expect(Rails.cache).to receive(:fetch).with([ "user_clicks", user.id, Date.current ]).and_call_original
      user.total_clicks
    end
  end

  describe '#member_of?' do
    let(:user) { create(:user) }
    let(:team) { create(:team) }

    context 'when user is a member' do
      before { create(:team_membership, user: user, team: team) }

      it 'returns true' do
        expect(user.member_of?(team)).to be true
      end
    end

    context 'when user is not a member' do
      it 'returns false' do
        expect(user.member_of?(team)).to be false
      end
    end
  end

  describe '#can_create_link?' do
    subject { user.can_create_link? }

    context 'with free plan' do
      let(:user) { create(:user, subscription_plan: 'free') }

      context 'under limit' do
        it { is_expected.to be true }
      end

      context 'at limit' do
        before { create_list(:link, 1000, user: user, created_at: 2.weeks.ago) }
        it { is_expected.to be false }
      end
    end

    context 'with enterprise plan' do
      let(:user) { create(:user, subscription_plan: 'enterprise') }
      before { create_list(:link, 10000, user: user) }

      it { is_expected.to be true }
    end
  end
end
