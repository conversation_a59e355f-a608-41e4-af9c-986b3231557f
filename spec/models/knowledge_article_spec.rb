require 'rails_helper'

RSpec.describe KnowledgeArticle, type: :model do
  subject(:article) { build(:knowledge_article) }

  describe 'associations' do
    it { should belong_to(:knowledge_category) }
  end

  describe 'validations' do
    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:content) }
    it { should validate_presence_of(:slug) }
    it { should validate_numericality_of(:position).only_integer.is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:helpful_count).only_integer.is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:not_helpful_count).only_integer.is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:view_count).only_integer.is_greater_than_or_equal_to(0) }

    it 'validates slug uniqueness within category' do
      category = create(:knowledge_category)
      create(:knowledge_article, knowledge_category: category, slug: 'test-slug')

      duplicate = build(:knowledge_article, knowledge_category: category, slug: 'test-slug')
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:slug]).to include('has already been taken')
    end

    it 'allows same slug in different categories' do
      category1 = create(:knowledge_category)
      category2 = create(:knowledge_category)

      create(:knowledge_article, knowledge_category: category1, slug: 'test-slug')
      duplicate = build(:knowledge_article, knowledge_category: category2, slug: 'test-slug')

      expect(duplicate).to be_valid
    end
  end

  describe 'callbacks' do
    describe '#generate_slug' do
      it 'generates slug from title when slug is blank' do
        article = build(:knowledge_article, title: 'How to Create Links', slug: nil)
        article.valid?
        expect(article.slug).to eq('how-to-create-links')
      end

      it 'does not override existing slug' do
        article = build(:knowledge_article, title: 'How to Create Links', slug: 'custom-slug')
        article.valid?
        expect(article.slug).to eq('custom-slug')
      end
    end

    describe '#set_defaults' do
      it 'sets default values on create' do
        article = create(:knowledge_article,
                        helpful_count: nil,
                        not_helpful_count: nil,
                        view_count: nil,
                        position: nil)

        expect(article.helpful_count).to eq(0)
        expect(article.not_helpful_count).to eq(0)
        expect(article.view_count).to eq(0)
        expect(article.position).to eq(0)
      end

      it 'sets published_at when published is true' do
        travel_to Time.current do
          article = create(:knowledge_article, published: true, published_at: nil)
          expect(article.published_at).to be_within(1.second).of(Time.current)
        end
      end
    end
  end

  describe 'scopes' do
    let!(:published_article) { create(:knowledge_article, published: true) }
    let!(:unpublished_article) { create(:knowledge_article, published: false) }
    let!(:featured_article) { create(:knowledge_article, featured: true, position: 1) }
    let!(:regular_article) { create(:knowledge_article, featured: false, position: 2) }

    describe '.published' do
      it 'returns only published articles' do
        expect(KnowledgeArticle.published).to include(published_article)
        expect(KnowledgeArticle.published).not_to include(unpublished_article)
      end
    end

    describe '.featured' do
      it 'returns only featured articles' do
        expect(KnowledgeArticle.featured).to include(featured_article)
        expect(KnowledgeArticle.featured).not_to include(regular_article)
      end
    end

    describe '.ordered' do
      it 'orders by position then created_at' do
        expect(KnowledgeArticle.ordered.first).to eq(featured_article)
      end
    end

    describe '.search' do
      let!(:article1) { create(:knowledge_article, title: 'How to create links', content: 'Link creation guide') }
      let!(:article2) { create(:knowledge_article, title: 'Analytics overview', keywords: 'tracking, analytics') }
      let!(:article3) { create(:knowledge_article, title: 'Custom domains', content: 'Domain setup instructions') }

      it 'searches by title' do
        results = KnowledgeArticle.search('create')
        expect(results).to include(article1)
        expect(results).not_to include(article2, article3)
      end

      it 'searches by content' do
        results = KnowledgeArticle.search('guide')
        expect(results).to include(article1)
        expect(results).not_to include(article2, article3)
      end

      it 'searches by keywords' do
        results = KnowledgeArticle.search('analytics')
        expect(results).to include(article2)
        expect(results).not_to include(article1, article3)
      end

      it 'returns empty for blank query' do
        results = KnowledgeArticle.search('')
        expect(results).to be_empty
      end
    end
  end

  describe 'instance methods' do
    let(:article) { create(:knowledge_article, helpful_count: 5, not_helpful_count: 2, view_count: 10) }

    describe '#increment_views!' do
      it 'increments view count' do
        expect { article.increment_views! }.to change(article, :view_count).by(1)
      end
    end

    describe '#increment_helpful!' do
      it 'increments helpful count' do
        expect { article.increment_helpful! }.to change(article, :helpful_count).by(1)
      end
    end

    describe '#increment_not_helpful!' do
      it 'increments not helpful count' do
        expect { article.increment_not_helpful! }.to change(article, :not_helpful_count).by(1)
      end
    end

    describe '#helpfulness_ratio' do
      it 'calculates helpfulness ratio' do
        expect(article.helpfulness_ratio).to eq(71.4) # 5/7 * 100
      end

      it 'returns 0 when no feedback' do
        article.update(helpful_count: 0, not_helpful_count: 0)
        expect(article.helpfulness_ratio).to eq(0)
      end
    end

    describe '#published?' do
      it 'returns true when published and published_at is in the past' do
        article = create(:knowledge_article, published: true, published_at: 1.hour.ago)
        expect(article.published?).to be true
      end

      it 'returns false when not published' do
        article = create(:knowledge_article, published: false, published_at: 1.hour.ago)
        expect(article.published?).to be false
      end

      it 'returns false when published_at is in the future' do
        article = create(:knowledge_article, published: true, published_at: 1.hour.from_now)
        expect(article.published?).to be false
      end

      it 'returns false when published_at is nil' do
        article = create(:knowledge_article, published: true, published_at: nil)
        expect(article.published?).to be false
      end
    end
  end

  describe 'factory' do
    it 'creates a valid article' do
      expect(article).to be_valid
    end

    it 'can create published article' do
      article = create(:knowledge_article, :published)
      expect(article).to be_published
    end

    it 'can create featured article' do
      article = create(:knowledge_article, :featured)
      expect(article.featured).to be true
    end
  end
end
