require 'rails_helper'

RSpec.describe TeamMembership, type: :model do
  let(:user) { create(:user) }
  let(:team) { create(:team) }
  let(:other_user) { create(:user) }
  let(:other_team) { create(:team) }

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:team) }
  end

  describe 'validations' do
    subject { build(:team_membership, user: user, team: team) }

    it 'creates a valid team membership with required associations' do
      membership = build(:team_membership, user: user, team: team)
      expect(membership).to be_valid
    end

    it 'is invalid without a user' do
      membership = build(:team_membership, user: nil, team: team)
      expect(membership).not_to be_valid
      expect(membership.errors[:user]).to include("must exist")
    end

    it 'is invalid without a team' do
      membership = build(:team_membership, team: nil, user: user)
      expect(membership).not_to be_valid
      expect(membership.errors[:team]).to include("must exist")
    end

    describe 'uniqueness validation' do
      it 'validates uniqueness of user_id scoped to team_id' do
        # Create the first membership
        create(:team_membership, user: user, team: team)
        
        # Try to create a duplicate
        duplicate = build(:team_membership, user: user, team: team)
        
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:user_id]).to include('has already been taken')
      end

      it 'allows same user in different teams' do
        membership1 = create(:team_membership, user: user, team: team)
        membership2 = build(:team_membership, user: user, team: other_team)
        
        expect(membership2).to be_valid
        expect(membership2.save).to be true
      end

      it 'allows different users in same team' do
        membership1 = create(:team_membership, user: user, team: team)
        membership2 = build(:team_membership, user: other_user, team: team)
        
        expect(membership2).to be_valid
        expect(membership2.save).to be true
      end
    end
  end

  describe 'role attribute' do
    it 'stores role information' do
      membership = create(:team_membership, user: user, team: team, role: 'admin')
      expect(membership.role).to eq('admin')
    end

    it 'defaults to member role via factory' do
      membership = create(:team_membership, user: user, team: team)
      expect(membership.role).to eq('member')
    end

    it 'allows different role values' do
      roles = ['member', 'admin', 'owner', 'contributor']
      
      roles.each do |role|
        membership = build(:team_membership, user: create(:user), team: team, role: role)
        expect(membership).to be_valid
        expect(membership.save).to be true
        expect(membership.reload.role).to eq(role)
      end
    end
  end

  describe 'database constraints and relationships' do
    it 'maintains referential integrity with user deletion' do
      membership = create(:team_membership, user: user, team: team)
      membership_id = membership.id
      
      # Should not be able to delete user if memberships exist
      # depending on foreign key constraints
      expect(membership.user).to eq(user)
      expect(membership.team).to eq(team)
    end

    it 'maintains referential integrity with team deletion' do
      membership = create(:team_membership, user: user, team: team)
      membership_id = membership.id
      
      # When team is destroyed, memberships should be destroyed too (dependent: :destroy)
      expect {
        team.destroy!
      }.to change { TeamMembership.count }.by(-1)
      
      expect(TeamMembership.find_by(id: membership_id)).to be_nil
    end
  end

  describe 'real-world scenarios' do
    it 'supports team membership workflow' do
      # Create a team membership
      membership = TeamMembership.new(user: user, team: team, role: 'member')
      expect(membership).to be_valid
      expect(membership.save).to be true
      
      # Verify the relationship works both ways
      expect(user.team_memberships).to include(membership)
      expect(user.teams).to include(team)
      expect(team.team_memberships).to include(membership)
      expect(team.users).to include(user)
    end

    it 'handles team member promotion/demotion' do
      membership = create(:team_membership, user: user, team: team, role: 'member')
      
      # Promote to admin
      membership.update!(role: 'admin')
      expect(membership.reload.role).to eq('admin')
      
      # Demote back to member
      membership.update!(role: 'member')
      expect(membership.reload.role).to eq('member')
    end

    it 'supports multiple users in a team' do
      user1 = create(:user)
      user2 = create(:user)
      user3 = create(:user)
      
      memberships = [
        create(:team_membership, user: user1, team: team, role: 'owner'),
        create(:team_membership, user: user2, team: team, role: 'admin'),
        create(:team_membership, user: user3, team: team, role: 'member')
      ]
      
      expect(team.team_memberships.count).to eq(3)
      expect(team.users.count).to eq(3)
      expect(team.users).to include(user1, user2, user3)
      
      # Check roles
      expect(team.team_memberships.find_by(user: user1).role).to eq('owner')
      expect(team.team_memberships.find_by(user: user2).role).to eq('admin')
      expect(team.team_memberships.find_by(user: user3).role).to eq('member')
    end

    it 'supports user belonging to multiple teams' do
      team1 = create(:team, name: 'Development Team')
      team2 = create(:team, name: 'Marketing Team')
      team3 = create(:team, name: 'Sales Team')
      
      memberships = [
        create(:team_membership, user: user, team: team1, role: 'admin'),
        create(:team_membership, user: user, team: team2, role: 'member'),
        create(:team_membership, user: user, team: team3, role: 'contributor')
      ]
      
      expect(user.team_memberships.count).to eq(3)
      expect(user.teams.count).to eq(3)
      expect(user.teams).to include(team1, team2, team3)
      
      # Check roles across teams
      expect(user.team_memberships.find_by(team: team1).role).to eq('admin')
      expect(user.team_memberships.find_by(team: team2).role).to eq('member')
      expect(user.team_memberships.find_by(team: team3).role).to eq('contributor')
    end

    it 'handles team member removal' do
      membership = create(:team_membership, user: user, team: team)
      
      # Remove member from team
      expect {
        membership.destroy!
      }.to change { team.team_memberships.count }.by(-1)
        .and change { user.team_memberships.count }.by(-1)
      
      expect(team.users).not_to include(user)
      expect(user.teams).not_to include(team)
    end

    it 'maintains data integrity during concurrent operations' do
      # Simulate concurrent team membership creation
      membership1 = build(:team_membership, user: user, team: team)
      membership2 = build(:team_membership, user: user, team: team)
      
      expect(membership1.save).to be true
      expect(membership2.save).to be false # Should fail due to uniqueness constraint
      
      expect(membership2.errors[:user_id]).to include('has already been taken')
    end
  end

  describe 'edge cases' do
    it 'handles nil role gracefully' do
      membership = build(:team_membership, user: user, team: team, role: nil)
      expect(membership).to be_valid
      expect(membership.save).to be true
      expect(membership.role).to be_nil
    end

    it 'handles empty string role' do
      membership = build(:team_membership, user: user, team: team, role: '')
      expect(membership).to be_valid
      expect(membership.save).to be true
      expect(membership.role).to eq('')
    end

    it 'handles long role names' do
      long_role = 'a' * 255
      membership = build(:team_membership, user: user, team: team, role: long_role)
      expect(membership).to be_valid
      expect(membership.save).to be true
      expect(membership.role).to eq(long_role)
    end

    it 'prevents duplicate membership creation via database constraints' do
      create(:team_membership, user: user, team: team)
      
      # Try to create duplicate at database level
      expect {
        TeamMembership.create!(user: user, team: team)
      }.to raise_error(ActiveRecord::RecordInvalid)
    end
  end

  describe 'factory and test setup' do
    it 'creates valid team membership with factory' do
      membership = create(:team_membership)
      expect(membership).to be_persisted
      expect(membership).to be_valid
      expect(membership.user).to be_present
      expect(membership.team).to be_present
      expect(membership.role).to eq('member')
    end

    it 'creates team membership with custom attributes' do
      membership = create(:team_membership, user: user, team: team, role: 'admin')
      expect(membership.user).to eq(user)
      expect(membership.team).to eq(team)
      expect(membership.role).to eq('admin')
    end

    it 'supports bulk creation with uniqueness constraints' do
      users = create_list(:user, 3)
      
      memberships = users.map do |u|
        create(:team_membership, user: u, team: team, role: 'member')
      end
      
      expect(memberships.size).to eq(3)
      expect(memberships.all?(&:persisted?)).to be true
      expect(team.team_memberships.count).to eq(3)
    end
  end

  describe 'query patterns' do
    let!(:admin_membership) { create(:team_membership, user: user, team: team, role: 'admin') }
    let!(:member_membership) { create(:team_membership, user: other_user, team: team, role: 'member') }
    
    it 'supports role-based queries' do
      admin_memberships = TeamMembership.where(role: 'admin')
      member_memberships = TeamMembership.where(role: 'member')
      
      expect(admin_memberships).to include(admin_membership)
      expect(admin_memberships).not_to include(member_membership)
      
      expect(member_memberships).to include(member_membership)
      expect(member_memberships).not_to include(admin_membership)
    end

    it 'supports team-based queries' do
      other_team_membership = create(:team_membership, user: create(:user), team: other_team)
      
      team_memberships = TeamMembership.where(team: team)
      
      expect(team_memberships).to include(admin_membership, member_membership)
      expect(team_memberships).not_to include(other_team_membership)
    end

    it 'supports user-based queries' do
      other_user_membership = create(:team_membership, user: other_user, team: other_team)
      
      user_memberships = TeamMembership.where(user: user)
      other_user_memberships = TeamMembership.where(user: other_user)
      
      expect(user_memberships).to include(admin_membership)
      expect(user_memberships).not_to include(member_membership, other_user_membership)
      
      expect(other_user_memberships).to include(member_membership, other_user_membership)
      expect(other_user_memberships).not_to include(admin_membership)
    end
  end
end