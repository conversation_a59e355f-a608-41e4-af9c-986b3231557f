require 'rails_helper'

RSpec.describe Link, 'custom domain support' do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user, short_code: 'abc123') }
  let(:custom_domain) { create(:custom_domain, :verified, user: user, domain: 'mydomain.com') }

  describe 'associations' do
    it { is_expected.to belong_to(:custom_domain).optional }
  end

  describe '#short_url' do
    context 'without custom domain' do
      it 'returns the default URL' do
        expect(link.short_url).to include('/abc123')
        expect(link.short_url).not_to include('mydomain.com')
      end
    end

    context 'with verified custom domain' do
      before { link.update(custom_domain: custom_domain) }

      it 'returns URL with custom domain' do
        expect(link.short_url).to eq('https://mydomain.com/abc123')
      end
    end

    context 'with unverified custom domain' do
      let(:unverified_domain) { create(:custom_domain, user: user, domain: 'unverified.com', verified: false) }

      before { link.update(custom_domain: unverified_domain) }

      it 'returns the default URL' do
        expect(link.short_url).to include('/abc123')
        expect(link.short_url).not_to include('unverified.com')
      end
    end

    context 'with request object' do
      let(:request) { double('request', base_url: 'http://test.host') }

      it 'uses request base URL for default domain' do
        expect(link.short_url(request)).to eq('http://test.host/abc123')
      end

      it 'ignores request when custom domain is set' do
        link.update(custom_domain: custom_domain)
        expect(link.short_url(request)).to eq('https://mydomain.com/abc123')
      end
    end
  end
end
