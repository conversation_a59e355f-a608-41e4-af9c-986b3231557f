require 'rails_helper'

RSpec.describe KnowledgeCategory, type: :model do
  subject(:category) { build(:knowledge_category) }

  describe 'associations' do
    it { should have_many(:knowledge_articles).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:name) }
    it { should validate_presence_of(:slug) }
    it { should validate_uniqueness_of(:slug) }
    it { should validate_numericality_of(:position).only_integer.is_greater_than_or_equal_to(0) }
  end

  describe 'callbacks' do
    describe '#generate_slug' do
      it 'generates slug from name when slug is blank' do
        category = build(:knowledge_category, name: 'Getting Started', slug: nil)
        category.valid?
        expect(category.slug).to eq('getting-started')
      end

      it 'does not override existing slug' do
        category = build(:knowledge_category, name: 'Getting Started', slug: 'custom-slug')
        category.valid?
        expect(category.slug).to eq('custom-slug')
      end

      it 'does not generate slug when name is blank' do
        category = build(:knowledge_category, name: nil, slug: nil)
        category.valid?
        expect(category.slug).to be_nil
      end
    end
  end

  describe 'scopes' do
    let!(:active_category) { create(:knowledge_category, active: true, position: 2) }
    let!(:inactive_category) { create(:knowledge_category, active: false, position: 1) }
    let!(:another_active) { create(:knowledge_category, active: true, position: 1) }

    describe '.active' do
      it 'returns only active categories' do
        expect(KnowledgeCategory.active).to contain_exactly(active_category, another_active)
      end
    end

    describe '.ordered' do
      it 'orders by position then created_at' do
        expect(KnowledgeCategory.ordered).to eq([ another_active, active_category, inactive_category ])
      end
    end
  end

  describe 'factory' do
    it 'creates a valid category' do
      expect(category).to be_valid
    end

    it 'can create with articles' do
      category = create(:knowledge_category, :with_articles)
      expect(category.knowledge_articles).not_to be_empty
    end
  end
end
