require 'rails_helper'

RSpec.describe ApplicationCable::Connection, type: :channel do
  let(:user) { create(:user) }

  describe '#connect' do
    context 'with valid user_id parameter' do
      it 'successfully connects when user_id is provided' do
        connect "/cable?user_id=#{user.id}"
        expect(connection.current_user).to eq(user)
      end

      it 'rejects connection when user_id does not exist' do
        expect { connect "/cable?user_id=999999" }.to have_rejected_connection
      end
    end

    context 'without authentication' do
      it 'rejects connection when no authentication provided' do
        expect { connect '/cable' }.to have_rejected_connection
      end
    end

    context 'in development environment' do
      before do
        allow(Rails.env).to receive(:development?).and_return(true)
      end

      it 'rejects connection and logs in development' do
        expect { connect '/cable' }.to have_rejected_connection
      end
    end
  end

  describe 'connection identification' do
    it 'identifies connection by current_user' do
      connect "/cable?user_id=#{user.id}"
      expect(connection.current_user).to eq(user)
    end
  end
end

# Test authentication logic separately to cover edge cases
RSpec.describe 'ApplicationCable::Connection authentication logic', type: :model do
  let(:user) { create(:user) }
  let(:connection_instance) { ApplicationCable::Connection.allocate }
  
  before do
    # Stub the Rails environment check
    allow(Rails.env).to receive(:development?).and_return(false)
    allow(connection_instance).to receive(:reject_unauthorized_connection).and_raise(ActionCable::Connection::Authorization::UnauthorizedError)
  end

  describe '#find_verified_user' do
    context 'with user_id parameter' do
      it 'finds user by request parameter' do
        request_double = double('request', params: { user_id: user.id.to_s })
        allow(connection_instance).to receive(:request).and_return(request_double)
        
        result = connection_instance.send(:find_verified_user)
        expect(result).to eq(user)
      end

      it 'handles string user_id parameter' do
        request_double = double('request', params: { user_id: user.id.to_s })
        allow(connection_instance).to receive(:request).and_return(request_double)
        
        result = connection_instance.send(:find_verified_user)
        expect(result).to eq(user)
      end

      it 'rejects invalid user_id' do
        request_double = double('request', params: { user_id: '999999' })
        allow(connection_instance).to receive(:request).and_return(request_double)
        
        # Mock cookies to return nil so it falls through to rejection
        cookies_double = double('cookies')
        encrypted_double = double('encrypted')
        allow(connection_instance).to receive(:cookies).and_return(cookies_double)
        allow(cookies_double).to receive(:encrypted).and_return(encrypted_double)
        allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(nil)
        
        expect {
          connection_instance.send(:find_verified_user)
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end

      it 'rejects non-numeric user_id' do
        request_double = double('request', params: { user_id: 'invalid' })
        allow(connection_instance).to receive(:request).and_return(request_double)
        
        # Mock cookies to return nil so it falls through to rejection
        cookies_double = double('cookies')
        encrypted_double = double('encrypted')
        allow(connection_instance).to receive(:cookies).and_return(cookies_double)
        allow(cookies_double).to receive(:encrypted).and_return(encrypted_double)
        allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(nil)
        
        expect {
          connection_instance.send(:find_verified_user)
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end

    context 'with session authentication' do
      let(:request_double) { double('request', params: {}) }
      let(:cookies_double) { double('cookies') }
      let(:encrypted_double) { double('encrypted') }

      before do
        allow(connection_instance).to receive(:request).and_return(request_double)
        allow(connection_instance).to receive(:cookies).and_return(cookies_double)
        allow(cookies_double).to receive(:encrypted).and_return(encrypted_double)
      end

      context 'with warden session structure' do
        it 'successfully authenticates with valid warden session' do
          session_data = {
            'warden.user.user.key' => [[user.id], 'session_token']
          }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          result = connection_instance.send(:find_verified_user)
          expect(result).to eq(user)
        end

        it 'rejects malformed warden session (empty user array)' do
          session_data = {
            'warden.user.user.key' => [[], 'session_token']
          }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end

        it 'rejects malformed warden session (nil user data)' do
          session_data = {
            'warden.user.user.key' => [nil, 'session_token']
          }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end

        it 'rejects invalid user_id in warden session' do
          session_data = {
            'warden.user.user.key' => [[999999], 'session_token']
          }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end
      end

      context 'with direct user_id in session' do
        it 'successfully authenticates with direct user_id' do
          session_data = { 'user_id' => user.id }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          result = connection_instance.send(:find_verified_user)
          expect(result).to eq(user)
        end

        it 'handles string user_id in session' do
          session_data = { 'user_id' => user.id.to_s }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          result = connection_instance.send(:find_verified_user)
          expect(result).to eq(user)
        end

        it 'rejects invalid user_id in session' do
          session_data = { 'user_id' => 999999 }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end
      end

      context 'with devise user_id in session' do
        it 'successfully authenticates with devise user_id' do
          session_data = { 'devise.user_id' => user.id }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          result = connection_instance.send(:find_verified_user)
          expect(result).to eq(user)
        end

        it 'rejects invalid devise user_id in session' do
          session_data = { 'devise.user_id' => 999999 }
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(session_data)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end
      end

      context 'session key configuration' do
        it 'uses default session key when not configured' do
          allow(Rails.application.config).to receive(:session_options).and_return({})
          session_data = { 'user_id' => user.id }
          # When no config, it defaults to "_linkmaster_session" as fallback
          allow(encrypted_double).to receive(:[]).with('_linkmaster_session').and_return(session_data)
          
          result = connection_instance.send(:find_verified_user)
          expect(result).to eq(user)
        end

        it 'uses configured session key when available' do
          allow(Rails.application.config).to receive(:session_options).and_return({ key: '_custom_session' })
          session_data = { 'user_id' => user.id }
          allow(encrypted_double).to receive(:[]).with('_custom_session').and_return(session_data)
          
          result = connection_instance.send(:find_verified_user)
          expect(result).to eq(user)
        end
      end

      context 'edge cases' do
        it 'rejects when session data is nil' do
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return(nil)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end

        it 'rejects when session data is empty hash' do
          allow(encrypted_double).to receive(:[]).with('_linklysis_session').and_return({})
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end

        it 'rejects when cookies are not available' do
          allow(connection_instance).to receive(:cookies).and_return(nil)
          
          request_double = double('request', params: {})
          allow(connection_instance).to receive(:request).and_return(request_double)
          
          expect {
            connection_instance.send(:find_verified_user)
          }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
        end
      end
    end

    context 'development environment logging' do
      before do
        allow(Rails.env).to receive(:development?).and_return(true)
        allow(connection_instance).to receive(:logger).and_return(double('logger', info: nil))
      end

      it 'logs rejection message in development' do
        request_double = double('request', params: {})
        allow(connection_instance).to receive(:request).and_return(request_double)
        allow(connection_instance).to receive(:cookies).and_return(nil)
        
        expect(connection_instance.logger).to receive(:info).with('ActionCable: No authenticated user found, rejecting connection')
        
        expect {
          connection_instance.send(:find_verified_user)
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end
  end
end