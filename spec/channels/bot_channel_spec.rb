require 'rails_helper'

RSpec.describe BotChannel, type: :channel do
  let(:user) { create(:user) }
  let(:subscription) { subscribe(as: user) }

  before do
    stub_connection(current_user: user)
  end

  describe '#subscribed' do
    it 'streams from user-specific channel' do
      subscription
      expect(subscription).to have_stream_from("bot_#{user.id}")
    end

    it 'sends welcome message on subscription' do
      expect { subscription }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('welcome')
        expect(data[:message]).to eq('Connected to Linklysis Assistant')
        expect(data[:timestamp]).to be_present
      }
    end
  end

  describe '#unsubscribed' do
    it 'stops all streams' do
      subscription
      unsubscribe
      expect(subscription).not_to have_streams
    end
  end

  describe '#send_message' do
    before { subscription }

    context 'with valid message' do
      let(:bot_service) { instance_double(BotService) }
      let(:successful_result) do
        ServiceResult.success(
          data: {
            response: 'Hello! How can I help you today?',
            intent: 'greeting',
            suggestions: [ 'Create a link', 'View analytics' ],
            context: { mood: 'helpful' }
          }
        )
      end

      before do
        allow(BotService).to receive(:new).and_return(bot_service)
        allow(bot_service).to receive(:process_message).and_return(successful_result)
      end

      it 'processes message through BotService' do
        perform(:send_message, { 'message' => 'Hello' })
        expect(bot_service).to have_received(:process_message).with('Hello')
      end

      it 'broadcasts successful response' do
        expect {
          perform(:send_message, { 'message' => 'Hello', 'context' => { 'previous_intent' => 'unknown' } })
        }.to have_broadcasted_to("bot_#{user.id}").with { |data|
          expect(data[:type]).to eq('message')
          expect(data[:user_message]).to eq('Hello')
          expect(data[:bot_response]).to eq('Hello! How can I help you today?')
          expect(data[:intent]).to eq('greeting')
          expect(data[:suggestions]).to eq([ 'Create a link', 'View analytics' ])
          expect(data[:context]).to eq({ 'mood' => 'helpful' })
          expect(data[:timestamp]).to be_present
        }
      end
    end

    context 'with blank message' do
      it 'does not process empty message' do
        expect(BotService).not_to receive(:new)
        perform(:send_message, { 'message' => '' })
      end

      it 'does not process whitespace-only message' do
        expect(BotService).not_to receive(:new)
        perform(:send_message, { 'message' => '   ' })
      end
    end

    context 'when bot service fails' do
      let(:bot_service) { instance_double(BotService) }
      let(:failed_result) { ServiceResult.failure(errors: [ 'Unable to process your request' ]) }

      before do
        allow(BotService).to receive(:new).and_return(bot_service)
        allow(bot_service).to receive(:process_message).and_return(failed_result)
      end

      it 'broadcasts error message' do
        expect {
          perform(:send_message, { 'message' => 'Help' })
        }.to have_broadcasted_to("bot_#{user.id}").with { |data|
          expect(data[:type]).to eq('error')
          expect(data[:error]).to eq('Unable to process your request')
          expect(data[:timestamp]).to be_present
        }
      end
    end
  end

  describe '#typing_indicator' do
    before { subscription }

    it 'broadcasts typing indicator when true' do
      expect {
        perform(:typing_indicator, { 'is_typing' => true })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('typing')
        expect(data[:is_typing]).to be true
        expect(data[:timestamp]).to be_present
      }
    end

    it 'broadcasts typing indicator when false' do
      expect {
        perform(:typing_indicator, { 'is_typing' => false })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('typing')
        expect(data[:is_typing]).to be false
        expect(data[:timestamp]).to be_present
      }
    end
  end

  describe '#request_suggestions' do
    before { subscription }

    context 'for new user' do
      before do
        allow(user).to receive_message_chain(:links, :count).and_return(0)
        allow(user).to receive_message_chain(:links, :active, :count).and_return(0)
        allow(user).to receive_message_chain(:custom_domains, :any?).and_return(false)
        allow(user).to receive_message_chain(:teams, :any?).and_return(false)
        allow(user).to receive_message_chain(:links, :recent, :limit, :exists?).and_return(false)
      end

      it 'broadcasts onboarding suggestions' do
        expect {
          perform(:request_suggestions, { 'intent' => 'onboarding' })
        }.to have_broadcasted_to("bot_#{user.id}").with { |data|
          expect(data[:type]).to eq('suggestions')
          expect(data[:suggestions]).to include('Create my first link')
          expect(data[:suggestions]).to include('What is link shortening?')
          expect(data[:suggestions]).to include('How do I get started?')
          expect(data[:context][:total_links]).to eq(0)
          expect(data[:timestamp]).to be_present
        }
      end
    end

    context 'for experienced user' do
      before do
        allow(user).to receive_message_chain(:links, :count).and_return(50)
        allow(user).to receive_message_chain(:links, :active, :count).and_return(45)
        allow(user).to receive_message_chain(:custom_domains, :any?).and_return(true)
        allow(user).to receive_message_chain(:teams, :any?).and_return(true)
        allow(user).to receive_message_chain(:links, :recent, :limit, :exists?).and_return(true)
      end

      it 'broadcasts advanced suggestions' do
        expect {
          perform(:request_suggestions, { 'intent' => 'analytics' })
        }.to have_broadcasted_to("bot_#{user.id}").with { |data|
          expect(data[:type]).to eq('suggestions')
          expect(data[:suggestions]).to include('View my analytics')
          expect(data[:suggestions]).to include('How do I customize short codes?')
          expect(data[:suggestions]).to include('Export my data')
          expect(data[:context][:total_links]).to eq(50)
          expect(data[:context][:has_custom_domains]).to be true
          expect(data[:timestamp]).to be_present
        }
      end
    end

    context 'for free plan user' do
      before do
        allow(user).to receive(:subscription_plan).and_return('free')
        allow(user).to receive_message_chain(:links, :count).and_return(5)
        allow(user).to receive_message_chain(:links, :active, :count).and_return(5)
        allow(user).to receive_message_chain(:custom_domains, :any?).and_return(false)
        allow(user).to receive_message_chain(:teams, :any?).and_return(false)
        allow(user).to receive_message_chain(:links, :recent, :limit, :exists?).and_return(true)
      end

      it 'includes upgrade suggestions' do
        expect {
          perform(:request_suggestions, {})
        }.to have_broadcasted_to("bot_#{user.id}").with { |data|
          expect(data[:suggestions]).to include('What are the subscription benefits?')
          expect(data[:suggestions]).to include('How do I upgrade my plan?')
        }
      end
    end
  end

  describe '#generate_suggestions (private method)' do
    before { subscription }

    it 'returns maximum 6 unique suggestions' do
      context = {
        total_links: 0,
        subscription_plan: 'free',
        has_custom_domains: false,
        is_team_member: false,
        recent_activity: false
      }

      suggestions = subscription.send(:generate_suggestions, context)

      expect(suggestions.length).to be <= 6
      expect(suggestions.uniq).to eq(suggestions)
    end
  end
end
