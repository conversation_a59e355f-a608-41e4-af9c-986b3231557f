require 'rails_helper'

RSpec.describe BotChannel, type: :channel do
  let(:user) { create(:user, first_name: 'Test', last_name: 'User') }
  let!(:link) { create(:link, user: user) }

  before do
    stub_connection(current_user: user)
  end

  describe 'full integration test' do
    it 'handles complete bot conversation flow' do
      # Test connection and welcome message on subscription
      expect {
        subscribe
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('welcome')
        expect(data[:message]).to eq('Connected to Linklysis Assistant')
        expect(data[:timestamp]).to be_present
      }

      # Test that subscription creates the stream
      expect(subscription).to have_stream_from("bot_#{user.id}")

      # Test greeting message
      expect {
        perform(:send_message, { 'message' => 'Hello' })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('message')
        expect(data[:user_message]).to eq('Hello')
        expect(data[:intent]).to eq('greeting')
        expect(data[:bot_response]).to be_present
      }

      # Test help request
      expect {
        perform(:send_message, { 'message' => 'How do I create a link?' })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('message')
        expect(data[:intent]).to eq('help_request')
        expect(data[:bot_response]).to be_present
      }

      # Test typing indicator
      expect {
        perform(:typing_indicator, { 'is_typing' => true })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('typing')
        expect(data[:is_typing]).to be true
      }
    end

    it 'handles different user contexts correctly' do
      subscribe

      # Test for user with links (user has 1 link from let! above)
      expect {
        perform(:send_message, { 'message' => 'Hello' })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('message')
        expect(data[:bot_response]).to include('1 link')
        expect(data[:intent]).to eq('greeting')
      }
    end

    it 'handles error scenarios gracefully' do
      subscribe

      # Test with empty message - should not process
      perform(:send_message, { 'message' => '' })

      # Test with whitespace-only message - should not process
      perform(:send_message, { 'message' => '   ' })

      # Test with very long message - should still work
      long_message = 'a' * 1000
      expect {
        perform(:send_message, { 'message' => long_message })
      }.to have_broadcasted_to("bot_#{user.id}").with { |data|
        expect(data[:type]).to eq('message')
        expect(data[:bot_response]).to be_present
      }
    end
  end
end
