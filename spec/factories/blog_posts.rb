FactoryBot.define do
  factory :blog_post do
    sequence(:title) { |n| "Blog Post Title #{n}" }
    sequence(:slug) { |n| "blog-post-title-#{n}" }
    content { "This is the content of the blog post. It contains multiple sentences and paragraphs to simulate real blog content." }
    category { "announcements" }
    author { "John Doe" }
    author_title { "Content Manager" }
    author_avatar { "https://example.com/avatar.jpg" }
    published_at { 1.day.ago }
    reading_time { "5 min read" }
    featured { false }
    tags { ["rails", "development", "tutorial"] }
    
    trait :published do
      published_at { 1.day.ago }
    end
    
    trait :unpublished do
      published_at { nil }
    end
    
    trait :future do
      published_at { 1.day.from_now }
    end
    
    trait :featured do
      featured { true }
    end
    
    trait :with_media do
      media_type { "video" }
      media_url { "https://example.com/video.mp4" }
      video_duration { "10:30" }
    end
    
    trait :podcast do
      media_type { "podcast" }
      episode_number { 42 }
      video_duration { "45:20" }
    end
  end
end