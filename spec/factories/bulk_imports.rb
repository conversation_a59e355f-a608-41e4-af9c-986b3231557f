FactoryBot.define do
  factory :bulk_import do
    association :user
    association :team, factory: :team, strategy: :build

    status { 'pending' }
    file_name { 'test_import.csv' }
    total_rows { 10 }
    processed_rows { 0 }
    successful_rows { 0 }
    failed_rows { 0 }
    error_details { {} }

    trait :processing do
      status { 'processing' }
      started_at { 5.minutes.ago }
      processed_rows { 5 }
      successful_rows { 4 }
      failed_rows { 1 }
    end

    trait :completed do
      status { 'completed' }
      started_at { 10.minutes.ago }
      completed_at { 2.minutes.ago }
      processed_rows { 10 }
      successful_rows { 8 }
      failed_rows { 2 }
    end

    trait :failed do
      status { 'failed' }
      started_at { 5.minutes.ago }
      completed_at { 1.minute.ago }
      error_details { { general_error: 'File processing failed' } }
    end

    trait :with_errors do
      error_details do
        {
          rows: {
            '1' => 'Invalid URL format',
            '3' => 'Missing required field',
            '5' => 'URL already exists'
          }
        }
      end
      failed_rows { 3 }
    end
  end
end
