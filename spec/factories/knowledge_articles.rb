FactoryBot.define do
  factory :knowledge_article do
    knowledge_category
    sequence(:title) { |n| "Article #{n}" }
    sequence(:slug) { |n| "article-#{n}" }
    content { "This is the content for #{title}. It contains helpful information." }
    keywords { "help, guide, tutorial" }
    helpful_count { 0 }
    not_helpful_count { 0 }
    view_count { 0 }
    position { 1 }
    featured { false }
    published { false }
    published_at { nil }

    trait :published do
      published { true }
      published_at { 1.hour.ago }
    end

    trait :featured do
      featured { true }
    end

    trait :with_feedback do
      helpful_count { rand(1..20) }
      not_helpful_count { rand(1..5) }
      view_count { rand(10..100) }
    end

    trait :scheduled do
      published { true }
      published_at { 1.hour.from_now }
    end
  end
end
