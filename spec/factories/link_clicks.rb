FactoryBot.define do
  factory :link_click do
    link
    attribution_data { {} }
    tracking_data { {} }
    clicked_at { Time.current }

    trait :with_attribution do
      attribution_data do
        {
          utm_source: 'google',
          utm_medium: 'cpc',
          utm_campaign: 'summer-sale',
          referrer: 'https://google.com'
        }
      end
    end

    trait :with_tracking do
      tracking_data do
        {
          ip_address: Faker::Internet.ip_v4_address,
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
          country_code: 'US',
          city: 'New York',
          device_type: 'desktop',
          browser: 'Chrome',
          os: 'MacOS'
        }
      end
    end
  end
end
