FactoryBot.define do
  factory :user do
    email { Faker::Internet.unique.email }
    password { 'password123' }

    # These will be added later when we implement full User model
    # role { :user }
    # subscription_plan { :free }

    # trait :professional do
    #   subscription_plan { :professional }
    # end

    # trait :business do
    #   subscription_plan { :business }
    # end

    # trait :enterprise do
    #   subscription_plan { :enterprise }
    # end

    # trait :admin do
    #   role { :admin }
    # end
  end
end
