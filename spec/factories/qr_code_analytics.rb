FactoryBot.define do
  factory :qr_code_analytic do
    link
    format { 'svg' }
    generated_at { Time.current }
    ip_address { '127.0.0.1' }
    user_agent { 'Mozilla/5.0 (Test Browser)' }
    options { { module_size: 6, color: '000000', background_color: 'ffffff' } }

    trait :svg do
      format { 'svg' }
    end

    trait :png do
      format { 'png' }
      options { { module_size: 6, png_size: 400, color: '000000' } }
    end

    trait :pdf do
      format { 'pdf' }
      options { { module_size: 6, png_size: 400, color: '000000' } }
    end

    trait :recent do
      generated_at { 1.hour.ago }
    end

    trait :old do
      generated_at { 1.month.ago }
    end
  end
end
