FactoryBot.define do
  factory :knowledge_category do
    sequence(:name) { |n| "Category #{n}" }
    sequence(:slug) { |n| "category-#{n}" }
    description { "Description for #{name}" }
    icon { "📚" }
    position { 1 }
    active { true }

    trait :with_articles do
      after(:create) do |category|
        create_list(:knowledge_article, 3, knowledge_category: category)
      end
    end

    trait :inactive do
      active { false }
    end
  end
end
