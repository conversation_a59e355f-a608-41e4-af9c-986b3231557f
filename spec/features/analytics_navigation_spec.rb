require 'rails_helper'

RSpec.feature "Analytics Navigation", type: :feature do
  let(:user) { create(:user) }
  let!(:link) { create(:link, user: user) }

  before do
    sign_in user
  end

  scenario "User can navigate from analytics show to link details" do
    visit analytic_path(link)

    expect(page).to have_link("View Details")

    click_link "View Details"

    expect(current_path).to eq(link_path(link))
  end

  scenario "User can navigate from analytics index to analytics show" do
    visit analytics_path

    # Should have links to individual analytics pages
    expect(page).to have_content("Analytics")

    # If there are top links displayed, they should be clickable
    if page.has_content?(link.short_code)
      click_link link.short_code
      expect(current_path).to eq(analytic_path(link))
    end
  end

  scenario "User can navigate back from analytics show to analytics index" do
    visit analytic_path(link)

    click_link "Back"

    expect(current_path).to eq(analytics_path)
  end
end
