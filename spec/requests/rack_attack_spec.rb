# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "Rack::Attack", type: :request do
  before do
    # Clear the cache before each test
    Rails.cache.clear

    # Enable Rack::Attack for tests (it should be enabled by default from the initializer)
    Rack::Attack.enabled = true
  end

  after do
    # Clean up after tests
    Rails.cache.clear
  end

  describe "throttling" do
    context "general requests by IP" do
      it "allows requests under the limit" do
        get root_path
        expect(response).to have_http_status(:ok)
        expect(response.headers).not_to include('Retry-After')
      end

      it "throttles requests over the limit" do
        # Make 300 requests (the limit)
        300.times do |i|
          get root_path
          expect(response).to have_http_status(:ok), "Request #{i + 1} should succeed"
        end

        # The 301st request should be throttled
        get root_path
        expect(response).to have_http_status(:too_many_requests)
        expect(response.headers).to include('Retry-After')
        expect(response.headers).to include('X-RateLimit-Limit')
        expect(response.headers).to include('X-RateLimit-Remaining')
        expect(response.headers).to include('X-RateLimit-Reset')
      end
    end

    context "login attempts by IP" do
      it "allows login attempts under the limit" do
        4.times do
          post user_session_path, params: { user: { email: '<EMAIL>', password: 'wrongpassword' } }
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      it "throttles login attempts over the limit" do
        # Make 5 login attempts (the limit)
        5.times do
          post user_session_path, params: { user: { email: '<EMAIL>', password: 'wrongpassword' } }
          expect(response).to have_http_status(:unprocessable_entity)
        end

        # The 6th attempt should be throttled
        post user_session_path, params: { user: { email: '<EMAIL>', password: 'wrongpassword' } }
        expect(response).to have_http_status(:too_many_requests)
      end
    end

    context "registration attempts by IP" do
      it "allows registration attempts under the limit" do
        2.times do |i|
          post user_registration_path, params: {
            user: {
              email: "test#{i}@example.com",
              password: 'password123',
              password_confirmation: 'password123'
            }
          }
          # Registration might succeed or fail due to validation, but shouldn't be throttled
          expect(response).not_to have_http_status(:too_many_requests)
        end
      end

      it "throttles registration attempts over the limit" do
        # Make 3 registration attempts (the limit)
        3.times do |i|
          post user_registration_path, params: {
            user: {
              email: "test#{i}@example.com",
              password: 'password123',
              password_confirmation: 'password123'
            }
          }
        end

        # The 4th attempt should be throttled
        post user_registration_path, params: {
          user: {
            email: '<EMAIL>',
            password: 'password123',
            password_confirmation: 'password123'
          }
        }
        expect(response).to have_http_status(:too_many_requests)
      end
    end

    context "API requests" do
      it "throttles API requests without token" do
        # Make 100 API requests without token (the limit for IP)
        100.times do
          get '/api/v1/links'
          expect(response).not_to have_http_status(:too_many_requests)
        end

        # The 101st request should be throttled
        get '/api/v1/links'
        expect(response).to have_http_status(:too_many_requests)
      end
    end
  end

  describe "blocklist" do
    context "suspicious user agents" do
      it "blocks known bad bot user agents" do
        get root_path, headers: { 'User-Agent' => 'BadBot/1.0 Scraper' }
        expect(response).to have_http_status(:forbidden)
      end

      it "allows legitimate bot user agents" do
        get root_path, headers: { 'User-Agent' => 'Googlebot/2.1' }
        expect(response).to have_http_status(:ok)
      end
    end

    context "suspicious requests" do
      it "blocks requests with very long paths" do
        long_path = "/" + "a" * 256
        get long_path
        expect(response).to have_http_status(:forbidden)
      end

      it "blocks requests with SQL injection attempts in query" do
        get root_path, params: { q: "'; DROP TABLE users; --" }
        expect(response).to have_http_status(:forbidden)
      end

      it "blocks requests with XSS attempts in query" do
        get root_path, params: { q: "<script>alert('xss')</script>" }
        expect(response).to have_http_status(:forbidden)
      end

      it "blocks requests with excessively long user agents" do
        long_user_agent = "A" * 501
        get root_path, headers: { 'User-Agent' => long_user_agent }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  describe "safelist" do
    it "allows requests from localhost" do
      # Simulate localhost IP
      allow_any_instance_of(ActionDispatch::Request).to receive(:ip).and_return('127.0.0.1')

      # Make requests beyond normal limits
      350.times do
        get root_path
        expect(response).to have_http_status(:ok)
      end
    end

    it "allows health check endpoints" do
      get '/up'
      expect(response).to have_http_status(:ok)
    end
  end

  describe "custom responses" do
    it "returns proper JSON response for throttled requests" do
      # Exhaust the limit
      300.times { get root_path }

      # Next request should be throttled
      get root_path

      expect(response).to have_http_status(:too_many_requests)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to include('error', 'message', 'retry_after')
      expect(json_response['error']).to eq('Rate limit exceeded')
    end

    it "returns proper JSON response for blocked requests" do
      get root_path, headers: { 'User-Agent' => 'BadBot/1.0 Scraper' }

      expect(response).to have_http_status(:forbidden)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to include('error', 'message')
      expect(json_response['error']).to eq('Forbidden')
    end
  end

  describe "logging" do
    it "logs throttled requests" do
      # Set up log capturing
      logs = []
      allow(Rails.logger).to receive(:warn) { |message| logs << message }

      # Exhaust the limit
      300.times { get root_path }

      # Trigger throttling
      get root_path

      # Check that warning was logged
      expect(logs).to include(
        match(/\[Rack::Attack\] Throttled request:.*requests\/ip/)
      )
    end

    it "logs blocked requests" do
      # Set up log capturing
      logs = []
      allow(Rails.logger).to receive(:warn) { |message| logs << message }

      # Trigger blocking
      get root_path, headers: { 'User-Agent' => 'BadBot/1.0 Scraper' }

      # Check that warning was logged
      expect(logs).to include(
        match(/\[Rack::Attack\] Blocked request:.*block suspicious requests/)
      )
    end
  end
end
