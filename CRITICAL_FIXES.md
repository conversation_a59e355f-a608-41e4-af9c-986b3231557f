# Critical Fixes Implementation Guide

This document provides step-by-step instructions for implementing the most critical fixes identified in the LinkMaster application audit.

## 🚨 Priority 1: Fix Broken Terms of Service and Privacy Policy Links

### Issue
Registration page has placeholder links for Terms of Service and Privacy Policy (`href="#"`).

### Location
`app/views/devise/registrations/new.html.erb` lines 119-121

### Fix Steps

1. **Create the pages controller method**:
```ruby
# app/controllers/pages_controller.rb
def terms_of_service
end

def privacy_policy
end
```

2. **Add routes**:
```ruby
# config/routes.rb
get '/terms', to: 'pages#terms_of_service', as: :terms_of_service
get '/privacy', to: 'pages#privacy_policy', as: :privacy_policy
```

3. **Create the view files**:
```erb
<!-- app/views/pages/terms_of_service.html.erb -->
<% content_for :title, "Terms of Service" %>
<div class="max-w-4xl mx-auto py-12 px-4">
  <h1 class="text-3xl font-bold mb-8">Terms of Service</h1>
  <!-- Add actual terms content -->
</div>
```

```erb
<!-- app/views/pages/privacy_policy.html.erb -->
<% content_for :title, "Privacy Policy" %>
<div class="max-w-4xl mx-auto py-12 px-4">
  <h1 class="text-3xl font-bold mb-8">Privacy Policy</h1>
  <!-- Add actual privacy policy content -->
</div>
```

4. **Update the registration view**:
```erb
<!-- app/views/devise/registrations/new.html.erb lines 119-121 -->
By creating an account, you agree to our
<%= link_to "Terms of Service", terms_of_service_path, class: "text-purple-300 hover:text-purple-200" %> and
<%= link_to "Privacy Policy", privacy_policy_path, class: "text-purple-300 hover:text-purple-200" %>
```