# frozen_string_literal: true

# Privacy and GDPR Compliance Configuration
Rails.application.configure do
  # Data retention period for analytics data
  # After this period, link click data will be automatically purged
  # Default: 13 months (1 year of data + 1 month buffer)
  config.data_retention_period = ENV.fetch("DATA_RETENTION_PERIOD", "13").to_i.months

  # Whether to notify administrators when data cleanup occurs
  config.notify_data_cleanup = ENV.fetch("NOTIFY_DATA_CLEANUP", "false").downcase == "true"

  # IP anonymization settings (always enabled for privacy)
  config.anonymize_ips = true

  # Respect Do Not Track headers
  config.respect_dnt = true

  # Maximum user agent length to store (prevent abuse)
  config.max_user_agent_length = 500

  # Enable/disable geolocation lookup
  # Even when enabled, only approximate location is stored (city/region level)
  config.enable_geolocation = ENV.fetch("ENABLE_GEOLOCATION", "true").downcase == "true"

  # Cookie consent settings
  config.require_cookie_consent = ENV.fetch("REQUIRE_COOKIE_CONSENT", "true").downcase == "true"

  # Analytics data minimal storage (only store essential data)
  config.minimal_analytics = ENV.fetch("MINIMAL_ANALYTICS", "false").downcase == "true"
end

# Schedule data retention cleanup job
# Run daily at 2 AM UTC to clean up old data
if defined?(SolidQueue) && Rails.env.production?
  # This would typically be configured in your deployment/cron system
  # Rails.application.config.after_initialize do
  #   DataRetentionCleanupJob.set(cron: '0 2 * * *').perform_later
  # end
end
