if defined?(Bullet)
  Bullet.enable = true

  # Enable bullet in development and test environments
  if Rails.env.development?
    Bullet.alert = true          # Enable JavaScript alert in browser
    Bullet.bullet_logger = true  # Log to bullet.log
    Bullet.console = true        # Enable console output
    Bullet.rails_logger = true   # Enable Rails logger output
    Bullet.add_footer = true     # Add footer with N+1 query warnings
  end

  if Rails.env.test?
    Bullet.raise = true          # Raise errors in tests to catch N+1 issues
    Bullet.bullet_logger = true  # Log to bullet.log for debugging
  end

  # Configure what bullet should detect
  Bullet.n_plus_one_query_enable     = true  # Detect N+1 queries
  Bullet.unused_eager_loading_enable = true  # Detect unused eager loading
  Bullet.counter_cache_enable        = true  # Detect missing counter cache

  # Safelist patterns to ignore false positives
  Bullet.add_safelist(
    type: :n_plus_one_query,
    class_name: "LinkClick",
    association: :link
  )

  Bullet.add_safelist(
    type: :n_plus_one_query,
    class_name: "<PERSON>",
    association: :user
  )

  # Skip bullet for specific controllers that handle bulk operations
  # Note: BulkImport may not need safelist if it doesn't have N+1 query issues
  # Bullet.add_safelist(
  #   type: :n_plus_one_query,
  #   class_name: "BulkImport",
  #   association: :links  # Add association if needed
  # )
end
