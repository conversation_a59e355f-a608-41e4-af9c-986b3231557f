# frozen_string_literal: true

# Rack::Attack configuration for LinkMaster
# Provides rate limiting and request filtering to prevent abuse

class Rack::Attack
  ### Configure Cache Store ###
  # Use Rails cache (Solid Cache for this application)
  Rack::Attack.cache.store = Rails.cache

  ### Throttle Policies ###

  # Throttle general requests by IP
  # Allow 300 requests per 5 minutes per IP
  throttle("requests/ip", limit: 300, period: 5.minutes) do |request|
    request.ip unless request.path.start_with?("/assets")
  end

  # Throttle login attempts by IP
  # Allow 5 login attempts per 20 seconds
  throttle("logins/ip", limit: 5, period: 20.seconds) do |request|
    if request.path == "/users/sign_in" && request.post?
      request.ip
    end
  end

  # Throttle login attempts by email
  # Allow 5 login attempts per 20 seconds per email
  throttle("logins/email", limit: 5, period: 20.seconds) do |request|
    if request.path == "/users/sign_in" && request.post?
      request.params.dig("user", "email").to_s.downcase.gsub(/\s+/, "")
    end
  end

  # Throttle registration attempts
  # Allow 3 registrations per hour per IP
  throttle("registration/ip", limit: 3, period: 1.hour) do |request|
    if request.path == "/users" && request.post?
      request.ip
    end
  end

  # Throttle password reset requests
  # Allow 5 password reset requests per hour per IP
  throttle("password_reset/ip", limit: 5, period: 1.hour) do |request|
    if request.path == "/users/password" && request.post?
      request.ip
    end
  end

  # Throttle link creation (authenticated users)
  # Allow 100 link creations per hour per user
  throttle("links/user", limit: 100, period: 1.hour) do |request|
    if request.path == "/links" && request.post?
      # Extract user ID from session or API token
      user_id = extract_user_id(request)
      "user:#{user_id}" if user_id
    end
  end

  # Throttle link creation (unauthenticated/by IP)
  # Allow 10 link creations per hour per IP for unauthenticated users
  throttle("links/ip", limit: 10, period: 1.hour) do |request|
    if request.path == "/links" && request.post? && !extract_user_id(request)
      request.ip
    end
  end

  # Throttle API requests by token
  # Allow 1000 API requests per hour per token
  throttle("api/token", limit: 1000, period: 1.hour) do |request|
    if request.path.start_with?("/api/")
      token = request.env["HTTP_AUTHORIZATION"]&.gsub(/^Bearer\s+/, "")
      "api_token:#{token}" if token
    end
  end

  # Throttle API requests by IP (for requests without token)
  # Allow 100 API requests per hour per IP
  throttle("api/ip", limit: 100, period: 1.hour) do |request|
    if request.path.start_with?("/api/")
      token = request.env["HTTP_AUTHORIZATION"]&.gsub(/^Bearer\s+/, "")
      request.ip unless token
    end
  end

  # Throttle redirect requests (short link clicks)
  # Allow 1000 redirects per 5 minutes per IP to prevent scraping
  throttle("redirects/ip", limit: 1000, period: 5.minutes) do |request|
    if request.get? && request.path.match?(/\A\/[a-zA-Z0-9\-_]{6}\z/)
      request.ip
    end
  end

  # Throttle bulk import requests
  # Allow 5 bulk imports per hour per user
  throttle("bulk_imports/user", limit: 5, period: 1.hour) do |request|
    if request.path == "/bulk_imports" && request.post?
      user_id = extract_user_id(request)
      "bulk_import:#{user_id}" if user_id
    end
  end

  ### Blocklist ###

  # Block requests from known bad IPs
  blocklist("block bad ips") do |request|
    # Add known malicious IPs to this array
    # ['*******', '*******'].include?(request.ip)

    # Block common scanner user agents
    request.user_agent&.match?(/(?i)bot|crawler|spider|scraper|scanner/) &&
      !request.user_agent&.match?(/(?i)googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|linkedinbot|whatsapp|telegram/)
  end

  # Block requests with suspicious patterns
  blocklist("block suspicious requests") do |request|
    # Block requests with very long paths (potential buffer overflow attempts)
    request.path.length > 255 ||
    # Block requests with suspicious query parameters
    request.query_string&.match?(/(?i)(union|select|insert|delete|drop|exec|script|javascript|vbscript|onload|onclick)/i) ||
    # Block requests with suspicious headers
    request.env["HTTP_USER_AGENT"]&.length.to_i > 500
  end

  ### Safelist ###

  # Always allow requests from localhost (for health checks, etc.)
  safelist("allow localhost") do |request|
    request.ip == "127.0.0.1" || request.ip == "::1"
  end

  # Allow health check endpoints
  safelist("allow health checks") do |request|
    request.path == "/up" || request.path == "/health"
  end

  ### Custom Response ###

  # Customize the response for throttled requests
  self.throttled_responder = lambda do |env|
    retry_after = (env["rack.attack.match_data"] || {})[:period]

    [
      429,
      {
        "Content-Type" => "application/json",
        "Retry-After" => retry_after.to_s,
        "X-RateLimit-Limit" => env["rack.attack.match_data"][:limit].to_s,
        "X-RateLimit-Remaining" => "0",
        "X-RateLimit-Reset" => (Time.now + retry_after).to_i.to_s
      },
      [ {
        error: "Rate limit exceeded",
        message: "Too many requests. Please try again later.",
        retry_after: retry_after
      }.to_json ]
    ]
  end

  # Customize the response for blocked requests
  self.blocklisted_responder = lambda do |env|
    [
      403,
      { "Content-Type" => "application/json" },
      [ {
        error: "Forbidden",
        message: "Your request has been blocked."
      }.to_json ]
    ]
  end

  ### Helper Methods ###

  private

  def self.extract_user_id(request)
    # Try to extract user ID from session
    if request.session && request.session["warden.user.user.key"]
      return request.session["warden.user.user.key"][0][0]
    end

    # Try to extract user ID from API token in Authorization header
    token = request.env["HTTP_AUTHORIZATION"]&.gsub(/^Bearer\s+/, "")
    if token
      api_token = ApiToken.active.find_by(token: token)
      return api_token&.user_id
    end

    nil
  end
end

### Logging & Monitoring ###

# Log blocked and throttled requests
ActiveSupport::Notifications.subscribe("rack.attack") do |name, start, finish, request_id, payload|
  request = payload[:request]

  case payload[:request].env["rack.attack.match_type"]
  when :throttle
    Rails.logger.warn "[Rack::Attack] Throttled request: #{request.ip} - #{request.path} - #{payload[:request].env['rack.attack.matched']}"
  when :blocklist
    Rails.logger.warn "[Rack::Attack] Blocked request: #{request.ip} - #{request.path} - #{payload[:request].env['rack.attack.matched']}"
  end
end

# Optional: Send metrics to monitoring service
if defined?(StatsD)
  ActiveSupport::Notifications.subscribe("rack.attack") do |name, start, finish, request_id, payload|
    case payload[:request].env["rack.attack.match_type"]
    when :throttle
      StatsD.increment("linkmaster.rack_attack.throttled")
    when :blocklist
      StatsD.increment("linkmaster.rack_attack.blocked")
    end
  end
end
