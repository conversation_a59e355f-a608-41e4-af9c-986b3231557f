# frozen_string_literal: true

# Docker build detection
# This initializer helps identify when the application is being built in Docker
# to prevent access to environment variables or credentials during the build process

if Rails.configuration.docker_build
  Rails.logger.info "Running in Docker build mode - skipping environment-dependent initializations" if Rails.logger

  # Skip any initializations that require:
  # - Database connections
  # - External service connections
  # - Environment variables or credentials
  # - File system writes outside of the app directory

  # This prevents warnings during Docker image builds about accessing
  # environment variables or Rails credentials during asset precompilation
end
