# Knowledge Base Seed Data
puts "Seeding Knowledge Base..."

# Create Categories
categories_data = [
  {
    name: "Getting Started",
    slug: "getting-started",
    description: "Essential guides for new users to get up and running quickly",
    icon: "🚀",
    position: 1,
    active: true
  },
  {
    name: "Link Management",
    slug: "link-management",
    description: "Creating, editing, and organizing your shortened links",
    icon: "🔗",
    position: 2,
    active: true
  },
  {
    name: "Analytics & Reports",
    slug: "analytics-reports",
    description: "Understanding your link performance and analytics data",
    icon: "📊",
    position: 3,
    active: true
  },
  {
    name: "Custom Domains",
    slug: "custom-domains",
    description: "Setting up and managing your custom branded domains",
    icon: "🌐",
    position: 4,
    active: true
  },
  {
    name: "API & Integrations",
    slug: "api-integrations",
    description: "Programmatic access and third-party integrations",
    icon: "⚙️",
    position: 5,
    active: true
  },
  {
    name: "Teams & Collaboration",
    slug: "teams-collaboration",
    description: "Working with teams and managing user permissions",
    icon: "👥",
    position: 6,
    active: true
  },
  {
    name: "Account & Billing",
    slug: "account-billing",
    description: "Account settings, subscription management, and billing",
    icon: "💳",
    position: 7,
    active: true
  },
  {
    name: "Troubleshooting",
    slug: "troubleshooting",
    description: "Common issues and their solutions",
    icon: "🔧",
    position: 8,
    active: true
  }
]

categories = categories_data.map do |data|
  KnowledgeCategory.find_or_create_by(slug: data[:slug]) do |cat|
    cat.assign_attributes(data)
  end
end

# Create Articles
articles_data = [
  # Getting Started
  {
    category_slug: "getting-started",
    title: "Welcome to Linklysis",
    content: <<~CONTENT,
      Welcome to Linklysis, your professional link shortening and analytics platform!

      ## What is Linklysis?
      Linklysis helps you create short, branded links that are easy to share and track. Whether you're sharing links on social media, email campaigns, or printed materials, Linklysis provides powerful analytics to understand your audience.

      ## Key Features
      - **Link Shortening**: Convert long URLs into short, memorable links
      - **Custom Domains**: Use your own domain for branded links
      - **Detailed Analytics**: Track clicks, locations, devices, and more
      - **Team Collaboration**: Work together with team members
      - **API Access**: Integrate with your existing tools and workflows

      ## Getting Started
      1. Create your first link by clicking "Create Link" in the dashboard
      2. Paste your long URL and customize your short link
      3. Share your link and watch the analytics roll in!

      Need help? Don't hesitate to ask me anything!
    CONTENT
    keywords: "welcome introduction overview features getting started tutorial",
    position: 1,
    featured: true,
    published: true
  },
  {
    category_slug: "getting-started",
    title: "Creating Your First Link",
    content: <<~CONTENT,
      Learn how to create your first shortened link in just a few clicks.

      ## Step-by-Step Guide

      ### 1. Access the Link Creator
      - Click the **"Create Link"** button in your dashboard
      - Or use the quick create form on the main dashboard

      ### 2. Enter Your URL
      - Paste the long URL you want to shorten
      - Make sure it includes `http://` or `https://`

      ### 3. Customize Your Link (Optional)
      - Choose a custom short code or let us generate one
      - Add a title for easy identification
      - Set an expiration date if needed

      ### 4. Advanced Options
      - Add UTM parameters for campaign tracking
      - Set up password protection
      - Configure geographic targeting

      ### 5. Create and Share
      - Click "Create Link" to generate your short URL
      - Copy the link and share it anywhere
      - Monitor performance in your analytics dashboard

      ## Tips for Better Links
      - Use descriptive custom codes when possible
      - Add meaningful titles for organization
      - Consider your audience when choosing link formats
    CONTENT
    keywords: "create link first time tutorial shortening URL custom",
    position: 2,
    featured: true,
    published: true
  },

  # Link Management
  {
    category_slug: "link-management",
    title: "How to Create a Short Link",
    content: <<~CONTENT,
      Creating short links with Linklysis is simple and powerful. Here's everything you need to know.

      ## Basic Link Creation

      ### Quick Create
      Use the dashboard form for instant link creation:
      1. Paste your long URL
      2. Click "Shorten"
      3. Copy your new short link

      ### Advanced Options
      For more control over your links:
      - **Custom Short Code**: Choose your own memorable code
      - **Link Title**: Add a descriptive title for organization
      - **Tags**: Categorize links with custom tags
      - **Expiration**: Set automatic expiration dates
      - **Password Protection**: Secure sensitive links

      ## Link Customization

      ### Short Codes
      - Auto-generated: Random 6-character codes
      - Custom codes: Choose meaningful names (3-50 characters)
      - Reserved words: Some codes are not available

      ### Metadata
      - **Title**: Helps you identify links in your dashboard
      - **Description**: Optional detailed description
      - **Tags**: Organize links by campaign, project, or category

      ## Best Practices
      - Use consistent naming conventions for custom codes
      - Add descriptive titles for easy management
      - Leverage tags for organization and filtering
      - Consider link expiration for time-sensitive content
    CONTENT
    keywords: "create short link custom code title tags metadata organization",
    position: 1,
    published: true
  },
  {
    category_slug: "link-management",
    title: "Organizing Links with Tags",
    content: <<~CONTENT,
      Tags help you organize and find your links quickly. Learn how to use them effectively.

      ## What are Tags?
      Tags are custom labels you can assign to links for better organization and filtering.

      ## Adding Tags
      - Add tags when creating a new link
      - Edit existing links to add or modify tags
      - Use commas to separate multiple tags
      - Tags are case-insensitive

      ## Tag Best Practices
      - **Campaigns**: Use tags like "summer-sale", "newsletter-march"
      - **Platforms**: Tag by social platform: "twitter", "facebook", "linkedin"
      - **Content Type**: "blog-post", "product", "event", "announcement"
      - **Projects**: Organize by client or project name

      ## Using Tags for Analysis
      - Filter your link list by specific tags
      - View combined analytics for all links with a tag
      - Export data filtered by tags
      - Create reports grouped by tag categories

      ## Examples
      - E-commerce: "product", "sale", "category-shoes"
      - Marketing: "email-campaign", "social-media", "paid-ads"
      - Events: "webinar", "conference", "workshop"
      - Content: "blog", "whitepaper", "case-study"
    CONTENT
    keywords: "tags organize filter campaign project social media marketing",
    position: 2,
    published: true
  },

  # Analytics & Reports
  {
    category_slug: "analytics-reports",
    title: "Understanding Your Link Analytics",
    content: <<~CONTENT,
      Get the most out of your link data with comprehensive analytics insights.

      ## Analytics Overview
      Your analytics dashboard provides detailed insights into link performance:

      ### Key Metrics
      - **Total Clicks**: All-time click count
      - **Unique Clicks**: Individual users who clicked
      - **Click-through Rate**: Clicks vs. impressions (when available)
      - **Geographic Distribution**: Where your clicks come from
      - **Device Breakdown**: Desktop, mobile, tablet usage
      - **Referrer Sources**: Which platforms drive traffic

      ## Time-Based Analysis
      - **Real-time**: Live click tracking
      - **Hourly**: Peak activity hours
      - **Daily**: Day-by-day performance
      - **Weekly/Monthly**: Long-term trends

      ## Geographic Insights
      - Country-level click distribution
      - City-level data for major metros
      - Time zone considerations for global campaigns

      ## Device & Browser Data
      - Operating system breakdown
      - Browser preferences of your audience
      - Mobile vs. desktop usage patterns

      ## Using Analytics for Optimization
      - Identify peak engagement times
      - Understand your audience demographics
      - Optimize content for popular devices
      - Adjust posting schedules based on geographic data
    CONTENT
    keywords: "analytics metrics clicks geographic device browser insights optimization",
    position: 1,
    featured: true,
    published: true
  },

  # Custom Domains
  {
    category_slug: "custom-domains",
    title: "Setting Up Custom Domains",
    content: <<~CONTENT,
      Brand your short links with custom domains for professional appearance and better trust.

      ## Why Use Custom Domains?
      - **Brand Recognition**: Use your own domain for links
      - **Trust**: Users trust familiar domains more
      - **Professional Appearance**: Consistent branding across all links
      - **Analytics**: Separate tracking for different domains

      ## Domain Requirements
      - You must own the domain
      - DNS access for configuration
      - SSL certificate support (handled automatically)

      ## Setup Process

      ### 1. Add Your Domain
      - Go to Settings → Custom Domains
      - Click "Add Domain"
      - Enter your domain (e.g., go.yourcompany.com)

      ### 2. DNS Configuration
      Configure these DNS records with your domain provider:
      - **CNAME Record**: Point your domain to our servers
      - **TXT Record**: Verify domain ownership

      ### 3. Verification
      - Click "Verify Domain" after DNS setup
      - Wait for propagation (up to 24 hours)
      - Receive confirmation when ready

      ### 4. SSL Certificate
      - Automatic SSL certificate provisioning
      - Force HTTPS for all links
      - Certificate auto-renewal

      ## Using Custom Domains
      - Set a default domain for new links
      - Choose domain when creating links
      - Edit existing links to use custom domains
      - View analytics separated by domain

      ## Troubleshooting
      - DNS propagation can take up to 24 hours
      - Verify DNS records are correctly configured
      - Check domain ownership verification
      - Contact support for complex setups
    CONTENT
    keywords: "custom domain branding DNS SSL certificate CNAME setup verification",
    position: 1,
    featured: true,
    published: true
  },

  # API & Integrations
  {
    category_slug: "api-integrations",
    title: "Getting Started with the API",
    content: <<~CONTENT,
      Access Linklysis programmatically with our RESTful API for automation and integrations.

      ## API Overview
      The Linklysis API allows you to:
      - Create and manage links programmatically
      - Retrieve analytics data
      - Manage domains and settings
      - Integrate with your existing workflows

      ## Authentication
      Use API tokens for secure access:

      ### Getting Your API Token
      1. Go to Settings → API Tokens
      2. Click "Generate New Token"
      3. Set permissions and expiration
      4. Copy and store securely

      ### Using Your Token
      Include in headers:
      ```
      Authorization: Bearer YOUR_API_TOKEN
      Content-Type: application/json
      ```

      ## Basic Usage

      ### Create a Link
      ```bash
      curl -X POST https://api.linklysis.com/v1/links \\
        -H "Authorization: Bearer YOUR_TOKEN" \\
        -H "Content-Type: application/json" \\
        -d '{"url": "https://example.com", "title": "My Link"}'
      ```

      ### Get Link Analytics
      ```bash
      curl -X GET https://api.linklysis.com/v1/links/{id}/analytics \\
        -H "Authorization: Bearer YOUR_TOKEN"
      ```

      ## Rate Limits
      - **Free Plan**: 100 requests/hour
      - **Pro Plan**: 1,000 requests/hour
      - **Business Plan**: 10,000 requests/hour

      ## Webhooks
      Receive real-time notifications:
      - Link clicks
      - Analytics milestones
      - Domain verification status

      ## SDKs and Libraries
      Official libraries available for:
      - JavaScript/Node.js
      - Python
      - PHP
      - Ruby

      ## Common Use Cases
      - Automated link creation from CMS
      - Campaign tracking integration
      - Analytics data export
      - Bulk link management
    CONTENT
    keywords: "API token authentication REST webhook SDK integration automation",
    position: 1,
    published: true
  },

  # Teams & Collaboration
  {
    category_slug: "teams-collaboration",
    title: "Working with Teams",
    content: <<~CONTENT,
      Collaborate effectively with team members using Linklysis team features.

      ## Team Features Overview
      - **Member Management**: Invite and manage team members
      - **Role-Based Permissions**: Control access levels
      - **Shared Link Management**: Collaborative link organization
      - **Team Analytics**: Combined performance insights

      ## Setting Up Your Team

      ### Creating a Team
      1. Go to Settings → Team
      2. Click "Create Team"
      3. Enter team name and description
      4. Set team preferences

      ### Inviting Members
      1. Click "Invite Member"
      2. Enter email address
      3. Select role and permissions
      4. Send invitation

      ## User Roles

      ### Owner
      - Full access to all features
      - Billing and subscription management
      - Team member management
      - Cannot be removed from team

      ### Admin
      - Manage team members (except owner)
      - Access all team links and analytics
      - Configure team settings
      - Manage custom domains

      ### Member
      - Create and manage own links
      - View team analytics (limited)
      - Use team custom domains
      - Cannot manage other members

      ### Viewer
      - Read-only access to links
      - View analytics (limited)
      - Cannot create or edit links
      - Cannot access settings

      ## Team Link Management
      - **Shared Links**: Visible to all team members
      - **Personal Links**: Private to creator
      - **Link Ownership**: Transfer between members
      - **Bulk Operations**: Team-wide link management

      ## Team Analytics
      - Combined performance metrics
      - Team member contribution tracking
      - Collaborative reporting
      - Export team data

      ## Best Practices
      - Use descriptive team and project names
      - Assign appropriate roles based on responsibilities
      - Establish link naming conventions
      - Regular team performance reviews
    CONTENT
    keywords: "teams collaboration members roles permissions invite shared links",
    position: 1,
    published: true
  },

  # Troubleshooting
  {
    category_slug: "troubleshooting",
    title: "Common Issues and Solutions",
    content: <<~CONTENT,
      Quick solutions to the most common Linklysis issues.

      ## Link Creation Issues

      ### "Invalid URL" Error
      **Problem**: URL not accepted during link creation
      **Solutions**:
      - Ensure URL includes http:// or https://
      - Check for special characters or spaces
      - Verify the URL is accessible
      - Try wrapping URL in quotes

      ### "Custom Code Already Taken"
      **Problem**: Desired custom code is unavailable
      **Solutions**:
      - Try variations with numbers or hyphens
      - Check if you own the existing link
      - Use auto-generated codes as alternative

      ## Analytics Issues

      ### Missing or Delayed Analytics
      **Problem**: Click data not appearing
      **Solutions**:
      - Wait a few minutes for data processing
      - Check if JavaScript is blocked
      - Verify link is publicly accessible
      - Clear browser cache and try again

      ### Geographic Data Incorrect
      **Problem**: Location data seems wrong
      **Solutions**:
      - VPN usage can affect location detection
      - Corporate networks may show headquarters location
      - Mobile carriers can affect geographic accuracy

      ## Custom Domain Issues

      ### Domain Verification Failed
      **Problem**: Cannot verify domain ownership
      **Solutions**:
      - Double-check DNS record configuration
      - Wait for DNS propagation (up to 24 hours)
      - Ensure CNAME points to correct target
      - Verify TXT record for ownership

      ### SSL Certificate Issues
      **Problem**: HTTPS not working on custom domain
      **Solutions**:
      - Wait for automatic certificate provisioning
      - Check domain verification status
      - Ensure CNAME record is correct
      - Contact support for manual certificate issues

      ## API Issues

      ### Authentication Errors
      **Problem**: API requests returning 401 Unauthorized
      **Solutions**:
      - Verify API token is correct and active
      - Check token hasn't expired
      - Ensure Bearer prefix in Authorization header
      - Regenerate token if necessary

      ### Rate Limit Exceeded
      **Problem**: Getting 429 Too Many Requests
      **Solutions**:
      - Implement request backoff strategy
      - Upgrade plan for higher limits
      - Cache responses when possible
      - Batch requests efficiently

      ## Performance Issues

      ### Slow Link Redirects
      **Problem**: Links taking too long to redirect
      **Solutions**:
      - Check your internet connection
      - Verify target URL is responsive
      - Clear browser DNS cache
      - Try different browser or device

      ## Getting More Help
      If these solutions don't resolve your issue:
      - Check our status page for service issues
      - Search this knowledge base for specific topics
      - Contact support with detailed problem description
      - Include relevant error messages and screenshots
    CONTENT
    keywords: "troubleshooting problems solutions errors issues fix help support",
    position: 1,
    featured: true,
    published: true
  }
]

puts "Creating articles..."

articles_data.each_with_index do |article_data, index|
  category = categories.find { |c| c.slug == article_data[:category_slug] }
  next unless category

  article = KnowledgeArticle.find_or_create_by(
    knowledge_category: category,
    slug: article_data[:title].parameterize
  ) do |art|
    art.title = article_data[:title]
    art.content = article_data[:content]
    art.keywords = article_data[:keywords]
    art.position = article_data[:position] || index
    art.featured = article_data[:featured] || false
    art.published = article_data[:published] || false
    art.published_at = Time.current if art.published
  end

  puts "  ✓ #{article.title}"
end

puts "✅ Knowledge base seeded successfully!"
puts "Created #{KnowledgeCategory.count} categories and #{KnowledgeArticle.count} articles"
