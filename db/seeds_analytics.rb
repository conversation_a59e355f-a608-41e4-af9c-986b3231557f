# Create sample data for analytics testing
user = User.first

if user
  puts "Creating sample links and clicks for analytics..."

  # Create some links
  links = [
    { original_url: "https://github.com", short_code: "github1", metadata: { title: "GitHub", description: "Where the world builds software" } },
    { original_url: "https://rubyonrails.org", short_code: "rails1", metadata: { title: "Ruby on Rails", description: "Web development that doesn't hurt" } },
    { original_url: "https://stackoverflow.com", short_code: "stack1", metadata: { title: "Stack Overflow", description: "Where developers learn and share" } },
    { original_url: "https://dev.to", short_code: "devto1", metadata: { title: "DEV Community", description: "A constructive and inclusive social network" } },
    { original_url: "https://producthunt.com", short_code: "phunt1", metadata: { title: "Product Hunt", description: "The best new products in tech" } }
  ]

  created_links = []
  links.each do |link_data|
    link = user.links.create!(link_data)
    created_links << link
    puts "Created link: #{link.short_code}"
  end

  # Create clicks for each link with various patterns
  countries = [ 'US', 'GB', 'CA', 'DE', 'FR', 'JP', 'AU', 'BR', 'IN', 'MX' ]
  devices = [ 'Mobile', 'Desktop', 'Tablet' ]
  browsers = [ 'Chrome', 'Safari', 'Firefox', 'Edge', 'Opera' ]
  referrers = [ 'https://google.com', 'https://twitter.com', 'https://facebook.com', 'https://linkedin.com', 'Direct', nil ]

  created_links.each_with_index do |link, index|
    # Create 20-100 clicks per link over the past 30 days
    click_count = rand(20..100)

    click_count.times do
      clicked_at = rand(30.days.ago..Time.current)
      country = countries.sample
      device = devices.sample
      browser = browsers.sample
      referrer = referrers.sample

      LinkClick.create!(
        link: link,
        clicked_at: clicked_at,
        tracking_data: {
          ip_address: Faker::Internet.ip_v4_address,
          user_agent: "Mozilla/5.0 (#{device}) #{browser}",
          country_code: country,
          city: Faker::Address.city,
          region: Faker::Address.state,
          device_type: device,
          browser: browser,
          os: device == 'Mobile' ? 'iOS' : (device == 'Desktop' ? 'Windows' : 'Android'),
          bot: false
        },
        attribution_data: {
          referrer: referrer,
          utm_source: referrer ? referrer.split('//').last.split('/').first : nil,
          utm_medium: referrer ? 'referral' : 'direct'
        }
      )
    end

    puts "Created #{click_count} clicks for #{link.short_code}"
  end

  # Update link_clicks_count for each link
  created_links.each do |link|
    Link.reset_counters(link.id, :link_clicks)
  end

  puts "Sample data created successfully!"
  puts "Total links: #{Link.count}"
  puts "Total clicks: #{LinkClick.count}"
else
  puts "No user found. Please create a user first."
end
