class CreateKnowledgeArticles < ActiveRecord::Migration[8.0]
  def change
    create_table :knowledge_articles do |t|
      t.references :knowledge_category, null: false, foreign_key: true
      t.string :title
      t.string :slug
      t.text :content
      t.text :keywords
      t.integer :helpful_count
      t.integer :not_helpful_count
      t.integer :view_count
      t.integer :position
      t.boolean :featured
      t.boolean :published
      t.datetime :published_at

      t.timestamps
    end
  end
end
