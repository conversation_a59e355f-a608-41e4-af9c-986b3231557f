class ChangeSubscriptionPlanToEnum < ActiveRecord::Migration[8.0]
  def up
    # Add a temporary column for the enum values
    add_column :users, :subscription_plan_enum, :integer, default: 0, null: false

    # Convert existing string values to integers in the new column
    execute <<-SQL
      UPDATE users#{' '}
      SET subscription_plan_enum = CASE subscription_plan
        WHEN 'free' THEN 0
        WHEN 'professional' THEN 1
        WHEN 'business' THEN 2
        WHEN 'enterprise' THEN 3
        ELSE 0
      END
    SQL

    # Remove old column and rename new one
    remove_column :users, :subscription_plan
    rename_column :users, :subscription_plan_enum, :subscription_plan

    # Add index for performance
    add_index :users, :subscription_plan
  end

  def down
    # Remove index
    remove_index :users, :subscription_plan

    # Add temporary string column
    add_column :users, :subscription_plan_string, :string

    # Convert integer values back to strings
    execute <<-SQL
      UPDATE users#{' '}
      SET subscription_plan_string = CASE subscription_plan
        WHEN 0 THEN 'free'
        WHEN 1 THEN 'professional'
        WHEN 2 THEN 'business'
        WHEN 3 THEN 'enterprise'
        ELSE 'free'
      END
    SQL

    # Remove integer column and rename string column
    remove_column :users, :subscription_plan
    rename_column :users, :subscription_plan_string, :subscription_plan
  end
end
