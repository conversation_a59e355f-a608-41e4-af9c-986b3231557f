class AddPrivacyIndexesAndCleanupOptimizations < ActiveRecord::Migration[8.0]
  def change
    # Add indexes for efficient data retention cleanup
    add_index :link_clicks, :clicked_at, name: 'index_link_clicks_on_clicked_at_for_cleanup'
    add_index :bulk_imports, [ :created_at, :status ], name: 'index_bulk_imports_on_created_at_and_status_for_cleanup'

    # Add index for efficient JSONB queries on tracking data
    # This helps with analytics queries that filter by country, device type, etc.
    add_index :link_clicks, "((tracking_data->>'country_code'))", name: 'index_link_clicks_on_country_code'
    add_index :link_clicks, "((tracking_data->>'device_type'))", name: 'index_link_clicks_on_device_type'
    add_index :link_clicks, "((tracking_data->>'bot'))", name: 'index_link_clicks_on_bot_flag'

    # Add index for unique visitor counting (anonymized IP analysis)
    add_index :link_clicks, "((tracking_data->>'ip_address'))", name: 'index_link_clicks_on_anonymized_ip'

    # Add privacy compliance tracking field
    add_column :link_clicks, :dnt_enabled, :boolean, default: false, null: false
    add_index :link_clicks, :dnt_enabled

    # Add data retention metadata
    add_column :link_clicks, :retention_expires_at, :datetime
    add_index :link_clicks, :retention_expires_at, name: 'index_link_clicks_on_retention_expires_at'
  end
end
