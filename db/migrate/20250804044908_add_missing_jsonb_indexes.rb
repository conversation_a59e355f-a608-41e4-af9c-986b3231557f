class AddMissingJsonbIndexes < ActiveRecord::Migration[8.0]
  def change
    # Add missing JSONB indexes for analytics queries
    # These indexes will significantly improve performance for JSONB field queries

    # Browser analytics - frequently queried in dashboard and link analytics
    add_index :link_clicks, "((tracking_data->>'browser'))",
              name: 'index_link_clicks_on_tracking_data_browser'

    # Operating system analytics - frequently queried in dashboard and link analytics
    add_index :link_clicks, "((tracking_data->>'os'))",
              name: 'index_link_clicks_on_tracking_data_os'

    # City analytics - used for geographic breakdowns
    add_index :link_clicks, "((tracking_data->>'city'))",
              name: 'index_link_clicks_on_tracking_data_city'

    # UTM campaign tracking - critical for marketing attribution
    add_index :link_clicks, "((attribution_data->>'utm_campaign'))",
              name: 'index_link_clicks_on_attribution_data_utm_campaign'

    # UTM medium tracking - also important for marketing attribution
    add_index :link_clicks, "((attribution_data->>'utm_medium'))",
              name: 'index_link_clicks_on_attribution_data_utm_medium'

    # Composite indexes with JSONB expressions require execute statements
    # Link analytics with date and country filtering
    execute <<-SQL
      CREATE INDEX index_link_clicks_on_link_date_country#{' '}
      ON link_clicks (link_id, clicked_at, ((tracking_data->>'country_code')));
    SQL

    # Device analytics with date range filtering
    execute <<-SQL
      CREATE INDEX index_link_clicks_on_date_device_type#{' '}
      ON link_clicks (clicked_at, ((tracking_data->>'device_type')));
    SQL

    # Clean up duplicate indexes (there are two country_code indexes)
    remove_index :link_clicks, name: 'index_link_clicks_on_country_code'
    remove_index :link_clicks, name: 'index_link_clicks_on_anonymized_ip'

    # Keep the more descriptive names
    # index_link_clicks_on_tracking_data_country_code (kept)
    # index_link_clicks_on_tracking_data_ip_address (kept)
  end
end
