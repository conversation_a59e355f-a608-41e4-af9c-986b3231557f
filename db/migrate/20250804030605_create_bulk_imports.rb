class CreateBulkImports < ActiveRecord::Migration[8.0]
  def change
    create_table :bulk_imports do |t|
      t.references :user, null: false, foreign_key: true
      t.references :team, foreign_key: true
      t.string :status, default: 'pending', null: false
      t.string :file_name
      t.integer :total_rows, default: 0
      t.integer :processed_rows, default: 0
      t.integer :successful_rows, default: 0
      t.integer :failed_rows, default: 0
      t.jsonb :error_details, default: {}
      t.datetime :started_at
      t.datetime :completed_at
      t.timestamps
    end

    add_index :bulk_imports, :status
    add_index :bulk_imports, [ :user_id, :created_at ]
  end
end
