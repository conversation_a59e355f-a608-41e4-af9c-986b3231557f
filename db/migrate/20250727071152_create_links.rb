class CreateLinks < ActiveRecord::Migration[8.0]
  def change
    create_table :links do |t|
      t.references :user, null: false, foreign_key: true
      t.references :team, foreign_key: true
      t.string :original_url, null: false, limit: 2048
      t.string :short_code, null: false, limit: 50
      t.jsonb :metadata, default: {}
      t.integer :link_clicks_count, default: 0, null: false
      t.datetime :archived_at
      t.datetime :expires_at

      t.timestamps
    end

    add_index :links, :short_code, unique: true
    add_index :links, [ :user_id, :created_at ]
    add_index :links, [ :team_id, :created_at ], where: "team_id IS NOT NULL"
    add_index :links, :archived_at, where: "archived_at IS NOT NULL"
    add_index :links, :expires_at, where: "expires_at IS NOT NULL"
    add_index :links, :metadata, using: :gin
  end
end
