class CreateLinkClicks < ActiveRecord::Migration[8.0]
  def change
    create_table :link_clicks do |t|
      t.references :link, null: false, foreign_key: true
      t.jsonb :attribution_data, default: {}, null: false
      t.jsonb :tracking_data, default: {}, null: false
      t.datetime :clicked_at, null: false

      t.timestamps
    end

    # Performance indexes
    add_index :link_clicks, [ :link_id, :clicked_at ]
    add_index :link_clicks, :clicked_at
    add_index :link_clicks, [ :link_id, :created_at ]

    # JSONB indexes for queries
    add_index :link_clicks, :attribution_data, using: :gin
    add_index :link_clicks, :tracking_data, using: :gin

    # Specific field indexes for common queries
    add_index :link_clicks, "(tracking_data->>'ip_address')"
    add_index :link_clicks, "(tracking_data->>'country_code')"
    add_index :link_clicks, "(attribution_data->>'utm_source')"
    add_index :link_clicks, "(attribution_data->>'referrer')"
  end
end
