# Linklysis 🔗

> **Professional Link Shortening & Analytics Platform**
> A modern, Rails 8-powered SaaS platform for link management, analytics, and team collaboration.

[![Rails](https://img.shields.io/badge/Rails-8.0-red.svg)](https://rubyonrails.org/)
[![Ruby](https://img.shields.io/badge/Ruby-3.4-red.svg)](https://www.ruby-lang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-16-blue.svg)](https://www.postgresql.org/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.0-blue.svg)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-Proprietary-yellow.svg)](#)

## 🚀 **Overview**

Linklysis is a production-ready link shortening platform designed to compete with services like Bitly, Rebrandly, and Dub.co. Built with Rails 8 and the Solid Trifecta (Solid Cache, Solid Queue, Solid Cable), it provides enterprise-grade link management without the complexity of Redis or Sidekiq.

### **Key Features**

- 🔗 **Smart Link Shortening** - Custom short codes with collision detection
- 📊 **Real-time Analytics** - Comprehensive click tracking and attribution
- 👥 **Team Collaboration** - Multi-user workspaces with role-based access
- 🌐 **Custom Domains** - White-label your short links
- 📱 **QR Code Generation** - Dynamic QR codes for every link
- 🔄 **Bulk Import/Export** - CSV-based bulk operations
- 🔌 **RESTful API** - Full-featured API with authentication
- 📈 **Advanced Analytics** - Geographic, device, and referrer tracking
- 🔒 **GDPR Compliant** - Privacy-first analytics with data retention policies
- ⚡ **Lightning Fast** - Sub-100ms redirects with optimized caching

## 🏗️ **Architecture**

### **Technology Stack**

- **Backend**: Ruby on Rails 8.0 with PostgreSQL 16
- **Frontend**: Hotwire (Turbo + Stimulus) with TailwindCSS
- **Caching**: Solid Cache (database-backed, no Redis)
- **Background Jobs**: Solid Queue (database-backed, no Sidekiq)
- **Real-time**: Solid Cable (WebSockets without Redis)
- **Authentication**: Devise with passwordless magic links
- **Deployment**: Kamal with Docker containers
- **Monitoring**: Built-in Rails instrumentation

### **Rails 8 Solid Trifecta**

Linklysis leverages Rails 8's revolutionary Solid Trifecta for a Redis-free architecture:

- **Solid Cache**: Database-backed caching for link metadata and analytics
- **Solid Queue**: Reliable background job processing for metadata extraction
- **Solid Cable**: WebSocket connections for real-time analytics updates

## 🎯 **Business Model**

| Plan | Price | Links/Month | Features |
|------|-------|-------------|----------|
| **Free** | $0 | 1,000 | Basic analytics, 30-day retention |
| **Professional** | $29 | 10,000 | API access, custom domains, 1-year retention |
| **Business** | $99 | 50,000 | Teams, webhooks, unlimited retention |
| **Enterprise** | Custom | Unlimited | SSO, dedicated support, SLA |

## 🚀 **Quick Start**

### **Prerequisites**

- Ruby 3.4+
- PostgreSQL 16+
- Node.js 18+ (for asset compilation)
- Docker (optional, for deployment)

### **Installation**

```bash
# Clone the repository
git clone https://github.com/kojjob/linklysis.git
cd linklysis

# Install dependencies
bundle install

# Setup database
bin/rails db:create db:migrate db:seed

# Install frontend dependencies and build assets
bin/rails tailwindcss:install
bin/rails assets:precompile

# Start the development server
bin/dev
```

### **Environment Variables**

Create a `.env` file in the root directory:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost/linkmaster_development

# Application
SECRET_KEY_BASE=your_secret_key_base
DEFAULT_HOST=http://localhost:3000

# Email (for magic links)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Optional: External services
GEOLOCATION_API_KEY=your_geolocation_api_key
```

### **Development**

```bash
# Start all services
bin/dev

# Run tests
bin/rails test

# Run system tests
bin/rails test:system

# Security audit
bundle exec brakeman

# Code quality
bundle exec rubocop
```

## 📊 **Core Features**

### **Link Management**

- **Smart Short Codes**: Collision-resistant 6-character codes
- **Custom Aliases**: User-defined short codes for branding
- **Bulk Operations**: CSV import/export for thousands of links
- **Link Expiration**: Time-based link expiration
- **Archive System**: Soft delete with restoration capabilities

### **Analytics & Tracking**

- **Real-time Metrics**: Live click tracking with WebSocket updates
- **Geographic Data**: Country, region, and city-level analytics
- **Device Detection**: Browser, OS, and device type tracking
- **Referrer Analysis**: Traffic source identification
- **Bot Filtering**: Automatic bot detection and filtering
- **Attribution Tracking**: Multi-touch attribution for campaigns

### **Team Collaboration**

- **Workspaces**: Isolated environments for teams
- **Role-based Access**: Owner, admin, member permissions
- **Shared Analytics**: Team-wide analytics dashboards
- **Bulk Management**: Team-level bulk operations

### **API & Integrations**

- **RESTful API**: Full CRUD operations with versioning
- **Webhook Support**: Real-time event notifications
- **Rate Limiting**: Configurable rate limits per plan
- **API Authentication**: Token-based authentication
- **Zapier Integration**: Connect with 5000+ apps

## 🔧 **Development**

### **Project Structure**

```text
linkmaster/
├── app/
│   ├── controllers/          # MVC controllers
│   │   ├── api/v1/          # API endpoints
│   │   └── settings/        # Settings management
│   ├── models/              # ActiveRecord models
│   ├── services/            # Business logic services
│   ├── jobs/                # Background jobs
│   ├── components/          # ViewComponents
│   └── views/               # ERB templates
├── config/
│   ├── initializers/        # Rails configuration
│   └── routes.rb           # Application routes
├── db/
│   ├── migrate/            # Database migrations
│   └── seeds.rb           # Sample data
├── spec/                   # RSpec test suite
└── docker/                # Docker configuration
```

### **Key Services**

- **LinkShorteningService**: Core link creation and management
- **AttributionTrackingService**: GDPR-compliant click tracking
- **AnalyticsService**: Metrics aggregation and reporting
- **MetadataExtractor**: URL metadata extraction
- **QrCodeService**: Dynamic QR code generation
- **BulkImportService**: CSV processing and validation

### **Testing**

```bash
# Run all tests
bin/rails test

# Run specific test files
bin/rails test test/models/link_test.rb

# Run system tests
bin/rails test:system

# Generate coverage report
COVERAGE=true bin/rails test
```

## 🚀 **Deployment**

### **Production Deployment with Kamal**

```bash
# Setup Kamal configuration
kamal setup

# Deploy to production
kamal deploy

# Check deployment status
kamal app logs

# Scale application
kamal app scale web=3
```

### **Docker Deployment**

```bash
# Build Docker image
docker build -t linkmaster .

# Run with Docker Compose
docker-compose up -d

# Scale services
docker-compose up --scale web=3
```

## 📈 **Performance**

### **Benchmarks**

- **Link Redirects**: < 100ms average response time
- **API Endpoints**: < 50ms average response time
- **Background Jobs**: < 5 seconds average processing time
- **Database Queries**: Optimized with proper indexing
- **Caching**: 95%+ cache hit rate for metadata

### **Optimization Features**

- **Database Indexing**: Optimized queries for high-traffic scenarios
- **Aggressive Caching**: Metadata, analytics, and user sessions
- **Background Processing**: Non-blocking operations
- **CDN Integration**: Static asset delivery optimization
- **Connection Pooling**: Efficient database connections

## 🔒 **Security & Privacy**

### **Security Features**

- **Rate Limiting**: Configurable limits with Rack::Attack
- **CSRF Protection**: Built-in Rails CSRF protection
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content Security Policy headers
- **HTTPS Enforcement**: SSL/TLS in production

### **GDPR Compliance**

- **Data Minimization**: Collect only necessary tracking data
- **Anonymization**: IP address hashing and anonymization
- **Data Retention**: Configurable retention policies
- **Right to Deletion**: User data deletion capabilities
- **Consent Management**: Opt-in tracking preferences

## 📚 **API Documentation**

### **Authentication**

```bash
# Get API token
curl -X POST https://linkmaster.io/api/v1/auth \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Use API token
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://linkmaster.io/api/v1/links
```

### **Core Endpoints**

```bash
# Create a short link
POST /api/v1/links
{
  "original_url": "https://example.com",
  "custom_short_code": "example"
}

# Get link analytics
GET /api/v1/links/:id/stats

# Bulk create links
POST /api/v1/links/bulk
{
  "urls": ["https://example1.com", "https://example2.com"]
}
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests for your changes
4. Commit your changes (`git commit -m 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

### **Development Guidelines**

- **Test-Driven Development**: Write tests before implementation
- **Service Objects**: Keep controllers thin, use service objects
- **ViewComponents**: Use ViewComponents over partials
- **Background Jobs**: Use Solid Queue for non-blocking operations
- **Caching**: Cache aggressively with Solid Cache

## 📄 **License**

This project is proprietary software. All rights reserved.

## 🆘 **Support**

- **Documentation**: [docs.linkmaster.io](https://docs.linkmaster.io)
- **API Reference**: [api.linkmaster.io](https://api.linkmaster.io)
- **Support Email**: [<EMAIL>](mailto:<EMAIL>)
- **Status Page**: [status.linkmaster.io](https://status.linkmaster.io)

---

## Built with ❤️ using Rails 8 and the Solid Trifecta
